package types

import (
	"time"
)

// Request defines the structure for a scan request.
type Request struct {
	Targets   []string `json:"Targets"`
	Templates []string `json:"Templates,omitempty"`
	Scanners  int      `json:"Scanners,omitempty"`
	Batches   int      `json:"Batches"`
	Threads   int      `json:"Threads"`
}

// Response is a generic API response structure.
type Response struct {
	RequestID string `json:"RequestId,omitempty"`
	Status    string `json:"status,omitempty"`
	Error     string `json:"error,omitempty"`
	Message   string `json:"message,omitempty"`
}

// PowControlRequest defines the structure for a PoW control request.
type PowControlRequest struct {
	Action string `json:"action"` // "start" or "stop"
}

// PowControlResponse is the response for a PoW control request.
type PowControlResponse struct {
	RequestID string `json:"request_id,omitempty"`
	Status    string `json:"status,omitempty"`
	Message   string `json:"message,omitempty"`
	Error     string `json:"error,omitempty"`
	Action    string `json:"action,omitempty"`
}

// GitHubDispatchPayload is the payload for a GitHub repository dispatch event.
type GitHubDispatchPayload struct {
	ClientPayload map[string]interface{} `json:"client_payload"`
	EventType     string                 `json:"event_type"`
}

// ScanRequest represents a scan request stored in DynamoDB.
type ScanRequest struct {
	CreatedAt              time.Time              `json:"created_at"`
	ResultsUpdatedAt       time.Time              `json:"results_updated_at,omitempty"`
	UpdatedAt              time.Time              `json:"updated_at"`
	ResultsSummary         *ResultsSummary        `json:"results_summary,omitempty"`
	VulnerableTargets      map[string][]string    `json:"vulnerable_targets,omitempty"`
	ResultsIntegrity       map[string]interface{} `json:"results_integrity,omitempty"`
	CompletedAt            *time.Time             `json:"completed_at,omitempty"`
	TargetsS3URL           string                 `json:"targets_s3_url"`
	RequestID              string                 `json:"request_id"`
	AggregatedFindingsKey  string                 `json:"aggregated_findings_key,omitempty"`
	Mode                   string                 `json:"mode"`
	ScanID                 string                 `json:"scan_id"`
	ResultsStatus          string                 `json:"results_status,omitempty"`
	Status                 string                 `json:"status"`
	Templates              []string               `json:"templates"`
	FindingsKeys           []string               `json:"findings_keys,omitempty"`
	ResultsBatchesReceived int                    `json:"results_batches_received"`
	ExpectedBatches        int                    `json:"expected_batches"`
	TTL                    int64                  `json:"ttl"`
	Threads                int                    `json:"threads"`
	Batches                int                    `json:"batches"`
	TargetCount            int                    `json:"target_count"`
	TotalVulnerableTargets int                    `json:"total_vulnerable_targets,omitempty"`
}

// ScanListResponse defines the structure for the list of scans response.
type ScanListResponse struct {
	Scans   []ScanRequest `json:"scans"`
	Total   int           `json:"total"`
	Limit   int           `json:"limit"`
	HasMore bool          `json:"has_more"`
}

// ScanStatusResponse defines the structure for scan status API response
type ScanStatusResponse struct {
	CreatedAt              time.Time           `json:"created_at"`
	UpdatedAt              time.Time           `json:"updated_at"`
	CompletedAt            *time.Time          `json:"completed_at,omitempty"`
	ResultsSummary         *ResultsSummary     `json:"results_summary,omitempty"`
	VulnerableTargets      map[string][]string `json:"vulnerable_targets,omitempty"`
	ScanID                 string              `json:"scan_id"`
	RequestID              string              `json:"request_id"`
	Status                 string              `json:"status"`
	Config                 ScanConfig          `json:"config"`
	TotalVulnerableTargets int                 `json:"total_vulnerable_targets,omitempty"`
}

// ScanConfig holds the configuration of a scan.
type ScanConfig struct {
	Output      string   `json:"output"`
	Mode        string   `json:"mode"`
	Templates   []string `json:"templates"`
	Targets     []string `json:"targets"`
	TargetCount int      `json:"target_count"`
	Batches     int      `json:"batches"`
	Threads     int      `json:"threads"`
	Scanners    int      `json:"scanners,omitempty"`
}

// ResultsSummary provides aggregated scan results
type ResultsSummary struct {
	TotalFindings     int   `json:"total_findings"`
	TargetsScanned    int   `json:"targets_scanned"`
	VulnerableTargets int   `json:"vulnerable_targets"`
	DurationSeconds   int64 `json:"duration_seconds"`
}

// ScanResultsResponse defines the structure for scan results API response
type ScanResultsResponse struct {
	CreatedAt       time.Time       `json:"created_at"`
	CompletedAt     *time.Time      `json:"completed_at,omitempty"`
	ResultsSummary  *ResultsSummary `json:"results_summary,omitempty"`
	ScanID          string          `json:"scan_id"`
	RequestID       string          `json:"request_id"`
	Status          string          `json:"status"`
	ResultsLocation string          `json:"results_location,omitempty"`
	Findings        []interface{}   `json:"findings"`
	Config          ScanConfig      `json:"config"`
}

// Template represents a single template's metadata.
type Template struct {
	Metadata    map[string]interface{} `json:"metadata"`
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Severity    string                 `json:"severity"`
	Author      []string               `json:"author"`
	Tags        []string               `json:"tags"`
	Reference   []string               `json:"reference"`
}

// TemplateIndex is the root structure of the templates.json file.
type TemplateIndex struct {
	Summary   TemplateSummary     `json:"summary"`
	Templates map[string]Template `json:"templates"`
	Version   string              `json:"version"`
}

// TemplateSummary provides a summary of the available templates.
type TemplateSummary struct {
	BySeverity map[string]int `json:"by_severity"`
	Total      int            `json:"total"`
}

// DashboardMetrics defines the structure for dashboard metrics response
type DashboardMetrics struct {
	SystemStatus   SystemStatus           `json:"system_status"`
	RecentScans    []DashboardScanSummary `json:"recent_scans"`
	RecentFindings []interface{}          `json:"recent_findings"`
	TotalScans     int                    `json:"total_scans"`
	ActiveScans    int                    `json:"active_scans"`
	TargetsScanned int                    `json:"targets_scanned"`
}

// DashboardScanSummary represents a simplified scan summary for dashboard display
type DashboardScanSummary struct {
	CreatedAt   time.Time  `json:"created_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	ScanID      string     `json:"scan_id"`
	RequestID   string     `json:"request_id"`
	Status      string     `json:"status"`
	TargetCount int        `json:"target_count"`
}

// SystemStatus represents the status of various system components
type SystemStatus struct {
	Nuclei string `json:"nuclei"`
	Queue  string `json:"queue"`
	API    string `json:"api"`
}

// VulnerableTargetResult represents a processed vulnerable target with its associated findings
type VulnerableTargetResult struct {
	Target       string   `json:"target"`
	TemplateIDs  []string `json:"template_ids"`
	FindingCount int      `json:"finding_count"`
}
