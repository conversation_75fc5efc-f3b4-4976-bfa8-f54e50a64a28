# CloudFront distribution for the frontend
# This provides global CDN capabilities with HTTPS, caching, and geographic distribution
# Only created when var.enable_cloudfront is true (for production deployments)
resource "aws_cloudfront_distribution" "frontend" {
  count = var.enable_cloudfront ? 1 : 0

  # Origin configuration - points to S3 bucket
  origin {
    domain_name              = aws_s3_bucket.frontend.bucket_regional_domain_name
    origin_id                = "S3-${aws_s3_bucket.frontend.id}"
    origin_access_control_id = aws_cloudfront_origin_access_control.frontend[0].id
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"
  comment             = "Frontend distribution for ${var.project_name} (${var.environment})"
  price_class         = var.cloudfront_price_class

  # Custom domain aliases (manual DNS management)
  aliases = var.frontend_domain_name != "" && var.use_custom_domain ? [var.frontend_domain_name] : []

  # Default cache behavior for all files
  default_cache_behavior {
    allowed_methods        = ["GET", "HEAD", "OPTIONS"]
    cached_methods         = ["GET", "HEAD", "OPTIONS"]
    target_origin_id       = "S3-${aws_s3_bucket.frontend.id}"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    # Use managed cache policy for optimized SPA caching
    cache_policy_id = data.aws_cloudfront_cache_policy.managed_caching_optimized.id

    # Use managed origin request policy
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_cors_s3_origin.id
  }

  # Special cache behavior for index.html (should not be cached for SPA routing)
  ordered_cache_behavior {
    path_pattern           = "index.html"
    allowed_methods        = ["GET", "HEAD", "OPTIONS"]
    cached_methods         = ["GET", "HEAD", "OPTIONS"]
    target_origin_id       = "S3-${aws_s3_bucket.frontend.id}"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    # Don't cache index.html to ensure users get the latest version
    cache_policy_id = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
  }

  # Cache behavior for static assets (JS, CSS, images) - long TTL
  ordered_cache_behavior {
    path_pattern           = "assets/*"
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "S3-${aws_s3_bucket.frontend.id}"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    # Cache assets for a long time since they are versioned by Vite
    cache_policy_id = data.aws_cloudfront_cache_policy.managed_caching_optimized_for_uncompressed_objects.id
  }

  # Handle SPA routing - redirect 403/404 errors to index.html
  # This is crucial for React Router to work properly
  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }

  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
  }

  # Geographic restrictions (none by default)
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  # SSL/TLS certificate configuration
  viewer_certificate {
    # Use default CloudFront certificate if no custom domain or not using custom domain
    cloudfront_default_certificate = var.frontend_domain_name == "" || !var.use_custom_domain

    # Use ACM certificate for custom domain (requires manual DNS validation)
    acm_certificate_arn      = var.frontend_domain_name != "" && var.use_custom_domain ? aws_acm_certificate.frontend[0].arn : null
    ssl_support_method       = var.frontend_domain_name != "" && var.use_custom_domain ? "sni-only" : null
    minimum_protocol_version = var.frontend_domain_name != "" && var.use_custom_domain ? "TLSv1.2_2021" : null
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-frontend-${var.environment}"
    Environment = var.environment
    Purpose     = "Frontend CDN Distribution"
  })


}

# Origin Access Control (OAC) - modern replacement for Origin Access Identity (OAI)
# This securely allows CloudFront to access the S3 bucket
resource "aws_cloudfront_origin_access_control" "frontend" {
  count = var.enable_cloudfront ? 1 : 0

  name                              = "${var.project_name}-frontend-${var.environment}-oac"
  description                       = "OAC for ${var.project_name} frontend S3 bucket"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

# Data sources for managed CloudFront cache policies
# Using AWS managed policies is recommended for better performance and maintenance
data "aws_cloudfront_cache_policy" "managed_caching_optimized" {
  name = "Managed-CachingOptimized"
}

data "aws_cloudfront_cache_policy" "managed_caching_disabled" {
  name = "Managed-CachingDisabled"
}

data "aws_cloudfront_cache_policy" "managed_caching_optimized_for_uncompressed_objects" {
  name = "Managed-CachingOptimizedForUncompressedObjects"
}

data "aws_cloudfront_origin_request_policy" "managed_cors_s3_origin" {
  name = "Managed-CORS-S3Origin"
}

# ACM certificate for CloudFront (must be in us-east-1 for CloudFront)
# Only created when using a custom domain with CloudFront
resource "aws_acm_certificate" "frontend" {
  count = var.frontend_domain_name != "" && var.enable_cloudfront ? 1 : 0

  provider          = aws.us_east_1
  domain_name       = var.frontend_domain_name
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
    ignore_changes = [validation_method]
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-frontend-${var.environment}"
    Environment = var.environment
    Purpose     = "Frontend SSL Certificate"
  })
}


