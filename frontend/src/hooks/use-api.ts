import { type DashboardMetricsResponse, scanApi } from "@/features/scans/services";
import { useRequest, useWatcher } from "alova/client";
import type { ScanConfig } from "../types";

// Choose the appropriate API implementation
const api = scanApi;

// Hook for initiating a scan
export const useInitiateScan = () => {
  return useRequest((config: ScanConfig) => api.initiateScan(config), {
    immediate: false,
  });
};

// Hook for getting scan status
export const useScanStatus = (scanId: string | undefined) => {
  return useWatcher(() => api.getScanStatus(scanId as string), [scanId], {
    immediate: !!scanId,
  });
};

// Hook for getting all scans with filtering
export const useScans = (options?: { status?: string; limit?: number }) => {
  const { data, loading, error } = useRequest(() => api.getAllScans(options), {
    initialData: {
      scans: [],
      total: 0,
      limit: options?.limit || 50,
      offset: 0,
      has_more: false,
    },
  });

  return { data, loading, error };
};

// Hook for dashboard metrics
export const useDashboardMetrics = () => {
  return useRequest(() => api.getDashboardMetrics(), {
    initialData: {
      total_scans: 0,
      active_scans: 0,
      targets_scanned: 0,
      recent_scans: [],
      recent_findings: [],
      system_status: {
        nuclei: "online",
        queue: "online",
        api: "online",
      },
    } as DashboardMetricsResponse,
  });
};
