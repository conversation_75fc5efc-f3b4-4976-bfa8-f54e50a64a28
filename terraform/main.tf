# ==============================================================================
# PROVIDER CONFIGURATIONS
# ==============================================================================

# Default AWS provider (uses the region from AWS CLI or environment variables)
# This is the primary provider for most resources
provider "aws" {
  region = var.aws_region != "" ? var.aws_region : "us-east-1"
}

# Additional AWS provider for us-east-1 region
# Required for CloudFront ACM certificates which must be in us-east-1
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

# ==============================================================================
# NUCLEI LAMBDA INFRASTRUCTURE
# ==============================================================================

module "nuclei_lambda" {
  source = "./nuclei_lambda"

  # Core configuration
  project_name = var.project_name
  environment  = var.environment

  # Nuclei configuration
  nuclei_version = var.nuclei_version
  nuclei_arch    = var.nuclei_arch
  nuclei_timeout = var.nuclei_timeout
  memory_size    = var.memory_size

  # Storage configuration
  custom_templates_path = "../nuclear_pond/templates/"
  nuclei_config_path    = "./config"

  # Monitoring configuration (environment-specific)
  log_retention_days = local.current_env_config.log_retention_days

  # Security configuration - pass nuclear pond task role ARN for Lambda permissions
  nuclear_pond_task_role_arn = module.nuclear_pond_backend.ecs_task_role_arn

  # VPC configuration for optional network isolation
  enable_vpc_isolation = var.enable_vpc_isolation
  vpc_id             = module.network.vpc_id
  private_subnet_ids = module.network.private_subnet_ids
  route_table_ids    = module.network.private_route_table_ids

  tags = local.compute_tags
}

###
# Shared Network Infrastructure
###
module "network" {
  source = "./network"

  project_name = var.project_name
  environment  = var.environment
  tags         = local.network_tags

  # Network configuration (single NAT Gateway for cost optimization)
  vpc_cidr                 = var.vpc_cidr
  public_subnet_az1_cidr   = var.public_subnet_az1_cidr
  public_subnet_az2_cidr   = var.public_subnet_az2_cidr
  private_subnet_az1_cidr  = var.private_subnet_az1_cidr
}

###
# Nuclear Pond Backend Infrastructure
###
module "nuclear_pond_backend" {
  source = "./backend"

  # Common parameters
  project_name = var.project_name
  environment  = var.environment
  tags         = local.compute_tags

  # Network configuration
  vpc_id             = module.network.vpc_id
  public_subnet_ids  = module.network.public_subnet_ids
  private_subnet_ids = module.network.private_subnet_ids

  # Authentication configuration
  jwt_secret    = var.jwt_secret
  demo_password = var.demo_password

  # Application configuration
  lambda_function_name   = module.nuclei_lambda.lambda_function_name
  lambda_function_arn    = module.nuclei_lambda.lambda_function_arn
  dynamodb_table_name    = aws_dynamodb_table.scan_state_table.name
  dynamodb_table_arn     = aws_dynamodb_table.scan_state_table.arn
  nuclei_templates_bucket     = module.nuclei_lambda.templates_bucket_name
  nuclei_templates_bucket_arn = module.nuclei_lambda.templates_bucket_arn

  # ECS configuration
  task_cpu                = var.nuclear_pond_task_cpu
  task_memory             = var.nuclear_pond_task_memory
  desired_count           = var.nuclear_pond_desired_count
  container_port          = var.nuclear_pond_container_port
  health_check_path       = var.nuclear_pond_health_check_path

  # Monitoring configuration
  log_retention_days      = var.nuclear_pond_log_retention_days

  # Load balancer configuration
  enable_deletion_protection = var.nuclear_pond_enable_deletion_protection

  # GitHub configuration
  github_token = var.github_token
  github_repository = var.github_repository
}

###
# Proof of Work (PoW) Target Infrastructure
###
module "pow_targets" {
  source = "./pow"
  count  = var.enable_pow_targets ? 1 : 0

  # Only create PoW resources if enabled
  enable_pow_targets = var.enable_pow_targets

  # Create a new hosted zone for the PoW targets if it doesn't exist
  pow_create_hosted_zone = var.pow_create_hosted_zone

  # Common parameters
  project_name = var.project_name
  environment  = var.environment
  tags         = local.compute_tags

  # Network configuration
  vpc_id             = module.network.vpc_id
  public_subnet_ids  = module.network.public_subnet_ids
  private_subnet_ids = module.network.private_subnet_ids

  # Domain configuration
  pow_domain_name = var.pow_domain_name
}

###
# Frontend Infrastructure
###
module "frontend" {
  source = "./frontend"

  # Provider configuration for ACM certificate (must be in us-east-1 for CloudFront)
  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # Common parameters
  project_name = var.project_name
  environment  = var.environment
  tags         = local.compute_tags

  # Domain configuration (manual DNS management)
  frontend_domain_name  = var.frontend_domain_name
  use_custom_domain     = var.use_custom_domain

  # CloudFront configuration
  enable_cloudfront     = var.enable_frontend_cloudfront
  cloudfront_price_class = var.frontend_cloudfront_price_class

  # API configuration - use CloudFront URL instead of ALB
  api_url = "https://${module.nuclear_pond_backend.cloudfront_domain_name}"
}
