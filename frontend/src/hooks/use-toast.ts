import type React from "react";
import { toast as sonnerToast } from "sonner";

// Simple toast types that work with <PERSON><PERSON>, extended with common variants
type ToastVariant = "default" | "destructive" | "success" | "warning" | "info" | "loading";

interface ToastAction {
  label: string;
  onClick: () => void;
}

interface ToastProps {
  title?: string;
  description?: string;
  variant?: ToastVariant;
  icon?: React.ReactNode;
  action?: ToastAction;
  cancel?: ToastAction;
  duration?: number;
  important?: boolean;
}

function toast({ title, description, variant = "default", icon, action, cancel, duration = 3000, important }: ToastProps) {
  const message = title || "";
  const options = { description, icon, action, cancel, duration, important };

  switch (variant) {
    case "destructive":
      return sonnerToast.error(message, options);
    case "success":
      return sonnerToast.success(message, options);
    case "warning":
      return sonnerToast.warning(message, options);
    case "info":
      return sonnerToast.info(message, options);
    case "loading":
      return sonnerToast.loading(message, options);
    default:
      return sonnerToast(message, options);
  }
}

function useToast() {
  return {
    toast,
    dismiss: sonnerToast.dismiss,
  };
}

export { toast, useToast };
export type { ToastProps, ToastVariant };
