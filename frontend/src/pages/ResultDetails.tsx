import { ExecutiveSummary } from "@/components/scan/ExecutiveSummary";
import { RealTimeDuration, ScanProgressDisplay, ScanStatusBadge } from "@/components/scan/ProgressIndicators";
import { useRealTimeMonitoring } from "@/hooks/use-real-time-monitoring";
import { triggerCacheInvalidation } from "@/utils/cache-invalidation";
import { toHumanString } from "@/utils/human-readable";
import { Link, useParams } from "@tanstack/react-router";
import { ArrowLeftIcon, ExternalLinkIcon, RefreshCwIcon } from "lucide-react";
import type React from "react";
import { useEffect } from "react";

// Connection status indicator component
const ConnectionStatusIndicator: React.FC<{
  connectionStatus: "connected" | "disconnected" | "error";
  lastUpdate: string | null;
}> = ({ connectionStatus, lastUpdate }) => {
  return (
    <div className="flex items-center gap-2 text-xs">
      {lastUpdate && connectionStatus === "connected" && <span className="text-base-content/50">Updated {new Date(lastUpdate).toLocaleTimeString()}</span>}
    </div>
  );
};

const ResultDetails: React.FC = () => {
  const { id: scanId } = useParams({ from: "/_authenticated/scans/$id" });

  const {
    scanStatus,
    isPolling,
    error: monitoringError,
    connectionStatus,
    lastUpdate,
    refetch: refetchStatus,
  } = useRealTimeMonitoring(scanId, {
    enabled: !!scanId,
  });

  // Trigger cache invalidation when scan status changes
  useEffect(() => {
    if (scanStatus?.status === "completed" || scanStatus?.status === "failed") {
      // Map scan status to cache invalidation event type
      const eventType = scanStatus.status === "completed" ? "scan_completed" : "scan_failed";
      triggerCacheInvalidation(eventType, scanId);
    }
  }, [scanStatus?.status, scanId]);

  // Calculate template count for progress estimation
  const templateCount = scanStatus?.config?.templates?.length || 10;

  // Determine loading and error states
  const statusLoading = !scanStatus && isPolling && !monitoringError;
  const statusError = monitoringError;

  // Render status badge using the new component
  const renderScanStatusBadge = (status: string) => {
    return <ScanStatusBadge status={status as "queued" | "running" | "completed" | "failed"} />;
  };

  if (statusError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/scans" className="btn btn-ghost btn-sm">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Results
          </Link>
        </div>

        <div className="bg-base-100 shadow rounded border border-base-300">
          <div className="p-8 text-center">
            <div className="mx-auto w-12 h-12 rounded-full bg-error/10 flex items-center justify-center mb-4">
              <ExternalLinkIcon className="h-6 w-6 text-error" />
            </div>
            <h3 className="text-lg font-medium">Scan not found</h3>
            <p className="text-sm text-base-content/70 mt-2">The scan with ID "{scanId}" could not be found or you don't have access to it.</p>
            <Link to="/scans" className="btn btn-primary mt-4">
              Back to Results
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex flex-col items-start gap-0">
          <Link to="/scans" className="btn btn-ghost btn-sm">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Results
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Scan Details</h1>
            <p className="text-base-content/70">{scanId ? `Scan ID: ${scanId}` : "Loading..."}</p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Connection Status Indicator */}
          <ConnectionStatusIndicator connectionStatus={connectionStatus} lastUpdate={lastUpdate} />

          {/* Manual refresh button - now as backup */}
          <button type="button" onClick={() => refetchStatus()} className="btn btn-outline btn-sm" title="Manual refresh">
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Scan Status Section */}
      <div className="bg-base-100 shadow rounded border border-base-300">
        <div className="p-6">
          <div className="flex items-center gap-2 text-xl font-semibold mb-2">
            Scan Information
            {statusLoading ? <div className="skeleton h-6 w-20" /> : scanStatus ? renderScanStatusBadge(scanStatus.status) : null}
          </div>

          {statusLoading ? (
            <div className="space-y-4">
              <div className="skeleton h-4 w-full" />
              <div className="skeleton h-4 w-3/4" />
              <div className="skeleton h-4 w-1/2" />
            </div>
          ) : scanStatus ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                {/* Scan Execution Details */}
                <div>
                  <div className="text-sm font-semibold text-base-content/80">Execution Details</div>
                  <div className="mt-1 text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Scanners Used:</span>
                      <span className="font-medium">{scanStatus.config?.threads || "N/A"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Target Batches:</span>
                      <span className="font-medium">{scanStatus.config?.batches || "N/A"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Targets:</span>
                      <span className="font-medium">{scanStatus.config?.target_count ? toHumanString(scanStatus.config?.target_count) : "N/A"}</span>
                    </div>
                    {/* Duration Display */}
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <div className="text-right">
                        <RealTimeDuration
                          startTime={scanStatus.created_at}
                          endTime={scanStatus.completed_at}
                          status={scanStatus.status as "queued" | "running" | "completed" | "failed"}
                          realTime={scanStatus.status === "running"}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="text-sm font-semibold text-base-content/80">Timeline</div>
                  <div className="mt-1 text-sm space-y-1">
                    <div>Created: {new Date(scanStatus.created_at).toLocaleString()}</div>
                    <div>Updated: {new Date(scanStatus.updated_at).toLocaleString()}</div>
                    {scanStatus.completed_at && <div>Completed: {new Date(scanStatus.completed_at).toLocaleString()}</div>}
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Results Section */}
      {scanStatus?.status === "completed" && (
        <div className="space-y-6">
          {/* Executive Summary - now works with just scanStatus */}
          <ExecutiveSummary scanStatus={scanStatus} showPerformanceMetrics={true} />
        </div>
      )}

      {/* Running/Queued State with Enhanced Progress */}
      {(scanStatus?.status === "running" || scanStatus?.status === "queued") && (
        <div className="bg-base-100 shadow rounded border border-base-300">
          <div className="p-8">
            <div className="text-center space-y-6">
              {/* Enhanced Progress Display */}
              <ScanProgressDisplay
                status={scanStatus.status}
                startTime={scanStatus.created_at}
                endTime={scanStatus.completed_at}
                targetCount={scanStatus.config?.target_count || 0}
                targetsScanned={undefined}
                vulnerabilitiesFound={undefined}
                templateCount={templateCount}
                showPhaseIndicator={true}
                showMetrics={true}
                showDuration={scanStatus.status === "running"}
              />
            </div>
          </div>
        </div>
      )}

      {/* Failed State */}
      {scanStatus?.status === "failed" && (
        <div className="bg-base-100 shadow rounded border border-base-300">
          <div className="p-8">
            <div className="text-center space-y-6">
              {/* Status Header */}
              <div>
                <h3 className="text-lg font-medium flex items-center justify-center gap-2 mb-2">
                  Scan Failed
                  {renderScanStatusBadge(scanStatus.status)}
                </h3>
              </div>

              <Link to="/scan" className="btn btn-primary">
                Start a New Scan
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultDetails;
