package apiclient

import (
	"bytes"
	"compress/gzip"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/yourusername/nuclei-ip-scanner/internal/models"
	"google.golang.org/protobuf/proto"
)

func TestHexToIPv4(t *testing.T) {
	tests := []struct {
		name        string
		hexStr      string
		expected    string
		expectError bool
	}{
		{
			name:        "Valid hex IP",
			hexStr:      "0x548d31ad",
			expected:    "*************",
			expectError: false,
		},
		{
			name:        "Valid hex IP with uppercase",
			hexStr:      "0x0A0A0A0A",
			expected:    "***********",
			expectError: false,
		},
		{
			name:        "Invalid hex format",
			hexStr:      "548d31ad",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Invalid hex characters",
			hexStr:      "0xinvalid",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Empty string",
			hexStr:      "",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Zero IP",
			hexStr:      "0x00000000",
			expected:    "0.0.0.0",
			expectError: false,
		},
		{
			name:        "Max IP",
			hexStr:      "0xFFFFFFFF",
			expected:    "***************",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := HexToIPv4(tt.hexStr)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for input %s, but got none", tt.hexStr)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for input %s: %v", tt.hexStr, err)
				}
				if result != tt.expected {
					t.Errorf("HexToIPv4(%s) = %s, want %s", tt.hexStr, result, tt.expected)
				}
			}
		})
	}
}

func TestFetchTargets(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Create a sample protobuf response
		target := &models.Target{
			Addr:  "0x548d31ad",
			Ports: []int32{80, 443, 8080},
		}

		response := &models.IPJobResponse{
			JobId:       12345,
			Priority:    1,
			Product:     "test-product",
			TargetCount: 1,
			Targets:     []*models.Target{target},
		}

		data, err := proto.Marshal(response)
		if err != nil {
			http.Error(w, "Failed to marshal response", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/x-protobuf")
		w.WriteHeader(http.StatusOK)
		w.Write(data)
	}))
	defer server.Close()

	targets, err := FetchTargets(server.URL)
	if err != nil {
		t.Fatalf("FetchTargets failed: %v", err)
	}

	if len(targets) != 1 {
		t.Errorf("Expected 1 target, got %d", len(targets))
	}

	if targets[0].Addr != "0x548d31ad" {
		t.Errorf("Expected addr '0x548d31ad', got '%s'", targets[0].Addr)
	}

	if len(targets[0].Ports) != 3 {
		t.Errorf("Expected 3 ports, got %d", len(targets[0].Ports))
	}
}

func TestFetchTargetsHTTPError(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}))
	defer server.Close()

	_, err := FetchTargets(server.URL)
	if err == nil {
		t.Error("Expected error for HTTP 500, but got none")
	}
}

func TestFetchTargetsInvalidProtobuf(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/x-protobuf")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("invalid protobuf data"))
	}))
	defer server.Close()

	_, err := FetchTargets(server.URL)
	if err == nil {
		t.Error("Expected error for invalid protobuf, but got none")
	}
}

func TestFetchTargetsNetworkError(t *testing.T) {
	_, err := FetchTargets("http://invalid-url-that-does-not-exist.com")
	if err == nil {
		t.Error("Expected error for invalid URL, but got none")
	}
}

func TestSaveTargets(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check headers
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("Expected Content-Type application/json, got %s", r.Header.Get("Content-Type"))
		}
		if r.Header.Get("Content-Encoding") != "gzip" {
			t.Errorf("Expected Content-Encoding gzip, got %s", r.Header.Get("Content-Encoding"))
		}
		if r.Header.Get("X-Filename") != "test.json.gz" {
			t.Errorf("Expected X-Filename test.json.gz, got %s", r.Header.Get("X-Filename"))
		}

		// Read and decompress the body
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read body", http.StatusBadRequest)
			return
		}

		// Decompress gzip data
		reader, err := gzip.NewReader(bytes.NewReader(body))
		if err != nil {
			http.Error(w, "Failed to create gzip reader", http.StatusBadRequest)
			return
		}
		defer reader.Close()

		decompressed, err := io.ReadAll(reader)
		if err != nil {
			http.Error(w, "Failed to decompress data", http.StatusBadRequest)
			return
		}

		// Check if the decompressed data matches expected
		expectedData := `{"test": "data"}`
		if string(decompressed) != expectedData {
			t.Errorf("Expected decompressed data '%s', got '%s'", expectedData, string(decompressed))
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("SUCCESS"))
	}))
	defer server.Close()

	testData := []byte(`{"test": "data"}`)
	result, err := SaveTargets(server.URL, testData, "test.json")
	if err != nil {
		t.Fatalf("SaveTargets failed: %v", err)
	}

	if result != "TARGETS SAVED SUCCESSFULLY" {
		t.Errorf("Expected 'TARGETS SAVED SUCCESSFULLY', got '%s'", result)
	}
}

func TestSaveTargetsHTTPError(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Error(w, "Bad Request", http.StatusBadRequest)
	}))
	defer server.Close()

	testData := []byte(`{"test": "data"}`)
	_, err := SaveTargets(server.URL, testData, "test.json")
	if err == nil {
		t.Error("Expected error for HTTP 400, but got none")
	}
}

func TestSaveTargetsNetworkError(t *testing.T) {
	testData := []byte(`{"test": "data"}`)
	_, err := SaveTargets("http://invalid-url-that-does-not-exist.com", testData, "test.json")
	if err == nil {
		t.Error("Expected error for invalid URL, but got none")
	}
}

func TestSaveTargetsEmptyData(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("SUCCESS"))
	}))
	defer server.Close()

	result, err := SaveTargets(server.URL, []byte{}, "empty.json")
	if err != nil {
		t.Fatalf("SaveTargets failed with empty data: %v", err)
	}

	if result != "TARGETS SAVED SUCCESSFULLY" {
		t.Errorf("Expected 'TARGETS SAVED SUCCESSFULLY', got '%s'", result)
	}
}

func TestSaveTargetsLargeData(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("SUCCESS"))
	}))
	defer server.Close()

	// Create large test data
	largeData := bytes.Repeat([]byte("a"), 1024*1024) // 1MB of data

	result, err := SaveTargets(server.URL, largeData, "large.json")
	if err != nil {
		t.Fatalf("SaveTargets failed with large data: %v", err)
	}

	if result != "TARGETS SAVED SUCCESSFULLY" {
		t.Errorf("Expected 'TARGETS SAVED SUCCESSFULLY', got '%s'", result)
	}
}
