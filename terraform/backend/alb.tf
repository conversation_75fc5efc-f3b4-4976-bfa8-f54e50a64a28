# ==============================================================================
# APPLICATION LOAD BALANCER FOR NUCLEAR POND BACKEND
# ==============================================================================

# Application Load Balancer
resource "aws_lb" "nuclear_pond_alb" {
  name               = "${var.project_name}-${var.environment}-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = var.public_subnet_ids

  enable_deletion_protection = var.enable_deletion_protection
  idle_timeout               = var.alb_idle_timeout

  # Enable access logs (optional - requires S3 bucket)
  # access_logs {
  #   bucket  = aws_s3_bucket.alb_logs.bucket
  #   prefix  = "nuclear-pond-alb"
  #   enabled = true
  # }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-alb"
    Environment = var.environment
    Component   = "backend"
  })
}

# Target Group for ECS Service
resource "aws_lb_target_group" "nuclear_pond_tg" {
  name        = "${var.project_name}-${var.environment}-tg"
  port        = var.container_port
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = var.vpc_id

  # Health check configuration
  health_check {
    enabled             = true
    healthy_threshold   = var.health_check_healthy_threshold
    unhealthy_threshold = var.health_check_unhealthy_threshold
    timeout             = var.health_check_timeout
    interval            = var.health_check_interval
    path                = var.health_check_path
    matcher             = "200"
    port                = "traffic-port"
    protocol            = "HTTP"
  }

  # Deregistration delay
  deregistration_delay = 30

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-tg"
    Environment = var.environment
    Component   = "backend"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# HTTP Listener (port 80)
resource "aws_lb_listener" "http_listener" {
  load_balancer_arn = aws_lb.nuclear_pond_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.nuclear_pond_tg.arn
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-http-listener"
    Environment = var.environment
    Component   = "backend"
  })
}

# CloudFront will handle HTTPS termination and forward HTTP to ALB
# Remove or comment out the HTTPS listener - CloudFront will handle HTTPS
# resource "aws_lb_listener" "nuclear_pond_https" {
#   ...
# }

# Keep the existing HTTP listener as-is, remove the new ones
# CloudFront will handle HTTPS termination and forward HTTP to ALB
# resource "aws_lb_listener" "nuclear_pond_http_redirect" {
#   load_balancer_arn = aws_lb.nuclear_pond_alb.arn
#   port              = "80"
#   protocol          = "HTTP"
#
#   default_action {
#     type = "redirect"
#     redirect {
#       port        = "443"
#       protocol    = "HTTPS"
#       status_code = "HTTP_301"
#     }
#   }
# }
