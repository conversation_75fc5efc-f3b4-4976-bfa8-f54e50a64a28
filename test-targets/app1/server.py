#!/usr/bin/env python3
"""
Simple HTTP server for test-app-1
Serves static content for nuclei scanning tests
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve static files and handle common endpoints"""
    
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Add some basic headers that might be useful for testing
        self.send_header('Server', 'TestApp1/1.0')
        self.send_header('X-Test-App', 'app1')
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        # Log the request
        print(f"GET {self.path} from {self.client_address[0]}")
        
        # Handle root path
        if self.path == '/':
            self.path = '/index.html'
        
        # Serve the file using the parent class
        super().do_GET()
    
    def do_POST(self):
        """Handle POST requests for testing"""
        print(f"POST {self.path} from {self.client_address[0]}")
        
        # Read the request body
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        # Simple response for POST requests
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {
            "status": "success",
            "app": "test-app-1",
            "method": "POST",
            "path": self.path,
            "received_data_length": len(post_data)
        }
        
        import json
        self.wfile.write(json.dumps(response).encode())

def main():
    PORT = 8000
    
    print(f"Starting Test App 1 HTTP Server on port {PORT}")
    print(f"Serving directory: {os.path.dirname(os.path.abspath(__file__))}")
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"Server running at http://0.0.0.0:{PORT}/")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()