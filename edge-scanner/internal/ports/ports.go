package ports

import (
	"fmt"
	"net"
	"time"
)

// ScanOpenPorts checks which of the given ports are open on the provided IP.
func ScanOpenPorts(ip string, portList []int) []int {
	open := []int{}
	for _, port := range portList {
		address := net.JoinHostPort(ip, fmt.Sprintf("%d", port))
		conn, err := net.DialTimeout("tcp", address, 2*time.Second)
		if err == nil {
			open = append(open, port)
			conn.Close()
		}
	}
	return open
}
