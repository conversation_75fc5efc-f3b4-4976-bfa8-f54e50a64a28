events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Default server (fallback)
    server {
        listen 80 default_server;
        server_name _;
        
        location / {
            return 404 "Test target not found";
        }
        
        location /health {
            return 200 "OK";
            add_header Content-Type text/plain;
        }
    }
    
    # fast-scan-demo-target.click -> test-app-1
    server {
        listen 80;
        server_name fast-scan-demo-target.click;
        
        location / {
            proxy_pass http://test-app-1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # Additional subdomains for testing
    server {
        listen 80;
        server_name app1.fast-scan-demo-target.click;
        
        location / {
            proxy_pass http://test-app-1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    server {
        listen 80;
        server_name app2.fast-scan-demo-target.click;
        
        location / {
            proxy_pass http://test-app-2:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # Vulnerable Apache 2.4.49 target
    server {
        listen 80;
        server_name vulnerable.fast-scan-demo-target.click;
        
        location / {
            proxy_pass http://vulnerable-apache:8002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # Wildcard subdomain support for scalability testing
    server {
        listen 80;
        server_name ~^(?<subdomain>.+)\.fast-scan-demo-target\.click$;
        
        # Route based on subdomain pattern
        location / {
            # Default to app1 for unknown subdomains
            set $target "test-app-1:8000";
            
            # Route app2-* subdomains to test-app-2
            if ($subdomain ~* "^app2") {
                set $target "test-app-2:8000";
            }
            
            # Route vulnerable-* subdomains to vulnerable-apache
            if ($subdomain ~* "^vulnerable") {
                set $target "vulnerable-apache:8002";
            }
            
            proxy_pass http://$target;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}