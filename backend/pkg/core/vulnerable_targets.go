package core

import (
	"fmt"
	"log"
	"nuclear_pond/pkg/types"
	"sort"
	"strings"
)

// VulnerableTargetProcessor handles processing of vulnerable targets for display
type VulnerableTargetProcessor struct {
	SeverityPriority map[string]int
	Targets          []string
	MaxTargets       int
}

// NewVulnerableTargetProcessor creates a new processor with default configuration
func NewVulnerableTargetProcessor(targets []string) *VulnerableTargetProcessor {
	return &VulnerableTargetProcessor{
		MaxTargets: 20,
		SeverityPriority: map[string]int{
			"critical":      5,
			"high":          4,
			"medium":        3,
			"low":           2,
			"info":          1,
			"informational": 1,
		},
		Targets: targets,
	}
}

// ProcessVulnerableTargets processes findings to extract vulnerable targets with their associated templates
// Returns a map of target -> template IDs and the total count of vulnerable targets
func (p *VulnerableTargetProcessor) ProcessVulnerableTargets(findings []map[string]any) (map[string][]string, int, error) {
	if len(findings) == 0 {
		return map[string][]string{}, 0, nil
	}

	// Group findings by original targets using prefix match
	targetFindings := make(map[string][]map[string]any)

	normalizedTargets := make([]string, len(p.Targets))
	for i, t := range p.Targets {
		normalizedTargets[i] = normalizeTarget(t)
	}

	for _, finding := range findings {
		fu := extractBestURL(finding)
		if fu == "" {
			continue
		}
		fuNorm := normalizeURLForMatch(fu)
		for idx, nt := range normalizedTargets {
			if strings.HasPrefix(fuNorm, nt) {
				key := p.Targets[idx]
				targetFindings[key] = append(targetFindings[key], finding)

				break
			}
		}
	}

	if len(targetFindings) == 0 {
		return map[string][]string{}, 0, nil
	}

	// Process each target to create VulnerableTargetResult
	var results []types.VulnerableTargetResult

	for target, targetFindingsList := range targetFindings {
		result, err := p.processTargetFindings(target, targetFindingsList)
		if err != nil {
			log.Printf("Error processing target %s: %v", target, err)

			continue
		}
		results = append(results, result)
	}

	totalVulnerableTargets := len(results)

	// Limit to MaxTargets for response
	if len(results) > p.MaxTargets {
		results = results[:p.MaxTargets]
	}

	// Convert to map format for API response
	vulnerableTargets := make(map[string][]string)
	for _, result := range results {
		vulnerableTargets[result.Target] = result.TemplateIDs
	}

	return vulnerableTargets, totalVulnerableTargets, nil
}

// processTargetFindings processes all findings for a single target
func (p *VulnerableTargetProcessor) processTargetFindings(target string, findings []map[string]any) (types.VulnerableTargetResult, error) {
	if len(findings) == 0 {
		return types.VulnerableTargetResult{}, fmt.Errorf("no findings provided for target %s", target)
	}

	templateIDs := make(map[string]bool)
	highestSeverityPriority := 0

	for _, finding := range findings {
		// Extract template ID
		templateID := extractTemplateIDFromFinding(finding)
		if templateID != "" {
			templateIDs[templateID] = true
		}

		// Determine highest severity
		severity := extractSeverityFromFinding(finding)
		severityPriority := p.getSeverityPriority(severity)

		if severityPriority > highestSeverityPriority {
			highestSeverityPriority = severityPriority
		}
	}

	// Convert template IDs map to slice
	templateIDSlice := make([]string, 0, len(templateIDs))
	for templateID := range templateIDs {
		templateIDSlice = append(templateIDSlice, templateID)
	}

	// Sort template IDs for consistent output
	sort.Strings(templateIDSlice)

	return types.VulnerableTargetResult{
		Target:       target,
		TemplateIDs:  templateIDSlice,
		FindingCount: len(findings),
	}, nil
}

// getSeverityPriority returns the priority value for a given severity level
func (p *VulnerableTargetProcessor) getSeverityPriority(severity string) int {
	if priority, exists := p.SeverityPriority[strings.ToLower(severity)]; exists {
		return priority
	}

	return 1 // Default to info priority
}

// extractTargetFromFinding extracts the target identifier from a finding
// This function mirrors the logic from outputs.go for consistency
func extractTargetFromFinding(finding map[string]any) string {
	// Try different possible target fields in order of preference
	if host, ok := finding["host"].(string); ok && host != "" {
		return host
	}
	if url, ok := finding["url"].(string); ok && url != "" {
		return url
	}
	if ip, ok := finding["ip"].(string); ok && ip != "" {
		return ip
	}
	if target, ok := finding["target"].(string); ok && target != "" {
		return target
	}

	return ""
}

// extractTemplateIDFromFinding extracts the template ID from a finding
func extractTemplateIDFromFinding(finding map[string]any) string {
	// Try template_id first (normalized format)
	if templateID, ok := finding["template_id"].(string); ok && templateID != "" {
		return templateID
	}

	// Try template-id (Nuclei's actual format)
	if templateID, ok := finding["template-id"].(string); ok && templateID != "" {
		return templateID
	}

	// Try templateID (original format)
	if templateID, ok := finding["templateID"].(string); ok && templateID != "" {
		return templateID
	}

	// Try extracting from nested info object
	if info, ok := finding["info"].(map[string]any); ok {
		if templateID, ok := info["id"].(string); ok && templateID != "" {
			return templateID
		}
	}

	// Try id field directly
	if id, ok := finding["id"].(string); ok && id != "" {
		return id
	}

	return ""
}

// extractSeverityFromFinding extracts the severity level from a finding
func extractSeverityFromFinding(finding map[string]any) string {
	// Try severity field directly
	if severity, ok := finding["severity"].(string); ok && severity != "" {
		return strings.ToLower(severity)
	}

	// Try extracting from nested info object
	if info, ok := finding["info"].(map[string]any); ok {
		if severity, ok := info["severity"].(string); ok && severity != "" {
			return strings.ToLower(severity)
		}
	}

	return "info" // Default severity
}

// ProcessVulnerableTargetsWithErrorHandling is a safe wrapper that handles panics and errors
func ProcessVulnerableTargetsWithErrorHandling(findings []map[string]any, targets []string) (map[string][]string, int) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Vulnerable target processing panicked: %v", r)
		}
	}()

	// We do not have targets here in this wrapper; keep function for compatibility
	processor := NewVulnerableTargetProcessor(targets)
	vulnerableTargets, totalCount, err := processor.ProcessVulnerableTargets(findings)
	if err != nil {
		log.Printf("Error processing vulnerable targets: %v", err)

		return map[string][]string{}, 0
	}

	return vulnerableTargets, totalCount
}

// Helpers for URL/target normalization (duplicated from outputs to avoid import cycles)
func extractBestURL(finding map[string]any) string {
	if s, ok := finding["matched-at"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["url"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["target"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["host"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["ip"].(string); ok && s != "" {
		return s
	}

	return ""
}

func normalizeTarget(t string) string {
	if t == "" {
		return ""
	}
	if !strings.HasSuffix(t, "/") {
		return t + "/"
	}

	return t
}

func normalizeURLForMatch(s string) string {
	if s == "" {
		return ""
	}
	if !strings.HasSuffix(s, "/") {
		return s + "/"
	}

	return s
}
