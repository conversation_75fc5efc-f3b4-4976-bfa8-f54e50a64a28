#!/usr/bin/env bash

# ==============================================================================
# FastScan Local Development Scan Workflow Verification
# ==============================================================================
# This script verifies that the scan workflow components work correctly in the
# local development environment, specifically:
# 1. S3 file uploads and downloads
# 2. DynamoDB scan state tracking

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

check_localstack() {
    log_header "Checking LocalStack Status"
    if ! curl -s "${LOCALSTACK_ENDPOINT:-http://localhost:4566}/_localstack/health" | grep -q "available"; then
        fail "LocalStack is not running. Please start it with: devbox run local:start"
    fi
    log_success "LocalStack is running"
}

load_environment() {
    log_header "Loading Environment Variables"
    if [ -f "${PROJECT_ROOT}/scripts/bin/load-local-env.sh" ]; then
        source "${PROJECT_ROOT}/scripts/bin/load-local-env.sh"
    else
        fail "Could not find load-local-env.sh"
    fi
    
    require_tool "aws"
    log_success "Environment variables loaded successfully"
}

test_s3_operations() {
    log_header "Testing S3 File Operations"
    
    local bucket_name="${S3_BUCKET}"
    local test_file="/tmp/fastscan-test-file.txt"
    local test_content="This is a test file for FastScan S3 operations $(date)"
    local test_key="test/fastscan-test-file.txt"
    
    echo "$test_content" > "$test_file"
    
    if ! awslocal s3 ls "s3://$bucket_name" &>/dev/null; then
        log_info "Creating S3 bucket: $bucket_name"
        awslocal s3 mb "s3://$bucket_name"
    fi
    
    log_info "Uploading test file to S3..."
    awslocal s3 cp "$test_file" "s3://$bucket_name/$test_key" || fail "Failed to upload file to S3"
    log_success "File uploaded successfully"
    
    log_info "Verifying file exists in S3..."
    awslocal s3 ls "s3://$bucket_name/$test_key" &>/dev/null || fail "File not found in S3 bucket"
    log_success "File exists in S3 bucket"
    
    log_info "Downloading file from S3..."
    local download_file="/tmp/fastscan-test-file-downloaded.txt"
    awslocal s3 cp "s3://$bucket_name/$test_key" "$download_file" || fail "Failed to download file from S3"
    log_success "File downloaded successfully"
    
    log_info "Verifying file content..."
    local downloaded_content
    downloaded_content=$(cat "$download_file")
    if [ "$downloaded_content" != "$test_content" ]; then
        fail "Downloaded file content does not match original"
    fi
    log_success "File content verified successfully"
    
    log_info "Cleaning up test files..."
    awslocal s3 rm "s3://$bucket_name/$test_key"
    rm -f "$test_file" "$download_file"
    
    log_success "S3 file operations test completed successfully"
}

test_dynamodb_operations() {
    log_header "Testing DynamoDB Scan State Tracking"
    
    local table_name="${DYNAMODB_TABLE}"
    local scan_id="test-scan-$(date +%s)"
    
    if ! awslocal dynamodb describe-table --table-name "$table_name" &>/dev/null; then
        log_info "Creating DynamoDB table: $table_name"
        awslocal dynamodb create-table \
            --table-name "$table_name" \
            --attribute-definitions AttributeName=scan_id,AttributeType=S \
            --key-schema AttributeName=scan_id,KeyType=HASH \
            --billing-mode PAY_PER_REQUEST
        
        log_info "Waiting for table to become active..."
        awslocal dynamodb wait table-exists --table-name "$table_name"
    fi
    
    log_info "Creating scan record in QUEUED state..."
    awslocal dynamodb put-item \
        --table-name "$table_name" \
        --item "{\"scan_id\": {\"S\": \"$scan_id\"},\"status\": {\"S\": \"QUEUED\"}}" || fail "Failed to create scan record"

    log_info "Verifying scan record exists..."
    local result
    result=$(awslocal dynamodb get-item --table-name "$table_name" --key "{\"scan_id\": {\"S\": \"$scan_id\"}}" --query 'Item.status.S' --output text)
    if [ "$result" != "QUEUED" ]; then
        fail "Failed to retrieve scan record or status is incorrect"
    fi
    log_success "Scan record created and verified successfully"
    
    log_info "Cleaning up test data..."
    awslocal dynamodb delete-item --table-name "$table_name" --key "{\"scan_id\": {\"S\": \"$scan_id\"}}"
    
    log_success "DynamoDB scan state tracking test completed successfully"
}

main() {
    log_header "FastScan Scan Workflow Verification"
    
    load_environment
    check_localstack
    
    test_s3_operations
    test_dynamodb_operations
    
    log_success "All scan workflow tests completed successfully!"
    log_info "The local development environment is correctly configured for scan workflows."
}

main "$@"