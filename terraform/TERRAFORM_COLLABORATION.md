# Terraform Collaboration Best Practices

This document outlines best practices for managing Terraform infrastructure across multiple developers, CI/CD pipelines, and environments.

## Table of Contents
- [Remote State Management](#remote-state-management)
- [State Locking](#state-locking)
- [Environment Separation](#environment-separation)
- [Workflow Patterns](#workflow-patterns)
- [Security & Access Control](#security--access-control)
- [Development Workflow](#development-workflow)
- [CI/CD Integration](#cicd-integration)
- [Troubleshooting](#troubleshooting)

## Remote State Management

### Why Remote State?
- **Collaboration**: Multiple developers can work on the same infrastructure
- **Consistency**: Single source of truth for infrastructure state
- **Security**: State files often contain sensitive information
- **Backup**: Cloud providers offer built-in backup and versioning

### Recommended Backend: AWS S3 + DynamoDB

```hcl
# terraform/backend.tf
terraform {
  backend "s3" {
    bucket         = "fastscan-terraform-state"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "fastscan-terraform-locks"
  }
}
```

### Setup Remote State Backend

```bash
# 1. Create S3 bucket for state storage
aws s3 mb s3://fastscan-terraform-state --region us-east-1

# 2. Enable versioning
aws s3api put-bucket-versioning \
  --bucket fastscan-terraform-state \
  --versioning-configuration Status=Enabled

# 3. Enable encryption
aws s3api put-bucket-encryption \
  --bucket fastscan-terraform-state \
  --server-side-encryption-configuration '{
    "Rules": [
      {
        "ApplyServerSideEncryptionByDefault": {
          "SSEAlgorithm": "AES256"
        }
      }
    ]
  }'

# 4. Create DynamoDB table for state locking
aws dynamodb create-table \
  --table-name fastscan-terraform-locks \
  --attribute-definitions AttributeName=LockID,AttributeType=S \
  --key-schema AttributeName=LockID,KeyType=HASH \
  --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5
```

## State Locking

State locking prevents multiple users from running Terraform simultaneously and corrupting the state.

### DynamoDB Locking Configuration
```hcl
terraform {
  backend "s3" {
    bucket         = "fastscan-terraform-state"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "fastscan-terraform-locks"  # This enables locking
  }
}
```

### Manual Lock Management
```bash
# Force unlock if needed (use with caution)
terraform force-unlock LOCK_ID

# Check current locks
aws dynamodb scan --table-name fastscan-terraform-locks
```

## Environment Separation

### Directory Structure
```
terraform/
├── environments/
│   ├── dev.tfvars
│   ├── staging.tfvars
│   └── prod.tfvars
├── modules/
│   ├── network/
│   ├── pow_targets/
│   └── lambda/
├── backend-configs/
│   ├── dev.hcl
│   ├── staging.hcl
│   └── prod.hcl
├── main.tf
├── variables.tf
└── outputs.tf
```

### Environment-Specific Backend Configs
```hcl
# backend-configs/dev.hcl
bucket         = "fastscan-terraform-state"
key            = "environments/dev/terraform.tfstate"
region         = "us-east-1"
encrypt        = true
dynamodb_table = "fastscan-terraform-locks"

# backend-configs/prod.hcl
bucket         = "fastscan-terraform-state-prod"
key            = "environments/prod/terraform.tfstate"
region         = "us-east-1"
encrypt        = true
dynamodb_table = "fastscan-terraform-locks-prod"
```

### Initialize with Environment-Specific Backend
```bash
# Initialize for dev environment
terraform init -backend-config=backend-configs/dev.hcl

# Initialize for production
terraform init -backend-config=backend-configs/prod.hcl
```

## Workflow Patterns

### 1. Centralized Workflow (Recommended for Small Teams)
- All Terraform operations run through CI/CD
- Developers create PRs with infrastructure changes
- CI/CD runs `terraform plan` on PRs
- CI/CD runs `terraform apply` on merge to main

### 2. Distributed Workflow
- Developers can run Terraform locally
- Shared remote state ensures consistency
- Requires strict coordination and communication

### 3. Hybrid Workflow (Our Current Approach)
- Critical environments (prod) managed via CI/CD only
- Development environments allow local execution
- Emergency changes can be made locally with proper approval

## Security & Access Control

### AWS IAM Policies for Terraform

#### Developer Policy (Limited Access)
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::fastscan-terraform-state/environments/dev/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:DeleteItem"
      ],
      "Resource": "arn:aws:dynamodb:us-east-1:*:table/fastscan-terraform-locks"
    }
  ]
}
```

#### CI/CD Policy (Full Access)
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "*",
      "Resource": "*"
    }
  ]
}
```

### Sensitive Variables Management
```bash
# Use environment variables for sensitive data
export TF_VAR_database_password="$(aws secretsmanager get-secret-value --secret-id prod/db/password --query SecretString --output text)"

# Or use AWS Secrets Manager data source
data "aws_secretsmanager_secret_version" "db_password" {
  secret_id = "prod/db/password"
}
```

## Development Workflow

### 1. Local Development Setup
```bash
# Clone repository
git clone <repository-url>
cd terraform

# Initialize with appropriate backend
terraform init -backend-config=backend-configs/dev.hcl

# Create workspace for your feature (optional)
terraform workspace new feature/cors-fix
terraform workspace select feature/cors-fix
```

### 2. Making Changes
```bash
# 1. Create feature branch
git checkout -b feature/add-cors-headers

# 2. Make infrastructure changes
# Edit .tf files

# 3. Plan changes
terraform plan -var-file=environments/dev.tfvars -out=plan.out

# 4. Review plan carefully
terraform show plan.out

# 5. Apply if safe (dev environment only)
terraform apply plan.out

# 6. Test changes
# Verify infrastructure works as expected

# 7. Commit and push
git add .
git commit -m "Add CORS headers to PoW targets"
git push origin feature/add-cors-headers
```

### 3. Code Review Process
```bash
# Create Pull Request with:
# - Description of changes
# - Terraform plan output
# - Testing results
# - Rollback plan if needed
```

## CI/CD Integration

### GitHub Actions Workflow Structure
```yaml
name: Terraform CI/CD

on:
  pull_request:
    paths: ['terraform/**']
  push:
    branches: [main]
    paths: ['terraform/**']

jobs:
  terraform-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.5.0
          
      - name: Terraform Format Check
        run: terraform fmt -check -recursive
        
      - name: Terraform Init
        run: terraform init -backend-config=backend-configs/dev.hcl
        
      - name: Terraform Validate
        run: terraform validate
        
      - name: Terraform Plan
        if: github.event_name == 'pull_request'
        run: |
          terraform plan -var-file=environments/dev.tfvars -no-color
          
      - name: Terraform Apply
        if: github.ref == 'refs/heads/main'
        run: |
          terraform apply -var-file=environments/dev.tfvars -auto-approve
```

### Environment Promotion Strategy
```bash
# 1. Changes tested in dev environment
# 2. PR merged to main triggers dev deployment
# 3. Manual approval required for staging
# 4. Manual approval + additional checks for prod
```

## Troubleshooting

### Common Issues and Solutions

#### 1. State Lock Issues
```bash
# Check for stuck locks
aws dynamodb scan --table-name fastscan-terraform-locks

# Force unlock (use carefully)
terraform force-unlock <LOCK_ID>
```

#### 2. State Drift
```bash
# Detect drift
terraform plan -detailed-exitcode

# Import existing resources
terraform import aws_instance.example i-1234567890abcdef0

# Refresh state
terraform refresh
```

#### 3. Backend Migration
```bash
# Migrate from local to remote state
terraform init -migrate-state

# Change backend configuration
terraform init -reconfigure
```

#### 4. Multiple Developers Conflict
```bash
# Always pull latest changes
git pull origin main

# Re-initialize if backend changed
terraform init

# Use workspaces for isolation
terraform workspace new developer-name
```

### Emergency Procedures

#### 1. Rollback Infrastructure
```bash
# Revert to previous state file version
aws s3 cp s3://fastscan-terraform-state/terraform.tfstate.backup terraform.tfstate

# Or use Terraform to destroy and recreate
terraform destroy -target=resource.name
terraform apply -target=resource.name
```

#### 2. State Recovery
```bash
# List state file versions
aws s3api list-object-versions --bucket fastscan-terraform-state --prefix terraform.tfstate

# Restore previous version
aws s3api get-object --bucket fastscan-terraform-state --key terraform.tfstate --version-id <VERSION_ID> terraform.tfstate
```

## Implementation Checklist

### Phase 1: Setup Remote State
- [ ] Create S3 bucket for state storage
- [ ] Create DynamoDB table for locking
- [ ] Configure backend in Terraform
- [ ] Migrate existing state to remote backend

### Phase 2: Environment Separation
- [ ] Create environment-specific tfvars files
- [ ] Create backend configs for each environment
- [ ] Set up separate state files per environment

### Phase 3: Access Control
- [ ] Create IAM policies for developers
- [ ] Create IAM policies for CI/CD
- [ ] Set up AWS credentials for team members

### Phase 4: CI/CD Integration
- [ ] Update GitHub Actions workflows
- [ ] Add terraform plan to PR checks
- [ ] Add terraform apply to main branch
- [ ] Set up approval processes for production

### Phase 5: Documentation & Training
- [ ] Document the new workflow
- [ ] Train team members on new processes
- [ ] Create troubleshooting guides

## Best Practices Summary

1. **Always use remote state** for team collaboration
2. **Enable state locking** to prevent conflicts
3. **Separate environments** with different state files
4. **Use consistent naming** conventions
5. **Plan before apply** - always review changes
6. **Use version control** for all Terraform code
7. **Implement proper access controls**
8. **Automate through CI/CD** for consistency
9. **Monitor and alert** on infrastructure changes
10. **Document everything** for team knowledge sharing

## Next Steps for FastScan Project

1. **Immediate**: Set up remote state backend
2. **Short-term**: Implement environment separation
3. **Medium-term**: Enhance CI/CD workflows
4. **Long-term**: Add monitoring and compliance checks

---

*This document should be updated as the team's Terraform practices evolve and mature.*