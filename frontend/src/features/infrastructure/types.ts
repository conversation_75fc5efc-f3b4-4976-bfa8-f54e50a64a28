/**
 * Request payload for PoW infrastructure control
 */
export interface PowControlRequest {
  action: "start" | "stop";
}

/**
 * Response from PoW infrastructure control endpoint
 */
export interface PowControlResponse {
  request_id?: string;
  status?: string;
  message?: string;
  error?: string;
  action?: "start" | "stop";
}

/**
 * Status of PoW infrastructure
 */
export interface PowStatus {
  status: "online" | "offline";
}

/**
 * PoW infrastructure configuration
 */
export interface PowConfig {
  domain: string;
  instanceType: string;
  instanceCount: number;
  region: string;
}

/**
 * Operation status for tracking GitHub Actions workflow
 */
export interface OperationStatus {
  requestId: string;
  status: "pending" | "running" | "completed" | "failed";
  message?: string;
  startedAt?: string;
  completedAt?: string;
}
