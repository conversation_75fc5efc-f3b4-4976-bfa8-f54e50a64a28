package security

import (
	"fmt"
	"net/url"
	"strings"
)

const (
	AllowedDemoDomain = "fast-scan-demo-target.click"
)

// ValidationResult holds the result of target validation
type ValidationResult struct {
	IsValid        bool
	InvalidTargets []string
	ValidTargets   []string
}

// IsValidDemoTarget validates if a target URL is a valid demo target
// Only allows subdomains of fast-scan-demo-target.click
func IsValidDemoTarget(target string) bool {
	parsedURL, err := url.Parse(target)
	if err != nil {
		return false
	}

	hostname := strings.ToLower(parsedURL.Hostname())

	// Allow exact match of the demo domain
	if hostname == AllowedDemoDomain {
		return true
	}

	// Allow subdomains of the demo domain
	if strings.HasSuffix(hostname, "."+AllowedDemoDomain) {
		return true
	}

	return false
}

// ValidateDemoTargets validates a list of targets to ensure all are demo targets
func ValidateDemoTargets(targets []string) ValidationResult {
	var invalidTargets []string
	var validTargets []string

	for _, target := range targets {
		if IsValidDemoTarget(target) {
			validTargets = append(validTargets, target)
		} else {
			invalidTargets = append(invalidTargets, target)
		}
	}

	return ValidationResult{
		IsValid:        len(invalidTargets) == 0,
		InvalidTargets: invalidTargets,
		ValidTargets:   validTargets,
	}
}

// ValidateTargetsSecurityError creates a security validation error
func ValidateTargetsSecurityError(invalidTargets []string) error {
	return fmt.Errorf("security violation: %d target(s) are not allowed. Only demo targets on %s domain are permitted. Invalid targets: %v",
		len(invalidTargets), AllowedDemoDomain, invalidTargets)
}