syntax = "proto3";

package job;

option go_package = "github.com/yourorg/evidence-core/internal/models;models";


message Target {
  string addr = 1;
  repeated int32 ports = 2;
}

message Probe {
  string template_id = 1;
  bytes params_blob = 2;

}

message IPJobResponse {
  int64 job_id = 1;
  int64 batch_id = 2;
  int32 priority = 3;
  string product = 4;
  int32 target_count = 5;
  repeated Target targets = 6;
  repeated Probe probes = 7;
  int32 soft_timeout_ms = 8;
  int32 hard_timeout_ms = 9;
  string auth_token = 10;
  string timestamp = 11;
}