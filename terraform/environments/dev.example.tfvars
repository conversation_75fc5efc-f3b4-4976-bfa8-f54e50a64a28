# ==============================================================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# ==============================================================================

# Core configuration
project_name = "fastscan"
environment = "dev"

# ==============================================================================
# GOVERNANCE AND TAGGING
# ==============================================================================

owner = "security-team"
cost_center = "security"
business_unit = "security"

# Development environment policies
backup_policy = "none"
monitoring_policy = "basic"
compliance_framework = "none"

# Resource discovery
enable_resource_discovery = true

# ==============================================================================
# INFRASTRUCTURE CONFIGURATION
# ==============================================================================

# Module enablement
enable_pow_targets = false

# Network configuration (smaller for dev)
vpc_cidr = "10.0.0.0/16"
public_subnet_az1_cidr = "10.0.1.0/24"
public_subnet_az2_cidr = "10.0.2.0/24"
private_subnet_az1_cidr = "10.0.3.0/24"

# NAT Gateway: Single instance for cost optimization (AZ1 only)

# Lambda configuration (optimized for dev)
nuclei_version = "3.1.7"
nuclei_arch = "linux_amd64"
nuclei_timeout = 300  # 5 minutes
memory_size = 512 

# ECS configuration (minimal for dev)
nuclear_pond_task_cpu = "256"
nuclear_pond_task_memory = "512"
nuclear_pond_desired_count = 1
nuclear_pond_container_port = 8082
nuclear_pond_health_check_path = "/health-check"
nuclear_pond_log_retention_days = 7
nuclear_pond_enable_deletion_protection = false

# Authentication configuration
jwt_secret = "your-secure-jwt-secret-here"
demo_password = "your-secure-demo-password-here"
enable_frontend_cloudfront = true
frontend_domain_name = "try.rootevidence.com"
frontend_cloudfront_price_class = "PriceClass_100"
use_custom_domain = false  # Set to false initially to deploy without domain, then true after DNS validation

# PoW targets configuration
pow_create_hosted_zone = true
pow_domain_name = "fast-scan-demo-target.click"

# GitHub token for PoW targets on demand deployment - SECURITY: Move to AWS Secrets Manager
github_token = "REPLACE_WITH_SECRETS_MANAGER_REFERENCE"

# GitHub repository for PoW targets on demand deployment
github_repository = "MoonlightLabs-dev/fastscan"

# Isolate Lambda function
enable_vpc_isolation = false

# ==============================================================================
# CUSTOM TAGS
# ==============================================================================

tags = {
  CreatedBy = "terraform"
  Purpose = "development-testing"
  AutoShutdown = "enabled"
  Team = "security-dev"
  Experimental = "true"
} 