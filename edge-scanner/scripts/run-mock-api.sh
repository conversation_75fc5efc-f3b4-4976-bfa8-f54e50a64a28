#!/bin/bash

set -e

echo "🎭 Starting Mock API Server for Edge Scanner"

# Check if we're in the edge-scanner directory
if [ ! -f "go.mod" ] || [ ! -d "cmd/scanner" ]; then
    echo "❌ Please run this script from the edge-scanner directory"
    exit 1
fi

# Check if Dock<PERSON> is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop existing mock API if running
echo "🛑 Stopping any existing mock API..."
docker-compose -f mock-api/docker-compose.mock.yml down 2>/dev/null || true

# Start mock API
echo "🚀 Starting mock API server..."
docker-compose -f mock-api/docker-compose.mock.yml up -d

# Wait for API to be ready
echo "⏳ Waiting for mock API to be ready..."
timeout=30
count=0
while [ $count -lt $timeout ]; do
    if curl -s http://localhost:8080/__admin/health &> /dev/null; then
        echo "✅ Mock API is ready!"
        break
    fi
    count=$((count + 1))
    sleep 1
done

if [ $count -eq $timeout ]; then
    echo "❌ Mock API failed to start within ${timeout} seconds"
    docker-compose -f mock-api/docker-compose.mock.yml logs
    exit 1
fi

echo ""
echo "🎉 Mock API server is running!"
echo "📋 Available endpoints:"
echo "  GET  http://localhost:8080/job/batch/next?queue=edge_scan_dev"
echo "  POST http://localhost:8080/job/{id}/batch/{batchId}/product/{product}/result"
echo ""
echo "🔍 Monitor logs with:"
echo "  docker-compose -f mock-api/docker-compose.mock.yml logs -f"
echo ""
echo "🛑 Stop with:"
echo "  docker-compose -f mock-api/docker-compose.mock.yml down"