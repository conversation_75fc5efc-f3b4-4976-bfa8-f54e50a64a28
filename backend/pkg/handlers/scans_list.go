package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/scan"
	"nuclear_pond/pkg/types"

	"github.com/go-chi/render"
)

// ScansListHandler lists recent scans with optional status filtering.
func ScansListHandler(w http.ResponseWriter, r *http.Request) {
	limit, statusFilter := parseListQuery(r)

	scansList, err := scan.GetAllScans(limit, nil, statusFilter)
	if err != nil {
		log.Printf("Error getting scans list: %v", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, types.Response{
			Error:   "Internal Server Error",
			Message: "Error retrieving scans: " + err.Error(),
		})

		return
	}

	render.JSON(w, r, scansList)
}
