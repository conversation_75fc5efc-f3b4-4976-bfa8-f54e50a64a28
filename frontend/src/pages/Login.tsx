import { EyeIcon, EyeOffIcon, LoaderIcon, UserIcon } from "lucide-react";
import type React from "react";
import { useRef, useState } from "react";
import Logo from "@/components/ui/Logo";
import { useLogin } from "@/features/auth/hooks";

const Login: React.FC = () => {
  const formRef = useRef<HTMLFormElement>(null);
  const [showPassword, setShowPassword] = useState(false);
  const { formData, errors, isLoading, handleEmailChange, handlePasswordChange, handleSubmit, isAuthenticated } = useLogin();

  if (isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-md lg:max-w-lg">
        {/* Login Form */}
        <div className="card bg-base-100 shadow-2xl">
          <div className="card-body">
            {/* Header Section */}
            <div className="">
              {/* Logo and Branding */}
              <Logo size="sm" />
            </div>

            <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div className="form-control">
                <label htmlFor="email" className="label">
                  <span className="label-text text-sm font-medium">Email Address</span>
                </label>
                <input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleEmailChange}
                  className={`input input-bordered w-full h-12 text-base ${errors.email ? "input-error" : ""}`}
                  disabled={isLoading}
                  autoComplete="email"
                />
                {errors.email && (
                  <div className="label">
                    <span className="label-text-alt text-error">{errors.email}</span>
                  </div>
                )}
              </div>

              {/* Password Field */}
              <div className="form-control">
                <div className="flex items-center justify-between">
                  <label htmlFor="password" className="label">
                    <span className="label-text text-sm font-medium">Password</span>
                  </label>
                </div>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handlePasswordChange}
                    className={`input input-bordered w-full h-12 text-base pr-10 ${errors.password ? "input-error" : ""}`}
                    disabled={isLoading}
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOffIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                  </button>
                </div>
                {errors.password && (
                  <div className="label">
                    <span className="label-text-alt text-error">{errors.password}</span>
                  </div>
                )}
              </div>

              <div className="form-control mt-6">
                <button type="submit" className="btn btn-primary w-full h-12 lg:h-14 text-base lg:text-lg font-medium" disabled={isLoading}>
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <LoaderIcon className="h-4 w-4 lg:h-5 lg:w-5 animate-spin" />
                      <span>Signing In...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4 lg:h-5 lg:w-5" />
                      <span>Sign In</span>
                    </div>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 space-y-2">
          <div className="flex items-center justify-center gap-4 text-xs text-base-content/70">
            <span>© 2025 Root Evidence</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
