#!/usr/bin/env python3
"""
Simple HTTP server for test-app-2
Serves static content for nuclei scanning tests with slightly different behavior
"""

import http.server
import socketserver
import os
import sys
import json
import time
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler with slightly different behavior than app1"""
    
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Different headers for app2
        self.send_header('Server', 'TestApp2/2.0')
        self.send_header('X-Test-App', 'app2')
        self.send_header('X-App-Version', '2.0.0')
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests with some additional endpoints"""
        # Log the request
        print(f"GET {self.path} from {self.client_address[0]}")
        
        # Handle special endpoints for app2
        if self.path == '/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            status_response = {
                "status": "healthy",
                "app": "test-app-2",
                "version": "2.0.0",
                "timestamp": int(time.time()),
                "uptime": "running"
            }
            self.wfile.write(json.dumps(status_response, indent=2).encode())
            return
        
        elif self.path == '/info':
            self.send_response(200)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            
            info_text = """Test Application 2
Version: 2.0.0
Purpose: Nuclei scanning target
Features: Static content serving, JSON API endpoints
"""
            self.wfile.write(info_text.encode())
            return
        
        # Handle root path
        if self.path == '/':
            self.path = '/index.html'
        
        # Serve the file using the parent class
        super().do_GET()
    
    def do_POST(self):
        """Handle POST requests with different response format"""
        print(f"POST {self.path} from {self.client_address[0]}")
        
        # Read the request body
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        # Different response format for app2
        self.send_response(201)  # Created instead of OK
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {
            "result": "created",
            "application": "test-app-2",
            "request": {
                "method": "POST",
                "endpoint": self.path,
                "data_size": len(post_data),
                "timestamp": int(time.time())
            },
            "server_info": {
                "version": "2.0.0",
                "type": "test-target"
            }
        }
        
        self.wfile.write(json.dumps(response, indent=2).encode())
    
    def do_PUT(self):
        """Handle PUT requests (additional method for app2)"""
        print(f"PUT {self.path} from {self.client_address[0]}")
        
        content_length = int(self.headers.get('Content-Length', 0))
        put_data = self.rfile.read(content_length)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {
            "status": "updated",
            "app": "test-app-2",
            "method": "PUT",
            "path": self.path,
            "data_received": len(put_data) > 0
        }
        
        self.wfile.write(json.dumps(response).encode())

def main():
    PORT = 8000
    
    print(f"Starting Test App 2 HTTP Server on port {PORT}")
    print(f"Serving directory: {os.path.dirname(os.path.abspath(__file__))}")
    print("Additional endpoints: /status, /info")
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"Server running at http://0.0.0.0:{PORT}/")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()