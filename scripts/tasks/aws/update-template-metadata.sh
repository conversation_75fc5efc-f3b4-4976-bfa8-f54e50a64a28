#!/usr/bin/env bash

# ==============================================================================
# Update Template Metadata in AWS Environment
# ==============================================================================
# This script updates the template metadata in a production AWS environment

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# Source common utilities if they exist
if [ -f "${PROJECT_ROOT}/scripts/lib/logging.sh" ]; then
  source "${PROJECT_ROOT}/scripts/lib/logging.sh"
else
  # Simple logging fallbacks if the library doesn't exist
  log_header() { echo -e "\n=== $1 ==="; }
  log_info() { echo -e "[INFO] $1"; }
  log_success() { echo -e "[SUCCESS] $1"; }
  log_warn() { echo -e "[WARNING] $1"; }
  fail() { echo -e "[ERROR] $1"; exit 1; }
fi

usage() {
  echo "Usage: $0 [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  --bucket NAME     Specify the S3 bucket name (required)"
  echo "  --profile NAME    AWS profile to use (optional)"
  echo "  --region NAME     AWS region to use (optional, default: us-east-1)"
  echo "  --help            Show this help message"
}

check_prerequisites() {
  log_header "Checking Prerequisites"
  
  # Check if AWS CLI is installed
  if ! command -v aws &> /dev/null; then
    fail "AWS CLI is not installed. Please install it to run this script."
  fi
  
  log_success "All prerequisites met"
}

run_metadata_generator() {
  log_header "Running Metadata Generator"
  
  local metadata_script="${PROJECT_ROOT}/scripts/tasks/metadata/generate-template-metadata.sh"
  
  if [ ! -f "$metadata_script" ]; then
    fail "Template metadata generator script not found: $metadata_script"
  fi
  
  local bucket_arg=""
  if [ -n "$BUCKET" ]; then
    bucket_arg="--bucket $BUCKET"
  fi
  
  log_info "Running template metadata generator..."
  if ! bash "$metadata_script" $bucket_arg; then
    fail "Template metadata generation failed"
  fi
  log_success "Template metadata generation completed"
}

main() {
  local BUCKET=""
  local PROFILE=""
  local REGION="us-east-1"
  
  while [[ $# -gt 0 ]]; do
    case $1 in
      --bucket) BUCKET="$2"; shift 2 ;;
      --profile) PROFILE="$2"; shift 2 ;;
      --region) REGION="$2"; shift 2 ;;
      --help) usage; exit 0 ;;
      *) fail "Unknown option: $1" ;;
    esac
  done
  
  if [ -z "$BUCKET" ]; then
    fail "Bucket name is required. Use --bucket option."
  fi
  
  # Set AWS environment variables
  export AWS_REGION="$REGION"
  if [ -n "$PROFILE" ]; then
    export AWS_PROFILE="$PROFILE"
  fi
  
  # Export bucket for the metadata generator
  export NUCLEI_TEMPLATES_BUCKET="$BUCKET"
  
  check_prerequisites
  run_metadata_generator
}

main "$@"