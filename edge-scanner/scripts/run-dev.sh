#!/bin/bash

set -e

echo "🔄 Starting Edge Scanner in Development Mode"

# Check if we're in the edge-scanner directory
if [ ! -f "go.mod" ] || [ ! -d "cmd/scanner" ]; then
    echo "❌ Please run this script from the edge-scanner directory"
    exit 1
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Run ./scripts/dev-setup.sh first"
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Set development defaults if not set
export DEBUG_MODE="${DEBUG_MODE:-true}"
export SCAN_INTERVAL_SECONDS="${SCAN_INTERVAL_SECONDS:-30}"
export WORKER_COUNT="${WORKER_COUNT:-5}"

echo "🔧 Configuration:"
echo "  API Endpoint: ${API_ENDPOINT}"
echo "  Debug Mode: ${DEBUG_MODE}"
echo "  Scan Interval: ${SCAN_INTERVAL_SECONDS} seconds"
echo "  Worker Count: ${WORKER_COUNT}"
echo ""

# Create results directory if it doesn't exist
mkdir -p results

# Run the scanner
echo "🚀 Starting scanner..."
go run ./cmd/scanner