FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Copy all files from the current directory to the container
COPY . .

# Make the server script executable
RUN chmod +x server.py

# Expose port 8000
EXPOSE 8000

# Health check using the /status endpoint specific to app2
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python3 -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/status')" || exit 1

# Run the server
CMD ["python3", "server.py"]