# Fast Scan AI Brand Guidelines

## Core Identity
**Mission**: Fast, reliable vulnerability scanning at scale
**Voice**: Technical, confident, direct, helpful

## Colors
```css
/* Primary */
--primary: #0066FF;      /* Lightning Blue - CTAs, links */
--dark: #0A0E27;         /* Deep Space - text, dark UI */
--white: #FFFFFF;        /* backgrounds */

/* Secondary */
--accent: #00D4FF;       /* Electric Cyan - highlights */
--gray: #64748B;         /* Storm Gray - secondary text */
--dark-secondary: #1E293B; /* Midnight Blue - dark backgrounds */

/* Semantic */
--success: #10B981;      /* green */
--warning: #F59E0B;      /* amber */
--danger: #EF4444;       /* red */
--info: #3B82F6;         /* blue */

/* Backgrounds */
--bg-light: #FFFFFF;
--bg-light-secondary: #F8FAFC;
--bg-dark: #0A0E27;
--bg-dark-secondary: #1E293B;
```

## Typography
```css
/* Font: Inter (body), Inter Mono (code) */
--font-display: 36px/1.2 600;   /* Main headers */
--font-h1: 30px/1.2 600;
--font-h2: 24px/1.2 600;
--font-h3: 20px/1.3 500;
--font-body: 14px/1.5 400;
--font-small: 12px/1.5 400;
--font-mono: 14px/1.5 "Inter Mono";
```

## Spacing & Layout
```css
/* Base unit: 4px */
--space: 4px 8px 16px 24px 32px 48px 64px;
--radius: 4px 8px 12px 9999px;
--container-width: 1440px;
--sidebar-width: 240px;
--header-height: 64px;
```

## Components

### Buttons
```css
.btn-primary { background: var(--primary); color: white; }
.btn-secondary { background: transparent; border: 1px solid var(--gray); }
.btn-danger { background: var(--danger); color: white; }
/* All: height: 40px; padding: 0 16px; radius: 4px; */
```

### Forms
```css
.input { 
  height: 40px; 
  padding: 8px 12px; 
  border: 1px solid #E2E8F0;
  border-radius: 4px;
}
.input:focus { border-color: var(--primary); }
```

### Cards
```css
.card {
  background: var(--bg-light);
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.07);
}
```

### Status States
- **Running**: Lightning icon (animated, blue)
- **Success**: ✓ (green)
- **Warning**: ⚠ (amber)
- **Critical**: ✕ (red)
- **Pending**: ⏱ (gray)

## Icons
- Style: 2px outlined, geometric
- Sizes: 16px, 20px, 24px
- Library: Lucide/Heroicons

## Responsive
```css
@mobile: max-width: 767px;
@tablet: 768px - 1023px;
@desktop: 1024px+;
```

## Animation
```css
--transition-fast: 200ms ease-in-out;  /* micro-interactions */
--transition-normal: 300ms ease-in-out; /* page transitions */
```

## Key Patterns

### Dashboard Layout
```
┌─────────────┬──────────────────────┐
│  Sidebar    │      Header (64px)   │
│  (240px)    ├──────────────────────┤
│             │                      │
│  Nav Items  │     Main Content     │
│             │                      │
└─────────────┴──────────────────────┘
```

### Data Tables
- Row height: 48px
- Alternating rows: subtle gray
- Sortable headers with icons
- Pagination bottom
- Bulk actions on selection

### Empty States
- Icon/illustration
- Clear message
- CTA button
- Professional tone

### Error Messages
- Specific problem description
- Actionable solution
- Plain language
- Error code for support

## Accessibility
- Contrast: 4.5:1 (text), 3:1 (large text)
- Focus: 2px solid outline
- Touch targets: min 44px
- Keyboard navigation
- Screen reader labels

## Dark Mode
- Auto-detect system preference
- Manual toggle
- Invert backgrounds, adjust shadows
- Maintain contrast ratios

## Quick Reference
- **Logo**: Lightning bolt icon
- **Primary action color**: #0066FF
- **Font stack**: Inter, system-ui, sans-serif
- **Base spacing**: 4px grid
- **Border radius**: 4px (small), 8px (medium)
- **Shadow**: 0 4px 6px rgba(0,0,0,0.07)
- **Max line length**: 75ch
- **Animation timing**: 200-300ms ease-in-out