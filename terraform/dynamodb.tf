resource "aws_dynamodb_table" "scan_state_table" {
  name         = local.naming.dynamodb_table
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "scan_id"
  
  attribute {
    name = "scan_id"
    type = "S"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  # Server-side encryption
  server_side_encryption {
    enabled = true
  }

  # Point-in-time recovery for production
  point_in_time_recovery {
    enabled = local.current_env_config.backup_required
  }

  tags = merge(local.storage_tags, {
    Name        = local.naming.dynamodb_table
    Component   = "state-management"
    Purpose     = "scan-state-tracking"
  })
}