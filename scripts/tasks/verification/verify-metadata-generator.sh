#!/usr/bin/env bash

# ==============================================================================
# Verify Metadata Generator
# ==============================================================================
# This script verifies that the metadata generator is working correctly

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# Source common utilities if they exist
if [ -f "${PROJECT_ROOT}/scripts/lib/logging.sh" ]; then
  source "${PROJECT_ROOT}/scripts/lib/logging.sh"
else
  # Simple logging fallbacks if the library doesn't exist
  log_header() { echo -e "\n=== $1 ==="; }
  log_info() { echo -e "[INFO] $1"; }
  log_success() { echo -e "[SUCCESS] $1"; }
  log_warn() { echo -e "[WARNING] $1"; }
  fail() { echo -e "[ERROR] $1"; exit 1; }
fi

check_localstack() {
  log_header "Checking LocalStack"
  
  if ! curl -s "http://localhost:4566/health" | grep -q "running" 2>/dev/null; then
    fail "LocalStack is not running. Please start it with 'devbox run localstack'"
  fi
  
  log_success "LocalStack is running"
}

verify_bucket_exists() {
  log_header "Verifying Template Bucket"
  
  # Try to get bucket name from environment
  local bucket="${NUCLEI_TEMPLATES_BUCKET:-}"
  
  if [ -z "$bucket" ]; then
    # Try to load from local env script if it exists
    if [ -f "${PROJECT_ROOT}/scripts/bin/load-local-env.sh" ]; then
      source "${PROJECT_ROOT}/scripts/bin/load-local-env.sh" >/dev/null 2>&1 || true
      bucket="${NUCLEI_TEMPLATES_BUCKET:-}"
    fi
  fi
  
  if [ -z "$bucket" ]; then
    log_warn "NUCLEI_TEMPLATES_BUCKET environment variable not set"
    log_info "Using default bucket name: nuclei-templates"
    bucket="nuclei-templates"
  fi
  
  log_info "Checking if bucket '$bucket' exists..."
  
  if ! awslocal s3api head-bucket --bucket "$bucket" >/dev/null 2>&1; then
    log_info "Bucket '$bucket' does not exist, creating it..."
    awslocal s3 mb "s3://$bucket"
  fi
  
  # Create a sample template for testing
  log_info "Creating sample template for testing..."
  
  local tmp_dir=$(mktemp -d)
  local sample_template="${tmp_dir}/sample-template.yaml"
  
  cat > "$sample_template" << EOF
id: sample-template
info:
  name: Sample Template
  author: ["Test User"]
  severity: medium
  description: This is a sample template for testing
  tags: ["test", "sample"]
EOF
  
  # Upload the sample template
  awslocal s3 cp "$sample_template" "s3://${bucket}/templates/sample-template.yaml"
  
  # Clean up
  rm -rf "$tmp_dir"
  
  export NUCLEI_TEMPLATES_BUCKET="$bucket"
  log_success "Template bucket verified and sample template uploaded"
}

run_metadata_generator() {
  log_header "Running Metadata Generator"
  
  local metadata_script="${PROJECT_ROOT}/scripts/tasks/metadata/generate-template-metadata.sh"
  
  if [ ! -f "$metadata_script" ]; then
    fail "Template metadata generator script not found: $metadata_script"
  fi
  
  log_info "Running template metadata generator..."
  if ! bash "$metadata_script" --local; then
    fail "Template metadata generation failed"
  fi
  
  log_success "Template metadata generation completed"
}

verify_output() {
  log_header "Verifying Output"
  
  local bucket="${NUCLEI_TEMPLATES_BUCKET}"
  
  # Check if templates.json exists
  log_info "Checking if templates.json exists in bucket..."
  
  local tmp_file=$(mktemp)
  
  if ! awslocal s3 cp "s3://${bucket}/templates.json" "$tmp_file" >/dev/null 2>&1; then
    fail "templates.json not found in bucket"
  fi
  
  # Verify JSON structure
  log_info "Verifying JSON structure..."
  
  if ! jq . "$tmp_file" >/dev/null 2>&1; then
    fail "templates.json is not valid JSON"
  fi
  
  # Check for required fields
  log_info "Checking for required fields..."
  
  if ! jq -e '.version' "$tmp_file" >/dev/null 2>&1; then
    fail "templates.json is missing 'version' field"
  fi
  
  if ! jq -e '.templates' "$tmp_file" >/dev/null 2>&1; then
    fail "templates.json is missing 'templates' field"
  fi
  
  if ! jq -e '.summary' "$tmp_file" >/dev/null 2>&1; then
    fail "templates.json is missing 'summary' field"
  fi
  
  # Check if our sample template is included
  log_info "Checking if sample template is included..."
  
  if ! jq -e '.templates["sample-template"]' "$tmp_file" >/dev/null 2>&1; then
    fail "Sample template not found in templates.json"
  fi
  
  # Clean up
  rm -f "$tmp_file"
  
  log_success "Output verification completed successfully"
}

main() {
  export LOCAL_DEVELOPMENT="true"
  export AWS_ENDPOINT_URL="http://localhost:4566"
  
  check_localstack
  verify_bucket_exists
  run_metadata_generator
  verify_output
  
  log_header "Verification Complete"
  log_success "✅ Metadata generator is working correctly!"
}

main "$@"