#!/bin/bash

# Test Targets Start Script
# Starts only the test target containers for local development

set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if docker-compose.yml exists
check_compose_file() {
    if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        error "docker-compose.yml not found in project root: $PROJECT_ROOT"
        exit 1
    fi
}

# Start test target containers
start_test_targets() {
    info "Starting test target containers..."
    
    cd "$PROJECT_ROOT"
    
    # Start test target services and proxy
    docker-compose up -d test-app-1 test-app-2 test-proxy
    
    if [[ $? -eq 0 ]]; then
        success "Test target containers started successfully"
    else
        error "Failed to start test target containers"
        exit 1
    fi
}

# Health check function
check_health() {
    local service=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    info "Checking health of $service at $url..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            success "$service is healthy"
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "$service failed health check after $max_attempts attempts"
            return 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
}

# Perform health checks on all test targets
health_checks() {
    info "Performing health checks..."
    
    local failed=0
    
    # Check test-app-1
    if ! check_health "test-app-1" "http://localhost:8080"; then
        ((failed++))
    fi
    
    # Check test-app-2
    if ! check_health "test-app-2" "http://localhost:8081"; then
        ((failed++))
    fi
    
    # Check proxy
    if ! check_health "test-proxy" "http://localhost:80/health"; then
        ((failed++))
    fi
    
    if [[ $failed -eq 0 ]]; then
        success "All test targets are healthy"
        return 0
    else
        error "$failed test target(s) failed health checks"
        return 1
    fi
}

# Show status of test targets
show_status() {
    info "Test target status:"
    echo ""
    
    # Show container status
    docker-compose ps test-app-1 test-app-2 2>/dev/null || {
        warn "Could not retrieve container status"
        return 1
    }
    
    echo ""
    info "Available test targets:"
    echo "  • test-app-1: http://test-app-1:8000"
    echo "  • test-app-2: http://test-app-2:8000"
    echo "  • vulnerable-apache: http://vulnerable-apache:8002"
    echo ""
}

# Main execution
main() {
    info "FastScan Test Targets - Start Script"
    echo ""
    
    # Pre-flight checks
    check_docker
    check_compose_file
    
    # Start containers
    start_test_targets
    
    # Wait a moment for containers to initialize
    sleep 3
    
    # Perform health checks
    if health_checks; then
        # Show final status
        echo ""
        show_status
        
        echo ""
        success "Test targets are ready for scanning!"
        info "Use 'devbox run test-targets:stop' to stop the test targets"
    else
        error "Some test targets failed to start properly"
        info "Check container logs with: docker-compose logs test-app-1 test-app-2"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Start FastScan test target containers for local development"
        echo ""
        echo "Options:"
        echo "  --help, -h    Show this help message"
        echo "  --status      Show current status of test targets"
        echo ""
        exit 0
        ;;
    --status)
        show_status
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac