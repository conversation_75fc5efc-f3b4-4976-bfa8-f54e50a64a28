import { Link, useLocation } from "@tanstack/react-router";
import { BookOpenIcon, ChevronUp, DollarSignIcon, LayoutDashboardIcon, LogOutIcon, SearchIcon, Settings, ShieldIcon, User2, WrenchIcon } from "lucide-react";
import type React from "react";
import Logo from "@/components/ui/Logo";
import { useAuth, useLogout } from "@/features/auth/hooks";
import { useSidebarState } from "@/features/layout/hooks";
import { cn } from "@/lib/utils";

interface NavItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  available: boolean;
  badge?: string;
  subLocations?: string[];
}

const navItems: NavItem[] = [
  {
    name: "Overview",
    path: "/dashboard",
    icon: LayoutDashboardIcon,
    available: true,
  },
  {
    name: "Financial Impact",
    path: "/financial-impact",
    icon: DollarSignIcon,
    available: false,
  },
  {
    name: "Remediation",
    path: "/remediation",
    icon: WrenchIcon,
    available: false,
  },
  {
    name: "Explanations",
    path: "/explanations",
    icon: BookOpenIcon,
    available: false,
  },
  {
    name: "Scans",
    path: "/scan",
    icon: SearchIcon,
    available: true,
    subLocations: ["/scans", "/scans/$id"],
  },
  {
    name: "Attack Surface",
    path: "/attack-surface",
    icon: ShieldIcon,
    available: false,
  },
];

const Sidebar: React.FC = () => {
  const location = useLocation();
  const { user } = useAuth();
  const { handleLogout } = useLogout();
  const { setOpen, isMobile } = useSidebarState();

  const handleNavClick = () => {
    if (isMobile) {
      setOpen(false);
    }
  };

  return (
    <aside className="min-h-full w-64 bg-base-200 flex flex-col">
      {/* Header Section with Logo */}
      <div className="p-4 border-b border-base-300 h-16">
        <Link to="/" className="flex items-center">
          <Logo size="sm" />
        </Link>
      </div>

      {/* Navigation Section */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="menu space-y-1 w-full">
          {navItems.map((item) => {
            const isActive = location.pathname === item.path || item.subLocations?.some((subLocation) => location.pathname.startsWith(subLocation));
            const Icon = item.icon;

            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  onClick={handleNavClick}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-lg transition-colors",
                    isActive ? "bg-primary text-primary-content font-medium" : "text-base-content hover:bg-base-300",
                    !item.available && "text-base-content/40 cursor-not-allowed",
                  )}
                  disabled={!item.available}
                >
                  {/* <Icon className="h-5 w-5" /> */}
                  <span>{item.name}</span>
                  {item.badge && <span className="badge badge-primary badge-sm ml-auto">{item.badge}</span>}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Profile Section */}
      <div className="p-4 border-t border-base-300">
        <div className="dropdown dropdown-top dropdown-end w-full">
          <button type="button" className="btn btn-ghost w-full justify-start gap-3 p-3 h-10" tabIndex={0}>
            <div className="avatar avatar-placeholder">
              <div className="bg-primary text-primary-content w-8 rounded-full">{user?.name?.charAt(0) || "U"}</div>
            </div>
            <div className="flex-1 text-left">
              <div className="font-semibold text-sm">{user?.name || "Demo User"}</div>
              <div className="text-xs opacity-70">{user?.email || "<EMAIL>"}</div>
            </div>
            <ChevronUp className="h-4 w-4" />
          </button>

          <ul className="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-300">
            <li>
              <button type="button" className="flex items-center gap-3">
                <User2 className="h-4 w-4" />
                Account
              </button>
            </li>
            <li>
              <button type="button" className="flex items-center gap-3">
                <Settings className="h-4 w-4" />
                Settings
              </button>
            </li>
            <li>
              <hr className="my-1" />
            </li>
            <li>
              <button type="button" onClick={handleLogout} className="flex items-center gap-3 text-error hover:bg-error/10">
                <LogOutIcon className="h-4 w-4" />
                Logout
              </button>
            </li>
          </ul>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
