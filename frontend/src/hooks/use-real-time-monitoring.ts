import { useCallback, useEffect, useRef, useState } from "react";
import type { BackendScanStatusResponse } from "@/features/scans/services";
import { scanApi } from "@/features/scans/services";

interface UseRealTimeMonitoringOptions {
  enabled?: boolean;
  onStatusChange?: (status: BackendScanStatusResponse) => void;
  onError?: (error: Error) => void;
}

interface UseRealTimeMonitoringReturn {
  scanStatus: BackendScanStatusResponse | null;
  isPolling: boolean;
  error: Error | null;
  connectionStatus: "connected" | "disconnected" | "error";
  lastUpdate: string | null;
  refetch: () => void;
}

const POLLING_INTERVAL = 2000; // 2 seconds
const MAX_RETRIES = 5;

/**
 * Real-time monitoring hook for scan status updates.
 *
 * This hook bypasses Alova's cache by using .send(true) to ensure fresh data
 * is fetched from the backend on every polling interval, which is essential
 * for real-time status monitoring.
 */
export const useRealTimeMonitoring = (scanId: string | undefined, options: UseRealTimeMonitoringOptions = {}): UseRealTimeMonitoringReturn => {
  const { enabled = true, onStatusChange, onError } = options;

  // state management
  const [scanStatus, setScanStatus] = useState<BackendScanStatusResponse | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<"connected" | "disconnected" | "error">("disconnected");
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  // Refs for cleanup
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);
  const isMountedRef = useRef(true);

  // Fetch scan status
  const fetchScanStatus = useCallback(async () => {
    if (!scanId || !isMountedRef.current) return;

    try {
      // Force fresh request by bypassing cache - this is crucial for real-time monitoring
      const response = await scanApi.getScanStatus(scanId).send(true);

      if (!isMountedRef.current) return;

      // Reset error state on success
      retryCountRef.current = 0;
      setError(null);
      setConnectionStatus("connected");
      setScanStatus(response);
      setLastUpdate(new Date().toISOString());

      // Call status change callback
      onStatusChange?.(response);

      // Stop polling if scan is completed or failed
      if (response.status === "completed" || response.status === "failed" || response.status === "validated") {
        setIsPolling(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }
    } catch (err) {
      if (!isMountedRef.current) return;

      const error = err instanceof Error ? err : new Error("Failed to fetch scan status");
      retryCountRef.current += 1;

      if (retryCountRef.current >= MAX_RETRIES) {
        setIsPolling(false);
        setConnectionStatus("error");
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }

      setError(error);
      onError?.(error);
    }
  }, [scanId, onStatusChange, onError]);

  // Manual refetch
  const refetch = useCallback(() => {
    fetchScanStatus();
  }, [fetchScanStatus]);

  // Start/stop polling based on scanId and enabled state
  useEffect(() => {
    if (!scanId || !enabled) {
      setIsPolling(false);
      setConnectionStatus("disconnected");
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Initial fetch
    fetchScanStatus();
    setIsPolling(true);

    // Set up polling interval
    intervalRef.current = setInterval(fetchScanStatus, POLLING_INTERVAL);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [scanId, enabled, fetchScanStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    scanStatus,
    isPolling,
    error,
    connectionStatus,
    lastUpdate,
    refetch,
  };
};
