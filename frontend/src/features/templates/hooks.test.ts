import { act, renderHook } from "@testing-library/react";
import { afterEach, describe, expect, it, vi } from "vitest";
import { useTemplateSelection } from "./hooks";

// Mock the underlying template service and alova client
vi.mock("./services", () => ({
  templateService: {
    getTemplates: vi.fn(),
  },
}));

vi.mock("alova/client", () => ({
  useRequest: vi.fn(),
}));

const mockTemplateIndex = {
  version: "1.0",
  generated_at: "2025-01-19T10:30:00Z",
  templates: {
    "CVE-2024-3400": {
      id: "CVE-2024-3400",
      name: "Palo Alto Networks PAN-OS Command Injection",
      path: "templates/CVE-2024-3400.yaml",
      author: ["pdteam"],
      severity: "critical" as const,
      description: "...",
      tags: ["cve", "panos", "rce"],
      size: 2048,
    },
    "CVE-2023-22518": {
      id: "CVE-2023-22518",
      name: "Atlassian Confluence RCE",
      path: "templates/CVE-2023-22518.yaml",
      author: ["projectdiscovery"],
      severity: "high" as const,
      description: "...",
      tags: ["cve", "confluence", "rce"],
      size: 1536,
    },
    "http-proxy": {
      id: "http-proxy",
      name: "HTTP Proxy Detection",
      path: "templates/http-proxy.yaml",
      author: ["pdteam"],
      severity: "info" as const,
      description: "...",
      tags: ["proxy", "http"],
      size: 512,
    },
  },
  summary: {
    total: 3,
    by_severity: { critical: 1, high: 1, medium: 0, low: 0, info: 1 },
  },
};

describe("useTemplateSelection (Refactored)", () => {
  const { useRequest } = require("alova/client");

  afterEach(() => {
    vi.clearAllMocks();
  });

  // Test initial state
  it("should initialize with correct default state while loading", () => {
    useRequest.mockReturnValue({ data: undefined, loading: true, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(null);
    expect(result.current.selectedTemplates).toEqual([]);
    expect(result.current.filteredTemplates).toEqual([]);
    expect(result.current.filterOptions).toEqual({ search: "", severities: [], tags: [] });
  });

  // Test data processing
  it("should process and derive data correctly when loaded", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    expect(result.current.isLoading).toBe(false);
    expect(result.current.templateSummary).toEqual(mockTemplateIndex.summary);
    expect(result.current.availableTags).toEqual(["cve", "confluence", "http", "panos", "proxy", "rce"]);
    expect(result.current.filteredTemplates).toHaveLength(3);
  });

  // Test filtering
  it("should filter templates by search term", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.updateFilterOptions({ search: "palo alto" });
    });

    expect(result.current.filteredTemplates).toHaveLength(1);
    // @ts-expect-error
    expect(result.current.filteredTemplates[0].id).toBe("CVE-2024-3400");
  });

  it("should filter templates by severity", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.filterBySeverity("critical");
    });

    expect(result.current.filteredTemplates).toHaveLength(1);
    // @ts-expect-error
    expect(result.current.filteredTemplates[0].id).toBe("CVE-2024-3400");
  });

  it("should filter templates by tag", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.filterByTag("confluence");
    });

    expect(result.current.filteredTemplates).toHaveLength(1);
    // @ts-expect-error
    expect(result.current.filteredTemplates[0].id).toBe("CVE-2023-22518");
  });

  // Test selections
  it("should toggle template selection", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.toggleTemplateSelection("CVE-2024-3400");
    });
    expect(result.current.selectedTemplates).toEqual(["CVE-2024-3400"]);

    act(() => {
      result.current.toggleTemplateSelection("CVE-2024-3400");
    });
    expect(result.current.selectedTemplates).toEqual([]);
  });

  it("should select all filtered templates", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.filterByTag("rce"); // Filter to 2 templates
    });
    expect(result.current.filteredTemplates).toHaveLength(2);

    act(() => {
      result.current.selectAllFiltered();
    });
    expect(result.current.selectedTemplates).toEqual(["CVE-2024-3400", "CVE-2023-22518"]);
  });

  it("should clear the selection", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.toggleTemplateSelection("CVE-2024-3400");
    });
    expect(result.current.selectedTemplates).toEqual(["CVE-2024-3400"]);

    act(() => {
      result.current.clearSelection();
    });
    expect(result.current.selectedTemplates).toEqual([]);
  });

  // Test statistics
  it("should calculate selection statistics correctly", () => {
    useRequest.mockReturnValue({ data: mockTemplateIndex, loading: false, error: null });
    const { result } = renderHook(() => useTemplateSelection());

    act(() => {
      result.current.toggleTemplateSelection("CVE-2024-3400");
      result.current.toggleTemplateSelection("http-proxy");
    });

    expect(result.current.selectionStats.total).toBe(2);
    expect(result.current.selectionStats.bySeverity).toEqual({
      critical: 1,
      high: 0,
      medium: 0,
      low: 0,
      info: 1,
    });
  });
});
