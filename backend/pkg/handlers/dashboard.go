package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/scan"
	"nuclear_pond/pkg/types"

	"github.com/go-chi/render"
)

// DashboardMetricsHandler handles the dashboard metrics endpoint
func DashboardMetricsHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Dashboard metrics request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	metrics, err := scan.GetDashboardMetrics()
	if err != nil {
		log.Printf("Error getting dashboard metrics: %v", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, types.Response{
			Error:   "Internal Server Error",
			Message: "Error retrieving dashboard metrics: " + err.Error(),
		})

		return
	}

	render.JSON(w, r, metrics)
}
