# Network Module

This Terraform module provides shared networking infrastructure for the Nuclear Pond project, including VPC, subnets, internet gateway, NAT gateway, and route tables.

## Architecture

The module creates a complete networking foundation:

```
┌─────────────────────────────────────────────────────────────┐
│                        VPC (10.0.0.0/16)                   │
├─────────────────────────┬───────────────────────────────────┤
│     Public Subnets      │        Private Subnets            │
│                         │                                   │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ Public AZ1      │   │   │ Private AZ1                 │ │
│  │ 10.0.1.0/24     │   │   │ 10.0.3.0/24                 │ │
│  │ - ALB           │   │   │ - ECS Tasks                 │ │
│  │ - NAT Gateway   │   │   │ - Private Resources         │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
│                         │                                   │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ Public AZ2      │   │   │ Private AZ2                 │ │
│  │ 10.0.2.0/24     │   │   │ 10.0.4.0/24                 │ │
│  │ - ALB           │   │   │ - ECS Tasks                 │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
└─────────────────────────┴───────────────────────────────────┘
           │                              │
    Internet Gateway                 NAT Gateway
           │                              │
        Internet                     Internet (outbound)
```

### Useful Commands

```bash
# Check VPC configuration
aws ec2 describe-vpcs --vpc-ids <vpc-id>

# Check subnet configuration
aws ec2 describe-subnets --filters "Name=vpc-id,Values=<vpc-id>"

# Check route tables
aws ec2 describe-route-tables --filters "Name=vpc-id,Values=<vpc-id>"

# Check NAT Gateway status
aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=<vpc-id>"
```

