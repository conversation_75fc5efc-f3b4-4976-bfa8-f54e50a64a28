 Code Review Report: edge-scanner

  Summary

  The edge-scanner is a legitimate defensive security tool that performs vulnerability scanning using Nuclei. The code appears
  to be a distributed scanning system that fetches scanning jobs from an API and executes Nuclei security scans.

  Overall Assessment: ✅ Legitimate Defensive Security Tool

  The codebase follows Go best practices and implements a distributed vulnerability scanning system using Nuclei for security
  testing purposes.

  ---
  Detailed Findings

  1. Go Project Structure & Dependencies ✅

  File: go.mod:3-19

  Strengths:
  - Modern Go version (1.23.0) with proper toolchain
  - Minimal, focused dependencies
  - Uses protobuf for data serialization
  - Proper module naming convention

  Issues:
  - Version mismatch: go.mod specifies go 1.23.0 but toolchain uses go1.24.5

  Recommendation: Align Go version and toolchain version for consistency.

  ---
  2. Application Entry Point ⚠️

  File: cmd/scanner/main.go:12-35

  Strengths:
  - Clean main function structure
  - Proper URL parsing and query parameter handling
  - Uses ticker for controlled polling intervals

  Issues:
  - Critical: Missing graceful shutdown handling
  - No context cancellation support
  - Hard-coded 2-minute polling interval
  - Missing signal handling for clean exits

  Recommendation: Implement graceful shutdown with signal handling.

  ---
  3. Configuration Management ✅

  File: internal/configs/config.go:20-63

  Strengths:
  - Proper environment variable handling with defaults
  - Good error logging for invalid values
  - Fatal error for missing required API_ENDPOINT
  - Type-safe integer conversion with fallback

  Minor Issues:
  - Missing validation for API_ENDPOINT URL format
  - No validation for reasonable WorkerCount limits

  ---
  4. Nuclei Integration ⚠️

  File: internal/nuclei/nuclei.go:11-49

  Critical Issues:
  - Security: Command injection vulnerability at line 21-37
  - Bug: Port parameter unused (commented out lines 14-17)
  - Bug: Incorrect context usage at line 42 - creates new command instead of using existing one

  Code Issue Example:
  cmd = exec.CommandContext(ctx, cmd.Path, cmd.Args[1:]...)  // Line 42 - Wrong!
  Should be:
  cmd = exec.CommandContext(ctx, "nuclei", cmd.Args[1:]...)

  Recommendations:
  1. Fix context usage for proper timeout handling
  2. Add input validation to prevent command injection
  3. Implement port parameter properly

  ---
  5. Scanner Worker Implementation ⚠️

  File: internal/scanner/worker.go:19-49

  Issues:
  - Bug: Commented out defer wg.Done() at line 20 - will cause goroutine leaks
  - Bug: Duplicate filename generation at lines 23-24
  - Logic: Inconsistent logging - line 44 duplicates line 31

  Critical Fix Needed:
  func Worker(id int, resultsDir string, tasks <-chan ScanTask, wg *sync.WaitGroup, saveJobs chan<- string) {
      defer wg.Done()  // This MUST be uncommented
      // ... rest of function
  }

  ---
  6. Controller Implementation ⚠️

  File: internal/controller/scan_cycle.go:52-60

  Critical Issue:
  - Race Condition: Worker goroutine receives wg parameter but calls it recursively at line 57
  - Bug: Port iteration logic commented out (lines 140, 144, 148) but ScanTask still has Port field

  Fix Needed:
  Remove the wg parameter from scanner.Worker call since it's handled in the goroutine.

  ---
  7. API Client Implementation ⚠️

  File: internal/apiclient/apiclient.go:19-129

  Issues:
  - Error Handling: Falls back to hardcoded values when protobuf parsing fails (lines 43-53)
  - HTTP: No timeout configuration for HTTP client
  - HTTP: No retry logic for failed requests
  - Security: No input validation on apiURL parameter

  Recommendations:
  1. Add HTTP client timeouts
  2. Implement proper error handling instead of silent fallback
  3. Add request retry mechanism

  ---
  8. Docker Configuration ✅

  File: Dockerfile:1-55

  Strengths:
  - Multi-stage build for smaller image size
  - Non-root user implementation
  - Proper file permissions
  - Health check included
  - Security best practices followed

  Minor Issue:
  - Missing Nuclei installation - the scanner won't work without Nuclei binary

  ---
  9. Test Coverage ✅

  File: internal/nuclei/nuclei_test.go and cmd/scanner/main_test.go

  Strengths:
  - Comprehensive test cases covering edge cases
  - Proper temp directory cleanup
  - Good error condition testing
  - Benchmark tests included

  Issues:
  - Tests don't actually verify Nuclei execution (expected due to binary dependency)
  - Test signature mismatch in nuclei_test.go:25 - uses port parameter not in current function

  ---
  Priority Fixes Required

  🔴 Critical (Fix Immediately)

  1. Command Injection Risk in nuclei.go:21-42 - Add input sanitization
  2. Goroutine Leak in worker.go:20 - Uncomment defer wg.Done()
  3. Context Bug in nuclei.go:42 - Fix command context usage

  🟡 High Priority

  1. Add graceful shutdown to main.go
  2. Fix worker synchronization in scan_cycle.go:57
  3. Add HTTP timeouts in apiclient.go

  🟢 Medium Priority

  1. Add Nuclei binary to Dockerfile
  2. Implement proper error handling instead of fallbacks
  3. Add input validation for URLs and parameters

  Security Assessment

  The application is a legitimate defensive security tool with no malicious intent. However, it contains several bugs that
  could affect reliability and security. The Nuclei integration follows standard practices for vulnerability scanning tools.

  Conclusion

  The codebase demonstrates good Go practices overall but requires immediate fixes for critical bugs, particularly the
  goroutine synchronization and command execution issues. Once these are addressed, it will be a solid, production-ready
  vulnerability scanning system.

