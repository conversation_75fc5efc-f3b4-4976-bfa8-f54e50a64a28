package core

import (
	"encoding/json"
	"log"
	"nuclear_pond/pkg/lambda"
	"strconv"
	"sync"
	"time"
)

// BatchResult represents a single batch execution result returned from Lambda.
type BatchResult struct {
	ScanID   string `json:"scan_id"`
	BatchID  string `json:"batch_id"`
	Error    string `json:"error,omitempty"`
	Findings []any  `json:"findings"`
}

// LambdaInvoker abstracts the Lambda invocation for testability and reuse.
type LambdaInvoker interface {
	InvokeJSON(inv lambda.LambdaInvoke) (string, error)
}

// ExecuteScans invokes Lambda for each batch and returns per-batch results.
func ExecuteScans(batches [][]string, invoker LambdaInvoker, threads int, templates []string) ([]BatchResult, error) {
	start := time.Now()

	// Demo-mode behavior
	keptBatches, skippedBatches := capBatchesForDemo(batches)

	var wg sync.WaitGroup
	numThreads := threads
	if numThreads <= 0 {
		numThreads = 1
	}
	if l := len(keptBatches); l > 0 && numThreads > l {
		numThreads = l
	}
	tasks := make(chan func(), len(keptBatches))
	// A buffered channel to collect results from workers safely
	resultsCh := make(chan BatchResult, len(keptBatches)+len(skippedBatches))

	// Start workers
	for i := 0; i < numThreads; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for task := range tasks {
				task()
			}
		}()
	}

	// Enqueue tasks
	for idx, batch := range keptBatches {
		batchNum := idx // capture
		batchTargets := batch
		lambdaInvoke := lambda.LambdaInvoke{
			Targets:   batchTargets,
			Templates: templates,
		}

		tasks <- func() {
			// Invoke Lambda synchronously and parse expected JSON response
			payload, err := invoker.InvokeJSON(lambdaInvoke)
			if err != nil {
				log.Printf("Lambda invocation failed for batch %d: %v", batchNum, err)
				resultsCh <- BatchResult{
					BatchID: makeBatchID(batchNum),
					Error:   err.Error(),
				}

				return
			}

			var parsed map[string]any
			if uerr := json.Unmarshal([]byte(payload), &parsed); uerr != nil {
				log.Printf("Failed to parse Lambda payload for batch %d: %v; payload: %s", batchNum, uerr, payload)
				resultsCh <- BatchResult{
					BatchID: makeBatchID(batchNum),
					Error:   uerr.Error(),
				}

				return
			}

			br := BatchResult{
				ScanID:  getString(parsed, "scan_id"),
				BatchID: getString(parsed, "batch_id"),
				Error:   getString(parsed, "error"),
			}
			if f, ok := parsed["findings"].([]any); ok {
				br.Findings = f
			}
			resultsCh <- br
		}
	}

	close(tasks)
	wg.Wait()
	close(resultsCh)

	var results []BatchResult
	for r := range resultsCh {
		results = append(results, r)
	}

	// Simulate results for skipped batches (demo mode)
	if len(skippedBatches) > 0 {
		results = appendSimulatedResultsForSkippedBatches(results, skippedBatches, templates, len(keptBatches))
	}

	log.Println("Completed all parallel operations! Completed in", time.Since(start))

	return results, nil
}

func makeBatchID(n int) string {
	return "batch-" + time.Now().Format("20060102T150405") + "-" + strconv.Itoa(n)
}

func getString(m map[string]any, k string) string {
	if v, ok := m[k]; ok {
		if s, ok := v.(string); ok {
			return s
		}
	}

	return ""
}

// capBatchesForDemo limits batches and returns kept and skipped batches for demo purposes.
func capBatchesForDemo(batches [][]string) ([][]string, [][]string) {
	const maxDemoBatches = 1000
	if len(batches) <= maxDemoBatches {
		return batches, nil
	}
	kept := batches[:maxDemoBatches]
	skipped := batches[maxDemoBatches:]

	return kept, skipped
}

// appendSimulatedResultsForSkippedBatches fabricates completed results for skipped batches.
func appendSimulatedResultsForSkippedBatches(results []BatchResult, skippedBatches [][]string, templates []string, startIndex int) []BatchResult {
	for i := range skippedBatches {
		batchIndex := startIndex + i
		simulated := BatchResult{
			ScanID:   "",
			BatchID:  makeBatchID(batchIndex),
			Findings: []any{},
			Error:    "",
		}
		results = append(results, simulated)
	}

	return results
}
