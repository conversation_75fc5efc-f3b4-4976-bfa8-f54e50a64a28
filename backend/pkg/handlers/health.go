package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/types"

	"github.com/go-chi/render"
)

// IndexHandler handles the root endpoint.
func IndexHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Index request: %s %s from %s", r.<PERSON>, r.URL.Path, r.RemoteAddr)

	render.JSON(w, r, types.Response{
		Message: "Welcome to the Nuclear Pond API",
	})
}

// HealthHandler handles the health check endpoint.
func HealthHandler(w http.ResponseWriter, r *http.Request) {
	render.JSON(w, r, types.Response{
		Status: "ok",
	})
}
