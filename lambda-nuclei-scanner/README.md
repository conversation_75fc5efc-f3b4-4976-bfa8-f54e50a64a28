# Lambda-Nuclei-Scanner

AWS Lambda function that runs Nuclei vulnerability scans in a serverless environment.

## Overview

This project provides a single-mode execution engine for Nuclei running on AWS Lambda. Templates and the Nuclei binary are shipped via Lambda layers for deterministic, network-isolated execution.

## Features

- **Lambda-only execution**: Purpose-built for AWS Lambda
- **Bundled templates via layer**: Templates are packaged under `/opt/templates` (no dynamic download)
- **Structured JSON response**: Returns parsed findings inline in the response
- **Hardened execution**: Safe, fixed Nuclei arguments and validation around output handling
- **Stateless runtime**: Uses `/tmp` for ephemeral files; no S3 writes

## Architecture

- Go-based Lambda handler: `cmd/lambda/main.go`
- Lambda layers: Nuclei binary and templates (placed under `/opt`)
- Runtime working directory: `/tmp` for targets and output files

## Request and Response

### Input Event (JSON)

```json
{
  "targets": ["https://example.com", "https://demo.example.com"],
  "templates": ["CVE-2024-3400", "CVE-2023-22518"],
  "scan_id": "abc123def456",
  "batch_id": "batch-1"
}
```

### Fields

- `targets` (required): Array of target URLs (must start with http:// or https://)
- `templates` (optional): Array of template file names or CVE IDs bundled in the layer. When provided, each is resolved to `/opt/templates/<name>.yaml` (or `.yml`). If omitted, Nuclei runs with its default template discovery as configured in the runtime.
- `scan_id` (optional): Dashless ID provided by the orchestrator
- `batch_id` (optional): Per-invocation batch identifier (defaults to `batch-1`)

### Response (Structured JSON)

```json
{
  "scan_id": "abc123def456",
  "batch_id": "batch-1",
  "findings": [
    { /* nuclei finding object #1 */ },
    { /* nuclei finding object #2 */ }
  ],
  "templates_used": ["CVE-2024-3400", "CVE-2023-22518"],
  "targets_scanned": 2,
  "started_at": "2025-01-01T00:00:00Z",
  "ended_at": "2025-01-01T00:00:02Z",
  "error": ""
}
```

- Only the JSON mode is supported. Findings are returned inline in `findings` (parsed from NDJSON written by Nuclei).

## Template Handling

- Templates are provided by a Lambda layer under `/opt/templates`.
- Optional `templates` input lets you run a subset by name (e.g., `CVE-2024-3400`). Each entry maps to `/opt/templates/<name>.yaml` (or `.yml`).
- There is no dynamic download or S3-based template retrieval.

## Environment and I/O

- Nuclei binary is resolved from `/opt/nuclei` (or common variants under `/opt`).
- Ephemeral files in `/tmp`:
  - `targets.txt`: written when multiple targets are provided
  - `output.json`: NDJSON results from Nuclei (parsed then removed)
- The function does not upload to S3 and does not require any environment variables.

## Building

### Prerequisites

- Go 1.22+
- Node.js (for build script)
- AWS CLI (for deployment)

### Build for Lambda Runtime

```bash
npm run build
# Produces a 'bootstrap' executable for Lambda (runtime: provided.al2)
```

### Manual build

```bash
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap ./cmd/lambda
```

## Deployment

This function is typically deployed via Terraform under the `nuclei_lambda` module. See `terraform/nuclei_lambda/README.md` for infrastructure details.

For manual deployment, package the `bootstrap` into a ZIP and upload to a Lambda function configured with the `provided.al2` runtime and the required layers (Nuclei binary + templates).

## Limitations and Notes

- Only JSON output is supported.
- When `templates` are omitted, Nuclei uses its default template discovery/behavior in the runtime. Ensure the layer or runtime configuration provides suitable defaults.
- Invalid template names may lead to Nuclei errors if the referenced file does not exist under `/opt/templates`.
