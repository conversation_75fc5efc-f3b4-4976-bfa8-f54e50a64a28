/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as R404RouteImport } from './routes/404'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthenticatedScanRouteImport } from './routes/_authenticated/scan'
import { Route as AuthenticatedRemediationRouteImport } from './routes/_authenticated/remediation'
import { Route as AuthenticatedFinancialImpactRouteImport } from './routes/_authenticated/financial-impact'
import { Route as AuthenticatedExplanationsRouteImport } from './routes/_authenticated/explanations'
import { Route as AuthenticatedDashboardRouteImport } from './routes/_authenticated/dashboard'
import { Route as AuthenticatedAttackSurfaceRouteImport } from './routes/_authenticated/attack-surface'
import { Route as AuthenticatedScansIndexRouteImport } from './routes/_authenticated/scans/index'
import { Route as AuthenticatedScansIdRouteImport } from './routes/_authenticated/scans/$id'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const R404Route = R404RouteImport.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedScanRoute = AuthenticatedScanRouteImport.update({
  id: '/scan',
  path: '/scan',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedRemediationRoute =
  AuthenticatedRemediationRouteImport.update({
    id: '/remediation',
    path: '/remediation',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedFinancialImpactRoute =
  AuthenticatedFinancialImpactRouteImport.update({
    id: '/financial-impact',
    path: '/financial-impact',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedExplanationsRoute =
  AuthenticatedExplanationsRouteImport.update({
    id: '/explanations',
    path: '/explanations',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDashboardRoute = AuthenticatedDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAttackSurfaceRoute =
  AuthenticatedAttackSurfaceRouteImport.update({
    id: '/attack-surface',
    path: '/attack-surface',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedScansIndexRoute = AuthenticatedScansIndexRouteImport.update({
  id: '/scans/',
  path: '/scans/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedScansIdRoute = AuthenticatedScansIdRouteImport.update({
  id: '/scans/$id',
  path: '/scans/$id',
  getParentRoute: () => AuthenticatedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/404': typeof R404Route
  '/login': typeof LoginRoute
  '/attack-surface': typeof AuthenticatedAttackSurfaceRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/explanations': typeof AuthenticatedExplanationsRoute
  '/financial-impact': typeof AuthenticatedFinancialImpactRoute
  '/remediation': typeof AuthenticatedRemediationRoute
  '/scan': typeof AuthenticatedScanRoute
  '/scans/$id': typeof AuthenticatedScansIdRoute
  '/scans': typeof AuthenticatedScansIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/404': typeof R404Route
  '/login': typeof LoginRoute
  '/attack-surface': typeof AuthenticatedAttackSurfaceRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/explanations': typeof AuthenticatedExplanationsRoute
  '/financial-impact': typeof AuthenticatedFinancialImpactRoute
  '/remediation': typeof AuthenticatedRemediationRoute
  '/scan': typeof AuthenticatedScanRoute
  '/scans/$id': typeof AuthenticatedScansIdRoute
  '/scans': typeof AuthenticatedScansIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/404': typeof R404Route
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/attack-surface': typeof AuthenticatedAttackSurfaceRoute
  '/_authenticated/dashboard': typeof AuthenticatedDashboardRoute
  '/_authenticated/explanations': typeof AuthenticatedExplanationsRoute
  '/_authenticated/financial-impact': typeof AuthenticatedFinancialImpactRoute
  '/_authenticated/remediation': typeof AuthenticatedRemediationRoute
  '/_authenticated/scan': typeof AuthenticatedScanRoute
  '/_authenticated/scans/$id': typeof AuthenticatedScansIdRoute
  '/_authenticated/scans/': typeof AuthenticatedScansIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/404'
    | '/login'
    | '/attack-surface'
    | '/dashboard'
    | '/explanations'
    | '/financial-impact'
    | '/remediation'
    | '/scan'
    | '/scans/$id'
    | '/scans'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/404'
    | '/login'
    | '/attack-surface'
    | '/dashboard'
    | '/explanations'
    | '/financial-impact'
    | '/remediation'
    | '/scan'
    | '/scans/$id'
    | '/scans'
  id:
    | '__root__'
    | '/'
    | '/404'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/attack-surface'
    | '/_authenticated/dashboard'
    | '/_authenticated/explanations'
    | '/_authenticated/financial-impact'
    | '/_authenticated/remediation'
    | '/_authenticated/scan'
    | '/_authenticated/scans/$id'
    | '/_authenticated/scans/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  R404Route: typeof R404Route
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/404': {
      id: '/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof R404RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/scan': {
      id: '/_authenticated/scan'
      path: '/scan'
      fullPath: '/scan'
      preLoaderRoute: typeof AuthenticatedScanRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/remediation': {
      id: '/_authenticated/remediation'
      path: '/remediation'
      fullPath: '/remediation'
      preLoaderRoute: typeof AuthenticatedRemediationRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/financial-impact': {
      id: '/_authenticated/financial-impact'
      path: '/financial-impact'
      fullPath: '/financial-impact'
      preLoaderRoute: typeof AuthenticatedFinancialImpactRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/explanations': {
      id: '/_authenticated/explanations'
      path: '/explanations'
      fullPath: '/explanations'
      preLoaderRoute: typeof AuthenticatedExplanationsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/dashboard': {
      id: '/_authenticated/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/attack-surface': {
      id: '/_authenticated/attack-surface'
      path: '/attack-surface'
      fullPath: '/attack-surface'
      preLoaderRoute: typeof AuthenticatedAttackSurfaceRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/scans/': {
      id: '/_authenticated/scans/'
      path: '/scans'
      fullPath: '/scans'
      preLoaderRoute: typeof AuthenticatedScansIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/scans/$id': {
      id: '/_authenticated/scans/$id'
      path: '/scans/$id'
      fullPath: '/scans/$id'
      preLoaderRoute: typeof AuthenticatedScansIdRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedAttackSurfaceRoute: typeof AuthenticatedAttackSurfaceRoute
  AuthenticatedDashboardRoute: typeof AuthenticatedDashboardRoute
  AuthenticatedExplanationsRoute: typeof AuthenticatedExplanationsRoute
  AuthenticatedFinancialImpactRoute: typeof AuthenticatedFinancialImpactRoute
  AuthenticatedRemediationRoute: typeof AuthenticatedRemediationRoute
  AuthenticatedScanRoute: typeof AuthenticatedScanRoute
  AuthenticatedScansIdRoute: typeof AuthenticatedScansIdRoute
  AuthenticatedScansIndexRoute: typeof AuthenticatedScansIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedAttackSurfaceRoute: AuthenticatedAttackSurfaceRoute,
  AuthenticatedDashboardRoute: AuthenticatedDashboardRoute,
  AuthenticatedExplanationsRoute: AuthenticatedExplanationsRoute,
  AuthenticatedFinancialImpactRoute: AuthenticatedFinancialImpactRoute,
  AuthenticatedRemediationRoute: AuthenticatedRemediationRoute,
  AuthenticatedScanRoute: AuthenticatedScanRoute,
  AuthenticatedScansIdRoute: AuthenticatedScansIdRoute,
  AuthenticatedScansIndexRoute: AuthenticatedScansIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  R404Route: R404Route,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
