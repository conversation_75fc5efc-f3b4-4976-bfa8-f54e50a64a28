package aws

import (
	"log"
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
)

// AWSConfig holds configuration for AWS services
type AWSConfig struct {
	Region         string
	Endpoint       string // Empty for production, LocalStack URL for local
	S3Bucket       string
	DynamoDBTable  string
	LambdaFunction string
	IsLocal        bool
}

// NewAWSConfig creates a new AWS configuration based on environment variables
func NewAWSConfig() *AWSConfig {
	isLocal := os.Getenv("LOCAL_DEVELOPMENT") == "true"

	config := &AWSConfig{
		Region:         getEnvOrDefault("AWS_REGION", "us-east-1"),
		S3Bucket:       os.Getenv("AWS_S3_BUCKET"),
		DynamoDBTable:  os.Getenv("AWS_DYNAMODB_TABLE"),
		LambdaFunction: os.Getenv("AWS_LAMBDA_FUNCTION_NAME"),
		IsLocal:        isLocal,
	}

	if isLocal {
		// LocalStack configuration
		config.Endpoint = getEnvOrDefault("AWS_ENDPOINT_URL", "http://localhost:4566")

		// Use local development resource names if not explicitly set
		if config.S3Bucket == "" {
			config.S3Bucket = "fastscan-local-bucket"
		}
		if config.DynamoDBTable == "" {
			config.DynamoDBTable = "fastscan-local-scans"
		}
		if config.LambdaFunction == "" {
			config.LambdaFunction = "fastscan-nuclei-function"
		}
	} else {
		// Production configuration - validate required environment variables
		if config.S3Bucket == "" || config.DynamoDBTable == "" || config.LambdaFunction == "" {
			log.Fatal("CRITICAL: AWS_S3_BUCKET, AWS_DYNAMODB_TABLE, and AWS_LAMBDA_FUNCTION_NAME must be set for production mode")
		}
	}

	return config
}

// CreateSession creates an AWS session based on the configuration
func (c *AWSConfig) CreateSession() (*session.Session, error) {
	awsConfig := &aws.Config{
		Region: aws.String(c.Region),
	}

	if c.IsLocal {
		// LocalStack configuration
		awsConfig.Endpoint = aws.String(c.Endpoint)
		awsConfig.S3ForcePathStyle = aws.Bool(true)

		// Use test credentials for LocalStack
		testAccessKey := getEnvOrDefault("AWS_ACCESS_KEY_ID", "test")
		testSecretKey := getEnvOrDefault("AWS_SECRET_ACCESS_KEY", "test")
		awsConfig.Credentials = credentials.NewStaticCredentials(testAccessKey, testSecretKey, "")
	}

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		log.Printf("Failed to create AWS session: %v", err)

		return nil, err
	}

	return sess, nil
}

// getEnvOrDefault returns the environment variable value or a default value if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}

	return defaultValue
}
