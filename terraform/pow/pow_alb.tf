# Application Load Balancer configuration for PoW targets

# Security Group for the PoW ALB
resource "aws_security_group" "pow_alb_sg" {
  count = local.create_pow_resources
  
  name        = "${var.project_name}-pow-alb-sg"
  description = "Allow HTTP inbound traffic to PoW ALB"
  vpc_id      = local.vpc_id

  ingress {
    description = "HTTP from Internet"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-alb-sg"
  })
}

# PoW Network Load Balancer (changed from ALB to avoid URL filtering)
resource "aws_lb" "pow_alb" {
  count = local.create_pow_resources
  
  name               = "${var.project_name}-pow-nlb"
  internal           = false
  load_balancer_type = "network"
  # Note: NLB doesn't use security groups, uses subnet-level security
  subnets            = local.public_subnet_ids

  enable_deletion_protection = false
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-nlb"
  })
}

# Target Group for PoW EC2 instances (NLB with HTTP health checks)
resource "aws_lb_target_group" "pow_ec2_tg" {
  count = local.create_pow_resources
  
  name        = "${var.project_name}-pow-ec2-tg"
  port        = 80
  protocol    = "TCP"
  target_type = "instance"
  vpc_id      = local.vpc_id

  health_check {
    enabled             = true
    protocol            = "TCP"
    port                = "traffic-port"
    interval            = 300
    timeout             = 10
    healthy_threshold   = 2
    unhealthy_threshold = 10
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-ec2-tg"
  })
}

# Listener for the PoW NLB
resource "aws_lb_listener" "pow_http_listener" {
  count = local.create_pow_resources
  
  load_balancer_arn = aws_lb.pow_alb[0].arn
  port              = "80"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.pow_ec2_tg[0].arn
  }
}

# Output the ALB DNS name
output "pow_alb_dns_name" {
  description = "DNS name of the PoW ALB"
  value       = local.create_pow_resources > 0 ? aws_lb.pow_alb[0].dns_name : null
} 