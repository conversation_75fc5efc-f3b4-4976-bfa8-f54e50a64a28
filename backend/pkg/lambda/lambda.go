package lambda

import (
	"encoding/json"
	"fmt"
	"log"
	"nuclear_pond/pkg/aws"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/lambda"
)

// struct for lambda invoke
type LambdaInvoke struct {
	Targets   []string `json:"targets"`
	Templates []string `json:"templates,omitempty"`
}

// Invoker defines the interface for invoking Lambda with a JSON request.
type Invoker interface {
	InvokeJSON(inv LambdaInvoke) (string, error)
}

// Client encapsulates a reusable AWS Lambda client and a validated function name.
type Client struct {
	svc          *lambda.Lambda
	functionName string
}

// NewClient creates a reusable Lambda client with validated function name.
func NewClient(functionName string) (*Client, error) {
	if err := validateLambdaFunctionName(functionName); err != nil {
		log.Printf("SECURITY: Invalid Lambda function name: %s", functionName)

		return nil, fmt.Errorf("invalid function name: %w", err)
	}

	cfg := aws.NewAWSConfig()
	sess, err := cfg.CreateSession()
	if err != nil {
		log.Printf("Failed to create AWS session: %v", err)

		return nil, err
	}

	return &Client{svc: lambda.New(sess), functionName: functionName}, nil
}

// InvokeJSON marshals the payload and invokes the configured Lambda once.
func (c *Client) InvokeJSON(inv LambdaInvoke) (string, error) {
	payloadBytes, err := json.Marshal(inv)
	if err != nil {
		return "", fmt.Errorf("failed to marshal LambdaInvoke: %w", err)
	}
	input := &lambda.InvokeInput{
		FunctionName: awssdk.String(c.functionName),
		Payload:      []byte(payloadBytes),
	}
	result, err := c.svc.Invoke(input)
	if err != nil {
		log.Printf("Failed to invoke lambda function %s: %v", c.functionName, err)

		return "", err
	}
	if result.FunctionError != nil {
		log.Printf("Lambda function returned error: %s", *result.FunctionError)

		return string(result.Payload), fmt.Errorf("lambda function error: %s", *result.FunctionError)
	}

	return string(result.Payload), nil
}

// invokeFunction executes a Lambda function and returns its payload.
func invokeFunction(payload, functionName string) (string, error) {
	// Security validation: Ensure function name matches expected pattern
	if err := validateLambdaFunctionName(functionName); err != nil {
		log.Printf("SECURITY: Invalid Lambda function name: %s", functionName)

		return "", fmt.Errorf("invalid function name: %w", err)
	}

	// Create AWS configuration
	awsConfig := aws.NewAWSConfig()

	log.Printf("Invoking Lambda function: %s in region: %s (local: %t)",
		functionName, awsConfig.Region, awsConfig.IsLocal)

	// Create AWS session using the configuration abstraction
	sess, sessionErr := awsConfig.CreateSession()
	if sessionErr != nil {
		log.Printf("Failed to create AWS session: %v", sessionErr)

		return "", sessionErr
	}

	// Create a Lambda service client.
	svc := lambda.New(sess)

	// Create the input
	input := &lambda.InvokeInput{
		FunctionName: awssdk.String(functionName),
		Payload:      []byte(payload),
	}

	// Invoke the lambda function
	result, err := svc.Invoke(input)
	if err != nil {
		log.Printf("Failed to invoke lambda function %s: %v", functionName, err)

		return "", err
	}

	// Check for function errors in the response
	if result.FunctionError != nil {
		log.Printf("Lambda function returned error: %s", *result.FunctionError)

		return string(result.Payload), fmt.Errorf("lambda function error: %s", *result.FunctionError)
	}

	log.Printf("Lambda invocation successful for function: %s", functionName)

	return string(result.Payload), nil
}

// InvokeAndReturn invokes Lambda synchronously and returns the raw JSON payload.
func InvokeAndReturn(inv LambdaInvoke, functionName string) (string, error) {
	// Marshal the request
	payloadBytes, err := json.Marshal(inv)
	if err != nil {
		return "", fmt.Errorf("failed to marshal LambdaInvoke: %w", err)
	}

	// Call the existing internal function
	resp, err := invokeFunction(string(payloadBytes), functionName)
	if err != nil {
		return resp, err
	}

	return resp, nil
}

func validateLambdaFunctionName(functionName string) error {
	// Only allow function names that match our expected pattern
	// This prevents invoking arbitrary Lambda functions
	allowedPatterns := []string{
		"fastscan-nuclei-function",
		"fastscan-local-nuclei-function",
	}

	for _, pattern := range allowedPatterns {
		if functionName == pattern {
			return nil
		}
	}

	return fmt.Errorf("function name '%s' not in allowed list", functionName)
}
