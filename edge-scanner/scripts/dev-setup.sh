#!/bin/bash

set -e

echo "🚀 Setting up Edge Scanner Local Development Environment"

# Check if we're in the edge-scanner directory
if [ ! -f "go.mod" ] || [ ! -d "cmd/scanner" ]; then
    echo "❌ Please run this script from the edge-scanner directory"
    exit 1
fi

# Create .env file from example if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update if needed."
else
    echo "✅ .env file already exists"
fi

# Create results directory
mkdir -p results
echo "✅ Results directory created"

# Install Go dependencies
echo "📦 Installing Go dependencies..."
go mod download

# Check if Nuclei is installed
if ! command -v nuclei &> /dev/null; then
    echo "🔧 Installing Nuclei scanner..."
    go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@3.1.7
    echo "✅ Nuclei installed"
else
    echo "✅ Nuclei already installed"
fi

# Update Nuclei templates
echo "📥 Updating Nuclei templates..."
nuclei -update-templates

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "Available commands:"
echo "  ./scripts/run-dev.sh         - Run scanner with development settings"
echo "  ./scripts/run-mock-api.sh    - Start mock API server"
echo "  ./scripts/test-integration.sh - Run integration tests"
echo "  ./scripts/run-single.sh      - Run single scan cycle for testing"
echo ""
echo "Next steps:"
echo "1. Review and update .env file if needed"
echo "2. Start mock API: ./scripts/run-mock-api.sh"
echo "3. Run scanner: ./scripts/run-dev.sh"