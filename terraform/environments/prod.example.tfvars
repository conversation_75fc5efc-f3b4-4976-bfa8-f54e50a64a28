# ==============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# ==============================================================================

# Core configuration
project_name = "fastscan"
environment = "prod"

# ==============================================================================
# GOVERNANCE AND TAGGING
# ==============================================================================

owner = "security-team"
cost_center = "security"
business_unit = "security"

# Production environment policies
backup_policy = "daily"
monitoring_policy = "enhanced"
compliance_framework = "soc2"

# Resource discovery
enable_resource_discovery = true

# ==============================================================================
# INFRASTRUCTURE CONFIGURATION
# ==============================================================================

# Module enablement
enable_pow_targets = false  # Disable for production

# Network configuration (production-ready)
vpc_cidr = "10.0.0.0/16"
public_subnet_az1_cidr = "10.0.1.0/24"
public_subnet_az2_cidr = "10.0.2.0/24"
private_subnet_az1_cidr = "10.0.3.0/24"


# Lambda configuration (production-optimized)
nuclei_version = "3.1.7"
nuclei_arch = "linux_amd64"
nuclei_timeout = 55  # 55 seconds, to meet the deadline
memory_size = 512

# ECS configuration (production-ready)
nuclear_pond_task_cpu = "1024"
nuclear_pond_task_memory = "2048"
nuclear_pond_desired_count = 1
nuclear_pond_container_port = 8082
nuclear_pond_health_check_path = "/health-check"
nuclear_pond_log_retention_days = 30
nuclear_pond_enable_deletion_protection = true

# Authentication configuration
jwt_secret = "REPLACE_WITH_SECRETS_MANAGER_REFERENCE"
demo_password = "REPLACE_WITH_SECRETS_MANAGER_REFERENCE"

# Frontend configuration
enable_frontend_cloudfront = true  # CDN for production
frontend_cloudfront_price_class = "PriceClass_All"
frontend_domain_name = "try.rootevidence.com"

# GitHub integration
github_token = ""
github_repository = "your-org/fastscan"

# Isolate Lambda function
enable_vpc_isolation = false

# ==============================================================================
# CUSTOM TAGS
# ==============================================================================

tags = {
  CreatedBy = "terraform"
  Purpose = "production-workload"
  AutoShutdown = "disabled"
  Team = "security-prod"
  Critical = "true"
} 