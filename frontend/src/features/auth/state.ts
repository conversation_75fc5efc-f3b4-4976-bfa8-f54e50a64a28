import { atom } from "jotai";
import { authService } from "@/lib/auth";
import type { AuthState } from "./types";

// Initial auth state
const initialAuthState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Load initial state with proper JWT validation
const getInitialState = (): AuthState => {
  if (typeof window === "undefined") return initialAuthState;

  // Check if we have a valid JWT token
  if (!authService.isAuthenticated()) {
    // Clear any stale auth data
    localStorage.removeItem("auth");
    return initialAuthState;
  }

  // If JWT is valid, try to load user data from localStorage
  const savedAuth = localStorage.getItem("auth");
  if (savedAuth) {
    try {
      const parsedAuth = JSON.parse(savedAuth);
      // Only return saved auth if it's properly structured and we have a valid JWT
      if (parsedAuth.user && parsedAuth.isAuthenticated) {
        return {
          ...parsedAuth,
          isAuthenticated: true, // Ensure this is true since JWT is valid
          isLoading: false,
          error: null,
        };
      }
    } catch (_e) {
      localStorage.removeItem("auth");
    }
  }

  return initialAuthState;
};

// Auth state atom without persistence (we'll handle it manually)
export const authAtom = atom<AuthState>(getInitialState());
