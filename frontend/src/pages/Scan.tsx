import { TargetInput } from "@/features/scans/components/TargetInput";
import { TargetList } from "@/features/scans/components/TargetList";
import { TemplateSelector } from "@/features/templates/components/TemplateSelector";
import { useInitiateScan } from "@/hooks/use-api";
import { useToast } from "@/hooks/use-toast";
import type { ScanConfig } from "@/types";
import { triggerCacheInvalidation } from "@/utils/cache-invalidation";
import { toHumanString } from "@/utils/human-readable";
import { validateDemoTargets } from "@/utils/target-validation";
import { arktypeResolver } from "@hookform/resolvers/arktype";
import { useLocation, useNavigate } from "@tanstack/react-router";
import { type } from "arktype";
import { ShieldIcon, TargetIcon } from "lucide-react";
import type React from "react";
import { useCallback, useState } from "react";
import { FormProvider, useForm, useWatch } from "react-hook-form";

// Form validation schema using ArkType
const scanFormSchema = type({
  targets: "string.url[]",
  templates: "string[]",
});

export type ScanFormValues = typeof scanFormSchema.infer;

const suggestScanners = (targetCount: number) => {
  return Math.ceil(targetCount / 100);
};

const Scan: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [manualScannersInput, setManualScannersInput] = useState<string>("");

  const isManualMode = new URLSearchParams(location.search).has("manual");

  const formMethods = useForm<ScanFormValues>({
    resolver: arktypeResolver(scanFormSchema),
    defaultValues: {
      targets: [],
      templates: [],
    },
  });

  const { handleSubmit, setValue } = formMethods;
  const targets = useWatch({ name: "targets", control: formMethods.control });
  const templates = useWatch({ name: "templates", control: formMethods.control });

  const { send } = useInitiateScan();

  // Stable callback to prevent infinite re-renders in TemplateSelector
  const _handleSelectionChange = useCallback(
    (selected: string[]) => {
      setValue("templates", selected);
    },
    [setValue],
  );

  const onSubmit = async (data: ScanFormValues) => {
    setIsSubmitting(true);

    try {
      // Security validation: Ensure all targets are demo targets
      const validation = validateDemoTargets(data.targets);
      if (!validation.isValid) {
        toast({
          title: "Invalid Targets Detected",
          description: `${validation.invalidTargets.length} target(s) are not allowed. Only demo targets on fast-scan-demo-target.click domain are permitted.`,
          variant: "destructive",
        });
        return;
      }

      const autoScanners = suggestScanners(data.targets.length);
      const resolvedScanners = isManualMode && !!manualScannersInput ? Math.max(1, Number(manualScannersInput)) : autoScanners;

      const payload: ScanConfig = {
        targets: data.targets,
        target_count: data.targets.length,
        scanners: Math.min(resolvedScanners, Math.max(1, data.targets.length)),
        templates: data.templates,
      };

      const response = await send(payload);

      // Trigger cache invalidation after scan starts
      triggerCacheInvalidation("scan_started", response.RequestId);

      toast({
        title: "Scan started",
        description: "You have been successfully started a scan.",
        variant: "success",
        duration: 4000,
      });

      formMethods.reset();

      // Navigate to scan details page automatically
      navigate({
        to: "/scans/$id",
        params: { id: response.RequestId },
        replace: true, // Replace current history entry to prevent back button navigation issues
      });
    } catch (error) {
      toast({
        title: "Error initiating scan",
        description: (error as Error).message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-primary/10 rounded-lg">
          <ShieldIcon className="h-8 w-8 text-primary" />
        </div>
        <div>
          <h1 className="text-4xl font-bold text-primary mb-2">New Security Scan</h1>
          <p className="text-base-content/70 text-lg">Configure and initiate a comprehensive vulnerability assessment</p>
        </div>
      </div>

      <FormProvider {...formMethods}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-secondary/10 rounded-lg">
                <TargetIcon className="h-5 w-5 text-secondary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Target Configuration</h2>
                <p className="text-base-content/60">Define the targets for your security assessment</p>
              </div>
            </div>
            <TargetInput />
          </div>

          <div className="card card-sm bg-base-200 border border-base-300">
            <div className="card-body">
              <TargetList formMethods={formMethods} />
            </div>
          </div>

          <div className="divider">
            <span className="text-base-content/60">Template Selection</span>
          </div>

          <div className="card card-sm bg-base-200 border border-base-300">
            <div className="card-body">
              <TemplateSelector onSelectionChange={_handleSelectionChange} />
            </div>
          </div>

          <div className="card-body border-t border-base-300 bg-base-200 rounded-lg">
            <div className="flex flex-col gap-4 items-center">
              {isManualMode && (
                <div className="w-full flex flex-col gap-3">
                  <div className="alert alert-info">
                    <div>
                      <span>Manual mode enabled. Override number of scanners (concurrency).</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                    <div className="form-control">
                      <label htmlFor="manual-scanners" className="label">
                        <span className="label-text">Scanners (concurrency)</span>
                      </label>
                      <input
                        id="manual-scanners"
                        type="number"
                        min={1}
                        inputMode="numeric"
                        className="input input-bordered"
                        value={manualScannersInput}
                        placeholder={String(suggestScanners(targets.length || 1))}
                        onChange={(e) => setManualScannersInput(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}

              <button type="submit" disabled={isSubmitting || targets.length === 0 || templates?.length === 0} className="btn btn-success btn-lg w-1/2">
                {isSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span> Initiating Scan...
                  </>
                ) : targets.length === 0 ? (
                  "Add targets to initiate scan"
                ) : (
                  `Scan ${toHumanString(targets.length)} Target${targets.length !== 1 ? "s" : ""} Now`
                )}
              </button>

              {targets.length > 0 &&
                (() => {
                  const auto = suggestScanners(targets.length);
                  const scanners =
                    isManualMode && manualScannersInput.trim() !== ""
                      ? Math.min(Math.max(1, Math.floor(Number(manualScannersInput))), targets.length)
                      : Math.min(auto, targets.length);
                  const perScanner = Math.ceil((targets.length || 1) / scanners);
                  return (
                    <div className="text-sm space-y-2 text-neutral-500">
                      <div>
                        <strong>Scan Configuration:</strong> {targets.length.toLocaleString()} target{targets.length !== 1 ? "s" : ""} using {scanners.toLocaleString()} scanner
                        {scanners !== 1 ? "s" : ""}, ~{perScanner.toLocaleString()} per scanner.
                      </div>
                    </div>
                  );
                })()}
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default Scan;
