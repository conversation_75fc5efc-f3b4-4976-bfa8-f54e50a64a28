import { Alert<PERSON>riangleIcon, CheckCircleIcon, ClockIcon, WrenchIcon } from "lucide-react";

export default function Remediation() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <WrenchIcon className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-primary mb-2">Remediation</h1>
            <p className="text-base-content/70 text-lg">Manage and track vulnerability remediation efforts</p>
          </div>
        </div>
      </div>

      {/* Coming Soon Card */}
      <div className="card bg-base-100 shadow border border-base-300">
        <div className="card-body">
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="p-4 bg-warning/10 rounded-full mb-6">
              <AlertTriangleIcon className="h-12 w-12 text-warning" />
            </div>
            <h2 className="text-2xl font-bold text-base-content mb-4">Coming Soon</h2>
            <p className="text-base-content/70 text-lg mb-8 max-w-2xl leading-relaxed">
              The Remediation module is currently under development. This feature will provide comprehensive tools for managing vulnerability remediation workflows, tracking
              progress, and coordinating security fixes across your organization.
            </p>

            {/* Feature Preview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl">
              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <CheckCircleIcon className="h-8 w-8 text-success mb-3" />
                  <h3 className="font-semibold text-base-content">Fix Tracking</h3>
                  <p className="text-sm text-base-content/60">Track remediation progress and completion status</p>
                </div>
              </div>

              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <ClockIcon className="h-8 w-8 text-info mb-3" />
                  <h3 className="font-semibold text-base-content">Timeline Management</h3>
                  <p className="text-sm text-base-content/60">Set deadlines and manage remediation timelines</p>
                </div>
              </div>

              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <WrenchIcon className="h-8 w-8 text-secondary mb-3" />
                  <h3 className="font-semibold text-base-content">Workflow Automation</h3>
                  <p className="text-sm text-base-content/60">Automate remediation workflows and notifications</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
