#!/bin/bash

set -e

echo "🎯 Running Edge Scanner - Single Cycle Test"

# Check if we're in the edge-scanner directory
if [ ! -f "go.mod" ] || [ ! -d "cmd/scanner" ]; then
    echo "❌ Please run this script from the edge-scanner directory"
    exit 1
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Run ./scripts/dev-setup.sh first"
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Override settings for single run
export DEBUG_MODE="true"
export WORKER_COUNT="2"

echo "🔧 Single run configuration:"
echo "  API Endpoint: ${API_ENDPOINT}"
echo "  Worker Count: ${WORKER_COUNT}"
echo ""

# Create results directory if it doesn't exist
mkdir -p results

# Run single cycle using development version
echo "🚀 Starting single scan cycle..."
go run ./cmd/scanner/main_dev.go -single-run -debug

echo ""
echo "✅ Single scan cycle completed!"
echo "📁 Check the results/ directory for output files"