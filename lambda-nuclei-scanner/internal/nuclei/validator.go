package nuclei

import (
	"fmt"
	"log"
	"main/internal/types"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// ValidateBinary ensures the nuclei binary exists and is executable.
func ValidateBinary(env *types.Environment) error {
	if stat, err := os.Stat(env.NucleiBinary); err != nil {
		log.Printf("ERROR: Nuclei binary not found at %s: %v", env.NucleiBinary, err)

		return fmt.Errorf("nuclei binary not found at %s: %w", env.NucleiBinary, err)
	} else if stat.Mode()&0o111 == 0 {
		if err := os.Chmod(env.NucleiBinary, 0o755); err != nil {
			log.Printf("ERROR: Failed to make nuclei binary executable: %v", err)

			return fmt.Errorf("nuclei binary is not executable and cannot fix permissions: %w", err)
		}
	}

	return nil
}

// DetectResults returns true when nuclei output indicates positive findings.
// Note: This function analyzes stdout/stderr output, but when using -jsonl mode,
// actual findings are written to the output file, not stdout.
func DetectResults(output string) bool {
	lowerOutput := strings.ToLower(output)

	// Check for command line errors first
	if strings.Contains(lowerOutput, "flag provided but not defined") ||
		strings.Contains(lowerOutput, "error parsing") ||
		strings.Contains(lowerOutput, "invalid flag") ||
		strings.Contains(lowerOutput, "unknown flag") ||
		strings.Contains(lowerOutput, "fatal") ||
		strings.Contains(lowerOutput, "panic") {
		log.Printf("Detected nuclei command line error, not scan results")

		return false
	}

	// Explicit no results indicators
	if strings.Contains(lowerOutput, "no results found") ||
		strings.Contains(lowerOutput, "better luck next time") ||
		strings.Contains(lowerOutput, "no results") ||
		strings.Contains(lowerOutput, "0 results") {
		log.Printf("Nuclei explicitly reported no results found")

		return false
	}

	// When running in -silent mode with -jsonl output, nuclei typically produces
	// minimal stdout/stderr. The actual findings are in the output file.
	// We should be conservative and assume no results unless we see clear indicators.

	// Look for very specific positive indicators
	hasPositiveResults := strings.Contains(lowerOutput, "vulnerabilities detected") ||
		strings.Contains(lowerOutput, "vulnerability found") ||
		strings.Contains(lowerOutput, "issues found")

	if hasPositiveResults {
		log.Printf("Nuclei output indicates positive results found")

		return true
	}

	// In silent mode, minimal output usually means no findings
	log.Printf("No clear result indicators in nuclei output, assuming no results")

	return false
}

// ValidateOutputFile ensures the expected output file exists and is non-empty when results are expected.
func ValidateOutputFile(filePath string, expectResults bool) error {
	stat, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		if expectResults {
			log.Printf("CRITICAL: Output file %s not created despite finding results", filePath)
			if files, dirErr := os.ReadDir("/tmp"); dirErr == nil {
				log.Printf("Files in /tmp after nuclei execution:")
				for _, file := range files {
					if strings.Contains(file.Name(), "nuclei") ||
						strings.Contains(file.Name(), "output") ||
						strings.HasSuffix(file.Name(), ".json") {
						if info, statErr := file.Info(); statErr == nil {
							log.Printf("  - %s (%d bytes)", file.Name(), info.Size())
						}
					}
				}
			}
			if pwd, pwdErr := os.Getwd(); pwdErr == nil {
				log.Printf("Current working directory: %s", pwd)
				if files, dirErr := os.ReadDir(pwd); dirErr == nil {
					log.Printf("Files in current directory:")
					for _, file := range files {
						if strings.HasSuffix(file.Name(), ".json") {
							if info, statErr := file.Info(); statErr == nil {
								log.Printf("  - %s (%d bytes)", file.Name(), info.Size())
							}
						}
					}
				}
			}

			return fmt.Errorf("nuclei found results but output file was not created")
		}
		if file, createErr := os.Create(filePath); createErr == nil {
			file.Close()
			log.Printf("Created empty output file: %s", filePath)
		}

		return nil
	}
	if err != nil {
		return fmt.Errorf("error accessing output file: %w", err)
	}

	if stat.Size() == 0 && expectResults {
		return fmt.Errorf("output file exists but is empty despite finding results")
	}

	log.Printf("Output file validated: %s (%d bytes)", filePath, stat.Size())

	return nil
}

// ValidateWithDelay adds a short delay and performs Lambda-specific checks before validating the output file.
func ValidateWithDelay(env *types.Environment, outputMode string, hasResults bool) error {
	if outputMode == types.OutputModeJSON {
		time.Sleep(100 * time.Millisecond)

		// Check what files actually exist in the output directory
		if env.IsLambda {
			log.Printf("Checking for nuclei output files...")
			if files, dirErr := os.ReadDir("/tmp"); dirErr == nil {
				for _, file := range files {
					fileName := file.Name()
					if (strings.Contains(fileName, "nuclei") || strings.Contains(fileName, "output")) &&
						strings.HasSuffix(fileName, ".json") {
						if info, statErr := file.Info(); statErr == nil {
							log.Printf("Found potential nuclei output file: %s (%d bytes)", fileName, info.Size())
						}
					}
				}
			}
		}

		// Check if the output file exists and determine actual results from file content
		stat, err := os.Stat(env.ScanOutput)
		if os.IsNotExist(err) {
			// If no output file was created, create an empty one and assume no results
			if file, createErr := os.Create(env.ScanOutput); createErr == nil {
				file.Close()
				log.Printf("Created empty output file: %s", env.ScanOutput)
			}

			return nil // No error - just no findings
		}
		if err != nil {
			return fmt.Errorf("error accessing output file: %w", err)
		}

		if stat.Size() == 0 {
			log.Printf("Output file %s is empty (no findings)", env.ScanOutput)

			return nil // No error - just no findings
		}

		log.Printf("Output file validated: %s (%d bytes)", env.ScanOutput, stat.Size())

		return nil
	}

	return nil
}

// TestWritePermissions verifies write permissions to the output directory.
func TestWritePermissions(env *types.Environment) error {
	testFile := filepath.Dir(env.ScanOutput) + "/write_test.tmp"
	if testFd, err := os.Create(testFile); err != nil {
		log.Printf("ERROR: Cannot write to output directory %s: %v", filepath.Dir(env.ScanOutput), err)

		return fmt.Errorf("cannot write to output directory: %w", err)
	} else {
		testFd.Close()
		os.Remove(testFile)
		log.Printf("✓ Write permissions confirmed for output directory")
	}
	if absPath, err := filepath.Abs(env.ScanOutput); err == nil {
		log.Printf("Nuclei will write to absolute path: %s", absPath)
	}

	return nil
}

// ValidateScanRequest validates targets and template selections on the request.
func ValidateScanRequest(request *types.ScanRequest) error {
	if len(request.Targets) == 0 {
		return fmt.Errorf("no targets specified in scan request")
	}

	if len(request.Templates) > 0 {
		for _, template := range request.Templates {
			if err := ValidateTemplateName(template); err != nil {
				return fmt.Errorf("invalid template name '%s': %w", template, err)
			}
		}
	}

	return nil
}

// ValidateTemplateName enforces CVE format or a safe custom template name.
func ValidateTemplateName(name string) error {
	name = strings.TrimSuffix(name, ".yaml")
	name = strings.TrimSuffix(name, ".yml")

	cvePattern := regexp.MustCompile(`^CVE-\d{4}-\d{1,}$`)
	if cvePattern.MatchString(name) {
		return nil
	}

	customPattern := regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9_\-\.]+$`)
	if !customPattern.MatchString(name) {
		return fmt.Errorf("template name must contain only alphanumeric characters, hyphens, underscores, or dots")
	}

	return nil
}

// ValidateTemplateExists reports if a template name exists in the provided list.
func ValidateTemplateExists(templateName string, availableTemplates []string) (bool, error) {
	templateName = strings.TrimSuffix(templateName, ".yaml")
	templateName = strings.TrimSuffix(templateName, ".yml")

	for _, available := range availableTemplates {
		available = strings.TrimSuffix(available, ".yaml")
		available = strings.TrimSuffix(available, ".yml")

		if strings.EqualFold(templateName, available) {
			return true, nil
		}
	}

	return false, nil
}

// ValidateTemplatesExist returns a list of missing templates or nil when all exist.
func ValidateTemplatesExist(requestedTemplates, availableTemplates []string) ([]string, error) {
	if len(requestedTemplates) == 0 {
		return nil, nil
	}

	var missingTemplates []string

	for _, template := range requestedTemplates {
		exists, err := ValidateTemplateExists(template, availableTemplates)
		if err != nil {
			return missingTemplates, err
		}

		if !exists {
			missingTemplates = append(missingTemplates, template)
		}
	}

	if len(missingTemplates) > 0 {
		return missingTemplates, fmt.Errorf("the following templates were not found: %s", strings.Join(missingTemplates, ", "))
	}

	return nil, nil
}

// ValidateTemplateFile checks that the given template file exists.
func ValidateTemplateFile(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("template file does not exist: %s", filePath)
	}

	return nil
}
