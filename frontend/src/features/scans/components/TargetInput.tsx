import { PlusIcon, UploadIcon } from "lucide-react";
import type React from "react";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { toast } from "sonner";
import type { ScanFormValues } from "@/pages/Scan";
import { toHumanString } from "@/utils/human-readable";
import { isValidDemoTarget } from "@/utils/target-validation";

// Helper to generate a random alphanumeric string
const generateRandomAlphanumericString = (length: number): string => {
  let result = "";
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

// Normalizes potential targets, validates them, and filters out duplicates or already existing ones.
const normalizeAndValidateTargets = (
  potentialTargets: string[],
  existingTargets: string[],
): {
  addedTargets: string[];
  invalidOriginals: string[];
  skippedDuplicates: string[];
} => {
  const addedTargets: string[] = []; // Stores normalized, valid, unique URLs
  const invalidOriginals: string[] = []; // Stores original inputs that were invalid
  const skippedDuplicates: string[] = []; // Stores original inputs that were duplicates

  // Deduplicate potential targets based on their original form before normalization
  const uniquePotentialOriginals = Array.from(new Set(potentialTargets.map((t) => t.trim()).filter(Boolean)));

  uniquePotentialOriginals.forEach((originalTarget) => {
    let normalizedTarget = originalTarget;
    // Prepend http:// if no scheme is present
    if (!/^[a-zA-Z][a-zA-Z0-9+.-]*:/.test(normalizedTarget)) {
      normalizedTarget = `https://${normalizedTarget}`;
    }

    try {
      new URL(normalizedTarget); // Validate the normalized URL (throws if invalid)

      // Security check: Only allow demo targets
      if (!isValidDemoTarget(normalizedTarget)) {
        invalidOriginals.push(originalTarget);
        return;
      }

      if (!existingTargets.includes(normalizedTarget) && !addedTargets.includes(normalizedTarget)) {
        addedTargets.push(normalizedTarget);
      } else {
        skippedDuplicates.push(originalTarget);
      }
    } catch (_e) {
      invalidOriginals.push(originalTarget);
    }
  });
  return { addedTargets, invalidOriginals, skippedDuplicates };
};

export const TargetInput: React.FC = () => {
  const { setValue, getValues } = useFormContext<ScanFormValues>();
  const [activeTab, setActiveTab] = useState("single");
  const [targetInput, setTargetInput] = useState("");
  const [bulkTargets, setBulkTargets] = useState("");
  const [numDemoTargets, setNumDemoTargets] = useState<number>(10);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isProcessingBulk, setIsProcessingBulk] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [cancelGeneration, setCancelGeneration] = useState(false);

  const handleAddTarget = () => {
    const trimmedInput = targetInput.trim();
    if (!trimmedInput) return;

    const currentTargets = getValues("targets");
    const {
      addedTargets: [newTarget],
      invalidOriginals,
      skippedDuplicates,
    } = normalizeAndValidateTargets([trimmedInput], currentTargets);

    if (newTarget) {
      setValue("targets", [...currentTargets, newTarget]);
      setTargetInput("");
      toast.success("Target Added", { description: `Added: ${newTarget}` });
    } else if (invalidOriginals.length > 0) {
      toast.error("Invalid Target", { description: `"${invalidOriginals[0]}" is not allowed. Only demo targets on fast-scan-demo-target.click domain are permitted.` });
    } else if (skippedDuplicates.length > 0) {
      toast("Target Already Added", { description: `"${skippedDuplicates[0]}" is already in the list.` });
      setTargetInput("");
    }
  };

  const handleProcessAndAddBulkTargets = async () => {
    if (!bulkTargets.trim() || isProcessingBulk) return;

    const potentialTargetsArray = bulkTargets
      .split(/[,;\s]+/)
      .map((t) => t.trim())
      .filter(Boolean);

    if (potentialTargetsArray.length === 0) {
      setBulkTargets("");
      return;
    }

    setIsProcessingBulk(true);

    try {
      // Process in batches for large inputs
      const BATCH_SIZE = 1000;
      if (potentialTargetsArray.length > BATCH_SIZE) {
        const batches = Math.ceil(potentialTargetsArray.length / BATCH_SIZE);
        let totalAdded = 0;
        let totalInvalid = 0;
        let totalDuplicates = 0;

        toast("Processing Bulk Targets", {
          description: `Processing ${potentialTargetsArray.length} targets in ${batches} batches...`,
        });

        for (let i = 0; i < batches; i++) {
          const batchStart = i * BATCH_SIZE;
          const batchEnd = Math.min(batchStart + BATCH_SIZE, potentialTargetsArray.length);
          const batch = potentialTargetsArray.slice(batchStart, batchEnd);

          const currentTargets = getValues("targets");
          const { addedTargets, invalidOriginals, skippedDuplicates } = normalizeAndValidateTargets(batch, currentTargets);

          if (addedTargets.length > 0) {
            setValue("targets", [...currentTargets, ...addedTargets]);
            totalAdded += addedTargets.length;
          }

          totalInvalid += invalidOriginals.length;
          totalDuplicates += skippedDuplicates.length;

          // Yield control to prevent UI blocking
          await new Promise((resolve) => setTimeout(resolve, 0));
        }

        // Show summary
        const messages = [];
        if (totalAdded > 0) messages.push(`${totalAdded} targets added`);
        if (totalInvalid > 0) messages.push(`${totalInvalid} invalid entries skipped`);
        if (totalDuplicates > 0) messages.push(`${totalDuplicates} duplicates skipped`);

        if (totalAdded > 0) {
          toast.success("Bulk Processing Complete", { description: messages.join(", ") });
        } else {
          toast.error("Bulk Processing Complete", { description: messages.join(", ") });
        }
      } else {
        // Process small batches synchronously
        const currentTargets = getValues("targets");
        const { addedTargets, invalidOriginals, skippedDuplicates } = normalizeAndValidateTargets(potentialTargetsArray, currentTargets);

        if (invalidOriginals.length > 0) {
          toast.error("Some Targets Invalid", {
            description: `Skipped ${invalidOriginals.length} invalid entries. Only demo targets on fast-scan-demo-target.click domain are allowed.`,
          });
        }
        if (skippedDuplicates.length > 0 && addedTargets.length === 0 && invalidOriginals.length === 0) {
          toast("No New Targets Added", {
            description: `All ${skippedDuplicates.length} inputs were duplicates.`,
          });
        }
        if (addedTargets.length > 0) {
          setValue("targets", [...currentTargets, ...addedTargets]);
          toast.success("Targets Added", {
            description: `${addedTargets.length} new target(s) added.`,
          });
        }
      }
    } catch (_error) {
      toast.error("Processing Failed", {
        description: "An error occurred while processing bulk targets.",
      });
    } finally {
      setIsProcessingBulk(false);
      setBulkTargets("");
    }
  };

  const handleGenerateDemoTargets = async () => {
    await _handleGenerateDemoTargets();
  };

  const _handleGenerateDemoTargets = async () => {
    if (numDemoTargets <= 0 || numDemoTargets > 100000) {
      return;
    }

    if (isGenerating) return;

    setIsGenerating(true);
    setGenerationProgress(0);
    setCancelGeneration(false);

    const final = [];
    try {
      const CHUNK_SIZE = 1000; // Process in smaller chunks
      const totalChunks = Math.ceil(numDemoTargets / CHUNK_SIZE);
      let processedCount = 0;

      // Process in chunks with proper yielding
      for (let chunkIndex = 0; chunkIndex < totalChunks && !cancelGeneration; chunkIndex++) {
        const chunkStart = chunkIndex * CHUNK_SIZE;
        const chunkEnd = Math.min(chunkStart + CHUNK_SIZE, numDemoTargets);
        const chunkSize = chunkEnd - chunkStart;

        // Generate chunk of targets
        const chunkTargets: string[] = [];
        for (let i = 0; i < chunkSize; i++) {
          const randomSub = generateRandomAlphanumericString(8);
          chunkTargets.push(`http://${randomSub}.fast-scan-demo-target.click`);
        }

        final.push(...chunkTargets);
        processedCount += chunkTargets.length;

        // Update progress
        const progress = Math.round(((chunkIndex + 1) / totalChunks) * 100);
        setGenerationProgress(progress);

        // Yield control to keep UI responsive
        await new Promise((resolve) => setTimeout(resolve, 10));
      }

      if (cancelGeneration) {
        toast("Generation Cancelled", {
          description: `Added ${processedCount} targets before cancellation.`,
        });
      } else {
        setValue("targets", final);
        toast.success("Demo Targets Generated", {
          description: `Successfully added ${processedCount} demo targets.`,
        });
      }
    } catch (_error) {
      toast.error("Generation Failed", {
        description: "An error occurred while generating demo targets.",
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setCancelGeneration(false);
    }
  };

  const handleCancelGeneration = () => {
    setCancelGeneration(true);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== "text/plain") {
      toast.error("Invalid File Type", { description: "Please upload a .txt file." });
      event.target.value = "";
      return;
    }

    const reader = new FileReader();
    reader.onload = async (e) => {
      const content = e.target?.result as string;
      if (content) {
        const potentialTargetsArray = content
          .split(/[,;\s]+/)
          .map((t) => t.trim())
          .filter(Boolean);

        // Process large files in batches
        const BATCH_SIZE = 1000;
        if (potentialTargetsArray.length > BATCH_SIZE) {
          const batches = Math.ceil(potentialTargetsArray.length / BATCH_SIZE);
          let totalAdded = 0;
          let totalInvalid = 0;

          toast("Processing File", {
            description: `Processing ${potentialTargetsArray.length} targets from ${file.name} in ${batches} batches...`,
          });

          try {
            for (let i = 0; i < batches; i++) {
              const batchStart = i * BATCH_SIZE;
              const batchEnd = Math.min(batchStart + BATCH_SIZE, potentialTargetsArray.length);
              const batch = potentialTargetsArray.slice(batchStart, batchEnd);

              const currentTargets = getValues("targets");
              const { addedTargets, invalidOriginals } = normalizeAndValidateTargets(batch, currentTargets);

              if (addedTargets.length > 0) {
                setValue("targets", [...currentTargets, ...addedTargets]);
                totalAdded += addedTargets.length;
              }
              totalInvalid += invalidOriginals.length;

              // Yield control to prevent UI blocking
              await new Promise((resolve) => setTimeout(resolve, 0));
            }

            const messages = [];
            if (totalAdded > 0) messages.push(`${totalAdded} targets added`);
            if (totalInvalid > 0) messages.push(`${totalInvalid} invalid entries skipped`);

            if (totalAdded > 0) {
              toast.success("File Processing Complete", { description: messages.join(", ") });
            } else {
              toast.error("File Processing Complete", { description: messages.join(", ") });
            }
          } catch (_error) {
            toast.error("File Processing Failed", {
              description: "An error occurred while processing the file.",
            });
          }
        } else {
          // Process small files synchronously
          const currentTargets = getValues("targets");
          const { addedTargets, invalidOriginals } = normalizeAndValidateTargets(potentialTargetsArray, currentTargets);

          if (invalidOriginals.length > 0) {
            toast.error("Some Targets Invalid in File", {
              description: `Skipped ${invalidOriginals.length} invalid entries from ${file.name}. Only demo targets on fast-scan-demo-target.click domain are allowed.`,
            });
          }
          if (addedTargets.length > 0) {
            setValue("targets", [...currentTargets, ...addedTargets]);
            toast.success("Targets Added from File", {
              description: `${addedTargets.length} new target(s) added from ${file.name}.`,
            });
          }
        }
      }
      event.target.value = "";
    };
    reader.readAsText(file);
  };

  return (
    <div>
      <div role="tablist" className="tabs tabs-lifted">
        <button type="button" role="tab" className={`tab ${activeTab === "single" ? "tab-active" : ""}`} onClick={() => setActiveTab("single")}>
          Single Target
        </button>
        <button type="button" role="tab" className={`tab ${activeTab === "bulk" ? "tab-active" : ""}`} onClick={() => setActiveTab("bulk")}>
          Bulk Input
        </button>
        <button type="button" role="tab" className={`tab ${activeTab === "demo" ? "tab-active" : ""}`} onClick={() => setActiveTab("demo")}>
          Demo Targets
        </button>
        {/* <button type="button" role="tab" className={`tab ${activeTab === "file" ? "tab-active" : ""}`} onClick={() => setActiveTab("file")}>
          File Upload
        </button> */}
      </div>

      <div className="bg-base-100 p-6 rounded-b-box rounded-tr-box">
        {activeTab === "single" && (
          <div className="flex gap-3">
            <input
              type="text"
              placeholder="Enter domain or URL (e.g., example.com)"
              className="input input-bordered input-primary flex-1"
              value={targetInput}
              onChange={(e) => setTargetInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAddTarget();
                }
              }}
            />
            <button type="button" onClick={handleAddTarget} className="btn btn-primary">
              <PlusIcon className="h-4 w-4" /> Add
            </button>
          </div>
        )}

        {activeTab === "bulk" && (
          <div className="space-y-4">
            <textarea
              placeholder="example.com\nsubdomain.example.org"
              value={bulkTargets}
              onChange={(e) => setBulkTargets(e.target.value)}
              rows={5}
              className="textarea textarea-bordered textarea-primary w-full"
            />
            <button type="button" onClick={handleProcessAndAddBulkTargets} className={`btn btn-secondary ${isProcessingBulk ? "loading" : ""}`} disabled={isProcessingBulk}>
              {isProcessingBulk ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Processing...
                </>
              ) : (
                <>
                  <PlusIcon className="h-4 w-4" /> Process and Add
                </>
              )}
            </button>
          </div>
        )}

        {activeTab === "demo" && (
          <div className="space-y-4">
            <div className="flex gap-3 items-end">
              <div className="flex-1">
                <label className="label" htmlFor="demo-targets">
                  <span className="label-text">
                    Number of targets: <span className="font-bold"> {toHumanString(numDemoTargets)}</span>
                  </span>
                </label>
                <input
                  type="range"
                  min={0}
                  max="100000"
                  value={numDemoTargets}
                  onChange={(e) => setNumDemoTargets(Number(e.target.value) || 1)}
                  className="range range-info w-full"
                  id="demo-targets"
                  disabled={isGenerating}
                />
              </div>
              {!isGenerating ? (
                <button type="button" onClick={handleGenerateDemoTargets} className="btn btn-accent">
                  <PlusIcon className="h-4 w-4" /> Generate
                </button>
              ) : (
                <button type="button" onClick={handleCancelGeneration} className="btn btn-error">
                  Cancel
                </button>
              )}
            </div>

            {isGenerating && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Generating demo targets...</span>
                  <span>{generationProgress}%</span>
                </div>
                <progress className="progress progress-accent w-full" value={generationProgress} max="100"></progress>
              </div>
            )}
          </div>
        )}

        {activeTab === "file" && (
          <div>
            <input type="file" accept=".txt" onChange={handleFileUpload} className="file-input file-input-bordered file-input-info w-full" />
            <div className="alert alert-info mt-4">
              <UploadIcon className="h-4 w-4" />
              <span className="text-sm">Upload a .txt file with targets separated by newlines, commas, or spaces.</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
