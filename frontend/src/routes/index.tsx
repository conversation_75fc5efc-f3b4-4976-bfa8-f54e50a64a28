import { createFileRoute, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/")({
  beforeLoad: async () => {
    // Import authService dynamically to avoid circular dependencies
    const { authService } = await import("@/lib/auth");

    // Check if user has valid JWT token
    if (authService.isAuthenticated()) {
      // Authenticated users go to dashboard
      throw redirect({
        to: "/dashboard",
      });
    } else {
      // Guest users go directly to login
      throw redirect({
        to: "/login",
      });
    }
  },
});
