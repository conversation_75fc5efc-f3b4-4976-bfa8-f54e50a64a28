# edge-sample

A simple Go application to scan for open ports (80, 443), 
and run Nuclei scans on discovered open ports. 
Results are saved as JSON files in the `results/` directory.

## Features
- Call Edge api to get random IP
- Scans each IP for open ports (80, 443)
- Runs Nuclei scans on open ports
- Saves scan results in `results/` as JSON

## Prerequisites
- Go 1.21 or newer
- (Optional) [Nuclei](https://github.com/projectdiscovery/nuclei) binary if required by your implementation

## Project Structure
```
cmd/
  scanner/         # Main application entry point
internal/
  ipgen/           # IP generation logic // deprecated 
  ports/           # Port scanning logic
  nuclei/          # Nuclei scan logic
```

## Build

Clone the repository and build the project:

```sh
git clone https://github.com/RootEvidence/edge-scanner.git
cd nuclei-ip-scanner
cd cmd/scanner
go build -o scanner
```

## Run

From the `home` directory, run:

```sh
go run ./cmd/scanner

```

Scan results will be saved in the `results/` directory.

## Customization
- To change the number of IPs generated or the ports scanned, modify the relevant values in `main.go`.

## License
MIT

