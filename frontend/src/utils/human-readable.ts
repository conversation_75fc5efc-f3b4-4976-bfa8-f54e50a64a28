// SI prefixes for powers of 10 (multiples of 3)
const SI_PREFIXES: Record<number, string> = {
  24: "Y", // yotta
  21: "Z", // zetta
  18: "E", // exa
  15: "P", // peta
  12: "T", // tera
  9: "G", // giga
  6: "M", // mega
  3: "k", // kilo
  0: "", // base unit
  "-3": "m", // milli
  "-6": "µ", // micro
  "-9": "n", // nano
  "-12": "p", // pico
  "-15": "f", // femto
  "-18": "a", // atto
  "-21": "z", // zepto
  "-24": "y", // yocto
} as const;

/**
 * Get the exponent (power of 10) for a given number
 */
function getExponent(n: number): number {
  if (n === 0) return 0;
  return Math.floor(Math.log10(Math.abs(n)));
}

/**
 * Round a number to 3 significant digits
 */
function toPrecision(n: number): number {
  return Number.parseFloat(n.toPrecision(3));
}

/**
 * Convert a number to human-readable format with SI prefixes
 * @param value - The number to convert (can be string or number)
 * @returns Human-readable string with appropriate SI prefix
 *
 * @example
 * toHumanString(1000) // "1k"
 * toHumanString(1500000) // "1.5M"
 * toHumanString(0.001) // "1m"
 */
export function toHumanString(value: string | number): string {
  const n = toPrecision(Number.parseFloat(String(value)));

  // Calculate the appropriate exponent (clamped to available prefixes)
  const exponent = Math.max(Math.min(3 * Math.floor(getExponent(n) / 3), 24), -24);

  const scaledValue = toPrecision(n / 10 ** exponent);
  const prefix = SI_PREFIXES[exponent];

  return `${scaledValue}${prefix}`;
}

// Default export for convenience
export default { toHumanString };
