import { useToast } from "@/hooks/use-toast";
import { useRouter } from "@tanstack/react-router";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import { login, logout } from "./services";
import { authAtom } from "./state";
import type { LoginFormData, LoginFormErrors } from "./types";
import { validateEmail, validatePassword } from "./utils";

// useAuth hook
export const useAuth = () => {
  const [auth, setAuth] = useAtom(authAtom);

  // setAuth that syncs with localStorage
  const setAuthWithSync = (newAuth: typeof auth) => {
    setAuth(newAuth);

    // Sync with localStorage if authenticated
    if (newAuth.isAuthenticated && newAuth.user) {
      localStorage.setItem("auth", JSON.stringify(newAuth));
    } else {
      localStorage.removeItem("auth");
    }
  };

  return {
    auth,
    setAuth: setAuthWithSync,
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
  };
};

// useLogin hook
export interface UseLoginReturn {
  // Form state
  formData: LoginFormData;
  errors: LoginFormErrors;
  isLoading: boolean;

  // Form handlers
  handleEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;

  // Auth state
  isAuthenticated: boolean;
}

export const useLogin = (redirectTo = "/dashboard") => {
  const [auth, setAuth] = useAtom(authAtom);
  const { toast } = useToast();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState<LoginFormData>({
    email: "",
    password: "",
  });

  const [errors, setErrors] = useState<LoginFormErrors>({
    email: null,
    password: null,
    general: null,
  });

  const [isLoading, setIsLoading] = useState(false);

  // Auto-redirect if already authenticated
  useEffect(() => {
    if (auth.isAuthenticated && auth.user) {
      router.navigate({ to: redirectTo });
    }
  }, [auth.isAuthenticated, auth.user, router, redirectTo]);

  // Event handlers
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, email: value }));
    setErrors((prev) => ({ ...prev, email: null, general: null }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, password: value }));
    setErrors((prev) => ({ ...prev, password: null, general: null }));
  };

  // Form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Reset errors
    setErrors({ email: null, password: null, general: null });

    // Validate fields
    const emailError = validateEmail(formData.email);
    const passwordError = validatePassword(formData.password);

    if (emailError || passwordError) {
      setErrors({
        email: emailError,
        password: passwordError,
        general: null,
      });
      setIsLoading(false);
      return;
    }

    try {
      const user = await login(formData.email, formData.password);

      // Verify JWT token is actually stored and valid
      const { authService } = await import("@/lib/auth");
      if (!authService.isAuthenticated()) {
        throw new Error("Authentication failed - invalid token");
      }

      setAuth({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });

      toast({
        title: "Welcome to Evidence",
        description: `Successfully authenticated as ${user.name}`,
        variant: "success",
      });

      router.navigate({ to: redirectTo });
    } catch (err) {
      const errorMessage = (err as Error).message;
      setErrors((prev) => ({ ...prev, general: errorMessage }));
      setAuth({
        ...auth,
        error: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    formData,
    errors,
    isLoading,
    handleEmailChange,
    handlePasswordChange,
    handleSubmit,
    isAuthenticated: auth.isAuthenticated,
  };
};

// useLogout hook
export const useLogout = () => {
  const { setAuth } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  const handleLogout = () => {
    logout();
    setAuth({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });

    toast({
      title: "Logged out",
      description: "You have been successfully logged out.",
    });

    router.navigate({ to: "/login" });
  };

  return { handleLogout };
};
