package main

import (
	"os"
	"path/filepath"
	"sync"
	"testing"
)

func TestMainFunction(t *testing.T) {
	// This test is mainly to ensure the main function doesn't panic
	// We can't easily test the full main function without mocking external dependencies

	// Set up test environment variables
	os.Setenv("API_ENDPOINT", "http://test-api.com")
	os.Setenv("WORKER_COUNT", "2")
	os.Setenv("TASK_CHANNEL_SIZE", "10")
	os.Setenv("RESULTS_DIR", "test-results")

	// Clean up after test
	defer func() {
		os.Unsetenv("API_ENDPOINT")
		os.Unsetenv("WORKER_COUNT")
		os.Unsetenv("TASK_CHANNEL_SIZE")
		os.Unsetenv("RESULTS_DIR")

		// Clean up test directory
		os.RemoveAll("test-results")
	}()

	// Note: We can't actually run the main function in a test because it would
	// try to make real API calls and run nuclei. Instead, we test the individual
	// components separately.
}

func TestCreateResultsDirectory(t *testing.T) {
	// Test directory creation
	testDir := "test-results-dir"

	// Clean up before and after test
	os.RemoveAll(testDir)
	defer os.RemoveAll(testDir)

	// Create directory
	err := os.MkdirAll(testDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Check if directory exists
	if _, err := os.Stat(testDir); os.IsNotExist(err) {
		t.Errorf("Directory %s was not created", testDir)
	}
}

func TestFileProcessing(t *testing.T) {
	// Create a temporary directory for test files
	tempDir, err := os.MkdirTemp("", "main_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create some test JSON files
	testFiles := []string{
		"test1.json",
		"test2.json",
		"test3.txt", // Non-JSON file
		"test4.json",
	}

	for _, filename := range testFiles {
		filePath := filepath.Join(tempDir, filename)
		err := os.WriteFile(filePath, []byte(`{"test": "data"}`), 0644)
		if err != nil {
			t.Fatalf("Failed to create test file %s: %v", filename, err)
		}
	}

	// Read directory
	files, err := os.ReadDir(tempDir)
	if err != nil {
		t.Fatalf("Failed to read directory: %v", err)
	}

	// Count JSON files
	jsonCount := 0
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			jsonCount++
		}
	}

	// Should find 3 JSON files (excluding test3.txt)
	if jsonCount != 3 {
		t.Errorf("Expected 3 JSON files, found %d", jsonCount)
	}
}

func TestFileSizeCheck(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "main_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create files with different sizes
	testCases := []struct {
		name     string
		content  string
		expected bool // whether file should be considered non-empty
	}{
		{
			name:     "empty.json",
			content:  "",
			expected: false,
		},
		{
			name:     "non-empty.json",
			content:  `{"test": "data"}`,
			expected: true,
		},
		{
			name:     "large.json",
			content:  string(make([]byte, 1024)), // 1KB of data
			expected: true,
		},
	}

	for _, tc := range testCases {
		filePath := filepath.Join(tempDir, tc.name)
		err := os.WriteFile(filePath, []byte(tc.content), 0644)
		if err != nil {
			t.Fatalf("Failed to create test file %s: %v", tc.name, err)
		}

		// Check file size
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			t.Fatalf("Failed to stat file %s: %v", tc.name, err)
		}

		isNonEmpty := fileInfo.Size() > 0
		if isNonEmpty != tc.expected {
			t.Errorf("File %s: expected non-empty=%v, got %v", tc.name, tc.expected, isNonEmpty)
		}
	}
}

func TestFileDeletion(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "main_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a test file
	testFile := filepath.Join(tempDir, "test-delete.json")
	err = os.WriteFile(testFile, []byte(`{"test": "data"}`), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Fatalf("Test file was not created")
	}

	// Delete the file
	err = os.Remove(testFile)
	if err != nil {
		t.Fatalf("Failed to delete test file: %v", err)
	}

	// Verify file is deleted
	if _, err := os.Stat(testFile); !os.IsNotExist(err) {
		t.Errorf("Test file was not deleted")
	}
}

func TestConcurrentFileProcessing(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "main_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create multiple test files
	numFiles := 10
	for i := 0; i < numFiles; i++ {
		filename := filepath.Join(tempDir, filepath.Join(tempDir, "test.json"))
		err := os.WriteFile(filename, []byte(`{"test": "data"}`), 0644)
		if err != nil {
			t.Fatalf("Failed to create test file %d: %v", i, err)
		}
	}

	// Read all files
	files, err := os.ReadDir(tempDir)
	if err != nil {
		t.Fatalf("Failed to read directory: %v", err)
	}

	// Process files concurrently (simulate the worker pattern)
	results := make(chan string, len(files))
	var wg sync.WaitGroup

	for _, file := range files {
		wg.Add(1)
		go func(filename string) {
			defer wg.Done()

			filePath := filepath.Join(tempDir, filename)
			fileInfo, err := os.Stat(filePath)
			if err != nil {
				results <- "error"
				return
			}

			if fileInfo.Size() > 0 {
				results <- "processed"
			} else {
				results <- "empty"
			}
		}(file.Name())
	}

	wg.Wait()
	close(results)

	// Count results
	processed := 0
	empty := 0
	errors := 0

	for result := range results {
		switch result {
		case "processed":
			processed++
		case "empty":
			empty++
		case "error":
			errors++
		}
	}

	// All files should be processed successfully
	if processed != numFiles {
		t.Errorf("Expected %d files to be processed, got %d", numFiles, processed)
	}

	if errors > 0 {
		t.Errorf("Expected 0 errors, got %d", errors)
	}
}

func TestEnvironmentVariableHandling(t *testing.T) {
	// Test environment variable handling
	testCases := []struct {
		name     string
		key      string
		value    string
		expected string
	}{
		{
			name:     "API_ENDPOINT",
			key:      "API_ENDPOINT",
			value:    "http://test-api.com",
			expected: "http://test-api.com",
		},
		{
			name:     "WORKER_COUNT",
			key:      "WORKER_COUNT",
			value:    "5",
			expected: "5",
		},
		{
			name:     "RESULTS_DIR",
			key:      "RESULTS_DIR",
			value:    "custom-results",
			expected: "custom-results",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Set environment variable
			os.Setenv(tc.key, tc.value)
			defer os.Unsetenv(tc.key)

			// Get environment variable
			actual := os.Getenv(tc.key)
			if actual != tc.expected {
				t.Errorf("Expected %s=%s, got %s", tc.key, tc.expected, actual)
			}
		})
	}
}

func TestFileExtensionFiltering(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "main_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create files with different extensions
	testFiles := []string{
		"test1.json",
		"test2.JSON",
		"test3.txt",
		"test4.xml",
		"test5.jsonl",
		"test6.json.gz",
	}

	for _, filename := range testFiles {
		filePath := filepath.Join(tempDir, filename)
		err := os.WriteFile(filePath, []byte(`{"test": "data"}`), 0644)
		if err != nil {
			t.Fatalf("Failed to create test file %s: %v", filename, err)
		}
	}

	// Read directory and filter JSON files
	files, err := os.ReadDir(tempDir)
	if err != nil {
		t.Fatalf("Failed to read directory: %v", err)
	}

	jsonFiles := []string{}
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			jsonFiles = append(jsonFiles, file.Name())
		}
	}

	// Should find 3 JSON files (test1.json, test2.JSON, test6.json.gz)
	expectedJSONFiles := 3
	if len(jsonFiles) != expectedJSONFiles {
		t.Errorf("Expected %d JSON files, found %d: %v", expectedJSONFiles, len(jsonFiles), jsonFiles)
	}
}

func BenchmarkFileProcessing(b *testing.B) {
	tempDir, err := os.MkdirTemp("", "main_benchmark")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create test files
	for i := 0; i < 100; i++ {
		filename := filepath.Join(tempDir, filepath.Join(tempDir, "test.json"))
		err := os.WriteFile(filename, []byte(`{"test": "data"}`), 0644)
		if err != nil {
			b.Fatalf("Failed to create test file: %v", err)
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		files, err := os.ReadDir(tempDir)
		if err != nil {
			b.Fatalf("Failed to read directory: %v", err)
		}

		for _, file := range files {
			if filepath.Ext(file.Name()) == ".json" {
				filePath := filepath.Join(tempDir, file.Name())
				_, err := os.Stat(filePath)
				if err != nil {
					b.Fatalf("Failed to stat file: %v", err)
				}
			}
		}
	}
}
