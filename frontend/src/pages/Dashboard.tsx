import { Link } from "@tanstack/react-router";
import { ActivityIcon, AlertCircleIcon, ClockIcon, FolderIcon, GlobeIcon, SearchIcon, SettingsIcon, ShieldCheckIcon, ShieldIcon, TargetIcon } from "lucide-react";
import { useDashboardMetrics } from "@/hooks/use-api";
import InfrastructureStatus from "@/pages/Dashboard/InfrastructureStatus";

function formatBigNumber(num: number) {
  // Format large numbers with 'k', 'M', 'B' suffixes
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1).replace(/\.0$/, "")}k`;
  }
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1).replace(/\.0$/, "")}M`;
  }
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(1).replace(/\.0$/, "")}B`;
  }
  return num;
}

export default function Dashboard() {
  const { data: dashboardData, loading: isLoading, error } = useDashboardMetrics();

  const EmptyState = ({ icon: Icon, title, description }: { icon: React.ElementType; title: string; description: string }) => (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <Icon className="h-12 w-12 text-base-content/40 mb-3" />
      <h3 className="text-lg font-medium text-base-content/80 mb-2">{title}</h3>
      <p className="text-sm text-base-content/60 max-w-sm leading-relaxed">{description}</p>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="loading loading-spinner loading-lg text-primary mb-4"></div>
          <h2 className="text-xl font-semibold text-base-content/80 mb-2">Loading Dashboard</h2>
          <p className="text-sm text-base-content/60">Fetching your scanning metrics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <AlertCircleIcon className="h-12 w-12 text-error mb-4 mx-auto" />
          <h2 className="text-xl font-semibold text-base-content/80 mb-2">Failed to Load Dashboard</h2>
          <p className="text-sm text-base-content/60">Unable to fetch dashboard metrics</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <ShieldIcon className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-primary mb-2">Scanning Dashboard</h1>
            <p className="text-base-content/70 text-lg">Monitor your scanning assessments and vulnerability findings</p>
          </div>
        </div>

        <div className="flex gap-3">
          <Link to="/scan" className="btn btn-primary">
            <SearchIcon className="h-4 w-4" />
            Start New Scan
          </Link>
        </div>
      </div>

      {/* Key Metrics Stats */}
      <div className="stats stats-vertical shadow border border-base-300 lg:stats-horizontal w-full">
        <div className="stat">
          <div className="stat-figure text-primary">
            <SearchIcon className="h-8 w-8" />
          </div>
          <div className="stat-title">Total Scans</div>
          <div className="stat-value text-primary">{dashboardData?.total_scans || 0}</div>
          <div className="stat-desc">Security assessments completed</div>
        </div>

        <div className="stat">
          <div className="stat-figure text-info">
            <ActivityIcon className="h-8 w-8" />
          </div>
          <div className="stat-title">Active Scans</div>
          <div className="stat-value text-info">{dashboardData?.active_scans || 0}</div>
          <div className="stat-desc">Currently running</div>
        </div>

        <div className="stat">
          <div className="stat-figure text-success">
            <GlobeIcon className="h-8 w-8" />
          </div>
          <div className="stat-title">Targets Scanned</div>
          <div className="stat-value text-success">{formatBigNumber(dashboardData?.targets_scanned || 0)}</div>
          <div className="stat-desc">Unique endpoints assessed</div>
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Scans */}
        <div className="card bg-base-100 shadow border border-base-300">
          <div className="card-body">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <ClockIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="card-title text-lg">Recent Scans</h2>
                <p className="text-base-content/60 text-sm">Latest security assessments</p>
              </div>
            </div>

            <div className="flex-1">
              {dashboardData?.recent_scans && dashboardData.recent_scans.length > 0 ? (
                <div className="space-y-3">
                  {dashboardData.recent_scans.map((scan) => (
                    <div key={scan.scan_id} className="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            scan.status === "completed" ? "bg-success" : scan.status === "running" ? "bg-info" : scan.status === "failed" ? "bg-error" : "bg-warning"
                          }`}
                        />
                        <div>
                          <p className="font-medium text-sm">{scan.request_id}</p>
                          <p className="text-xs text-base-content/60">
                            {scan.target_count} target{scan.target_count !== 1 ? "s" : ""}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-base-content/60">{new Date(scan.created_at).toLocaleDateString()}</p>
                        <p
                          className={`text-xs font-medium ${
                            scan.status === "completed" ? "text-success" : scan.status === "running" ? "text-info" : scan.status === "failed" ? "text-error" : "text-warning"
                          }`}
                        >
                          {scan.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <EmptyState icon={SearchIcon} title="No Recent Scans" description="Your recent scanning activity will appear here once you start running security assessments." />
              )}
            </div>

            <div className="card-actions justify-end pt-4 border-t border-base-300">
              <Link to="/scans" className="btn btn-outline btn-primary btn-sm">
                View All Scans
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Findings */}
        <div className="card bg-base-100 shadow border border-base-300">
          <div className="card-body">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-warning/10 rounded-lg">
                <AlertCircleIcon className="h-5 w-5 text-warning" />
              </div>
              <div>
                <h2 className="card-title text-lg">Recent Findings</h2>
                <p className="text-base-content/60 text-sm">Latest security vulnerabilities</p>
              </div>
            </div>

            <div className="flex-1">
              <EmptyState icon={ShieldCheckIcon} title="No Recent Findings" description="Security vulnerabilities and issues discovered by your scans will be displayed here." />
            </div>

            <div className="card-actions justify-end pt-4 border-t border-base-300">
              <Link to="/scans" className="btn btn-outline btn-warning btn-sm">
                View All Findings
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Infrastructure Status */}
      <InfrastructureStatus />

      {/* Quick Actions */}
      <div className="card bg-base-100 shadow border border-base-300">
        <div className="card-body">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-secondary/10 rounded-lg">
              <TargetIcon className="h-5 w-5 text-secondary" />
            </div>
            <div>
              <h2 className="card-title text-lg">Quick Actions</h2>
              <p className="text-base-content/60 text-sm">Common tasks and shortcuts to get started</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link to="/scan" className="btn btn-outline btn-primary h-20 flex-col gap-2 hover:btn-primary">
              <SearchIcon className="h-6 w-6" />
              <span className="text-sm font-medium">Start New Scan</span>
            </Link>

            <button type="button" className="btn btn-outline btn-secondary h-20 flex-col gap-2 btn-disabled">
              <FolderIcon className="h-6 w-6" />
              <span className="text-sm font-medium">Manage Templates</span>
            </button>

            <button type="button" className="btn btn-outline btn-accent h-20 flex-col gap-2 btn-disabled">
              <TargetIcon className="h-6 w-6" />
              <span className="text-sm font-medium">Manage Targets</span>
            </button>

            <button type="button" className="btn btn-outline btn-neutral h-20 flex-col gap-2 btn-disabled">
              <SettingsIcon className="h-6 w-6" />
              <span className="text-sm font-medium">Configuration</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
