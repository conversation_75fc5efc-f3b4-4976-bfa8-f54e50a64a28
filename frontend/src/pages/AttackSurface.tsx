import { AlertTriangleIcon, EyeIcon, NetworkIcon, ShieldIcon } from "lucide-react";

export default function AttackSurface() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <ShieldIcon className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-primary mb-2">Attack Surface</h1>
            <p className="text-base-content/70 text-lg">Visualize and analyze your organization's attack surface</p>
          </div>
        </div>
      </div>

      {/* Coming Soon Card */}
      <div className="card bg-base-100 shadow border border-base-300">
        <div className="card-body">
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="p-4 bg-warning/10 rounded-full mb-6">
              <AlertTriangleIcon className="h-12 w-12 text-warning" />
            </div>
            <h2 className="text-2xl font-bold text-base-content mb-4">Coming Soon</h2>
            <p className="text-base-content/70 text-lg mb-8 max-w-2xl leading-relaxed">
              The Attack Surface module is currently under development. This feature will provide comprehensive visualization and analysis of your organization's attack surface,
              helping you understand potential entry points and exposure risks.
            </p>

            {/* Feature Preview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl">
              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <NetworkIcon className="h-8 w-8 text-success mb-3" />
                  <h3 className="font-semibold text-base-content">Surface Mapping</h3>
                  <p className="text-sm text-base-content/60">Map and visualize your digital attack surface</p>
                </div>
              </div>

              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <EyeIcon className="h-8 w-8 text-info mb-3" />
                  <h3 className="font-semibold text-base-content">Exposure Analysis</h3>
                  <p className="text-sm text-base-content/60">Analyze potential exposure points and risks</p>
                </div>
              </div>

              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <ShieldIcon className="h-8 w-8 text-secondary mb-3" />
                  <h3 className="font-semibold text-base-content">Risk Assessment</h3>
                  <p className="text-sm text-base-content/60">Assess and prioritize attack surface risks</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
