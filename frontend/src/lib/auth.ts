import { alovaClient } from "./api-client";

export interface AuthResponse {
  token: string;
  message: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface JWTPayload {
  user_id: string;
  exp: number;
  iat: number;
  nbf: number;
  iss: string;
  sub: string;
}

// Authentication service
export const authService = {
  // Authenticate with password and get JWT token
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await alovaClient.Post<AuthResponse>("/auth", {
      email,
      password,
    });

    // Store token in localStorage for persistence
    if (response.token) {
      localStorage.setItem("jwt_token", response.token);
    }

    return response;
  },

  // Get stored JWT token
  getToken(): string | null {
    return localStorage.getItem("jwt_token");
  },

  // Decode JWT token payload (without verification - for client-side expiry check only)
  decodeToken(token: string): JWTPayload | null {
    try {
      const parts = token.split(".");
      if (parts.length !== 3) return null;

      const payload = parts[1];
      if (!payload) return null;

      const decoded = JSON.parse(atob(payload.replace(/-/g, "+").replace(/_/g, "/")));
      return decoded;
    } catch (error) {
      console.error("Failed to decode JWT token:", error);
      return null;
    }
  },

  // Check if token is expired (client-side check only)
  isTokenExpired(token: string): boolean {
    const payload = this.decodeToken(token);
    if (!payload || !payload.exp) return true;

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  },

  // Check if user is authenticated with valid, non-expired token
  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    // Check if token is expired (client-side check)
    if (this.isTokenExpired(token)) {
      this.logout(); // Clear expired token
      return false;
    }

    return true;
  },

  // Validate token with backend (for critical operations)
  async validateTokenWithBackend(): Promise<boolean> {
    try {
      const token = this.getToken();
      if (!token) return false;

      // Make a simple authenticated request to validate token
      await alovaClient.Get("/dashboard/metrics");
      return true;
    } catch (_error) {
      // If 401, token is invalid/expired
      this.logout();
      return false;
    }
  },

  // Logout and clear all auth data
  logout(): void {
    localStorage.removeItem("jwt_token");
    localStorage.removeItem("auth");
  },

  // Refresh JWT token
  async refreshToken(): Promise<boolean> {
    try {
      const currentToken = this.getToken();
      if (!currentToken) return false;

      const response = await alovaClient.Post<AuthResponse>(
        "/auth/refresh",
        {},
        {
          headers: {
            Authorization: `Bearer ${currentToken}`,
          },
        },
      );

      if (response.token) {
        localStorage.setItem("jwt_token", response.token);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Token refresh failed:", error);
      this.logout();
      return false;
    }
  },

  // Get authorization header for API requests
  getAuthHeader(): string | null {
    const token = this.getToken();
    return token ? `Bearer ${token}` : null;
  },
};
