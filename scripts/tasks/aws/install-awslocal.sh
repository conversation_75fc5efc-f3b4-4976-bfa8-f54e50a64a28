#!/usr/bin/env bash

# ==============================================================================
# Install awslocal CLI Tool
# ==============================================================================
# This script installs the awslocal CLI tool for interacting with LocalStack

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

main() {
    log_info "Installing awscli-local (awslocal) tool..."
    require_tool "uv"

    if uv tool install awscli-local; then
        log_success "awslocal installed successfully!"
        log_info "You can now use awslocal to interact with LocalStack"
        log_info "Example: awslocal s3 ls"
    else
        fail "Failed to install awslocal"
    fi

    # uv tool install automatically adds tools to PATH, but let's ensure it's there
    if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
        log_info "Adding ~/.local/bin to PATH..."
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
        log_info "Please restart your shell or run 'source ~/.bashrc' to update PATH"
    fi

    log_success "Installation complete!"
}

main "$@"