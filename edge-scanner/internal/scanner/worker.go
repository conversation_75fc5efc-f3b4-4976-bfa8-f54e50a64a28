package scanner

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"

	"github.com/RootEvidence/edge-scanner/internal/nuclei"
)

type ScanTask struct {
	IP         string
	Port       int
	TemplateID string
}

func Worker(id int, resultsDir string, tasks <-chan ScanTask, wg *sync.WaitGroup, saveJobs chan<- string) {
	// defer wg.Done()
	for task := range tasks {
		fmt.Printf("[Worker-%d] Scanning %s:%d with nuclei\n", id, task.IP, task.Port)
		fileName := fmt.Sprintf("%s-%d-%s.json", task.IP, task.Port, task.TemplateID)
		outputPath := filepath.Join(resultsDir, fmt.Sprintf("%s-%d-%s.json", task.IP, task.Port, task.TemplateID))
		log.Printf("[Worker-%d] Generated output path: %s", id, outputPath)
		templateID := task.TemplateID

		if err := nuclei.RunNuclei(task.IP, outputPath, templateID); err != nil {
			log.Printf("[Worker-%d] Nuclei scan failed for %s:%d: %v", id, task.IP, task.Port, err)
		} else {
			fmt.Printf("[Worker-%d] Scan complete: %s\n", id, outputPath)
		}
		fileInfo, err := os.Stat(outputPath)
		if err != nil {
			log.Printf("[Worker-%d] Failed to stat output file: %v", id, err)
			continue
		}
		if fileInfo.Size() == 0 {
			log.Printf("[Worker-%d] Output file is 0 KB, deleting: %s", id, outputPath)
			if err := os.Remove(outputPath); err != nil {
				log.Printf("[Worker-%d] Failed to delete empty result file: %v", id, err)
			}
		} else {
			fmt.Printf("[Worker-%d] Scan complete: %s\n", id, outputPath)
			saveJobs <- fileName
		}

	}
}
