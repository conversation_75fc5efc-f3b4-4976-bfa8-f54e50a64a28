#!/usr/bin/env bash

# ==============================================================================
# Lambda Layer Publishing Script
# ==============================================================================
# This script orchestrates the building of Lambda artifacts and then publishes
# the resulting layer zip files to LocalStack for local development.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

# --- Paths ---
readonly ARTIFACTS_DIR="${PROJECT_ROOT}/build/artifacts"
readonly BUILD_SCRIPT="${PROJECT_ROOT}/scripts/tasks/build/build-lambda-artifacts.sh"

# --- Resource names ---
readonly NUCLEI_LAYER_NAME="${PROJECT_NAME}-${ENVIRONMENT}-nuclei-layer"
readonly TEMPLATES_LAYER_NAME="${PROJECT_NAME}-${ENVIRONMENT}-templates-layer"

check_prerequisites() {
    log_info "Checking prerequisites..."
    require_tool "awslocal"
    require_tool "zip"
    require_tool "curl"

    if [ ! -f "$BUILD_SCRIPT" ]; then
        fail "Main build script not found: $BUILD_SCRIPT"
    fi

    if ! curl -s "${LOCALSTACK_ENDPOINT}/_localstack/health" | grep -q "available"; then
        fail "LocalStack is not running. Please start it with: devbox run localstack"
    fi

    log_success "All prerequisites met"
}

build_artifacts() {
    log_header "Building All Lambda Artifacts"
    if ! bash "$BUILD_SCRIPT"; then
        fail "Lambda artifact build failed. See logs for details."
    fi
    log_success "All Lambda artifacts built successfully."
}

publish_layer() {
    local layer_name="$1"
    local zip_file="$2"
    local description="$3"
    local layer_arn_file="$4"

    log_info "Publishing layer: ${layer_name}"

    if [ ! -f "$zip_file" ]; then
        fail "Layer zip file not found: $zip_file"
    fi

    local layer_arn
    layer_arn=$(awslocal lambda publish-layer-version \
        --layer-name "$layer_name" \
        --description "$description" \
        --zip-file "fileb://${zip_file}" \
        --compatible-runtimes provided.al2 \
        --query 'LayerVersionArn' \
        --output text)

    if [ -n "$layer_arn" ]; then
        log_success "Layer ${layer_name} published: ${layer_arn}"
        echo "$layer_arn" > "$layer_arn_file"
    else
        fail "Failed to publish layer ${layer_name}"
    fi
}

publish_all_layers() {
    log_header "Publishing Lambda Layers to LocalStack"

    mkdir -p "${ARTIFACTS_DIR}"

    publish_layer "$NUCLEI_LAYER_NAME" \
        "${ARTIFACTS_DIR}/nuclei-layer.zip" \
        "Nuclei binary for vulnerability scanning" \
        "${ARTIFACTS_DIR}/nuclei-layer-arn.txt"

    publish_layer "$TEMPLATES_LAYER_NAME" \
        "${ARTIFACTS_DIR}/templates-layer.zip" \
        "Nuclei templates for vulnerability scanning" \
        "${ARTIFACTS_DIR}/templates-layer-arn.txt"

    log_success "All layers published successfully."
}

display_layer_info() {
    log_info "Lambda Layer Information:"
    echo ""

    if [ -f "${ARTIFACTS_DIR}/nuclei-layer-arn.txt" ]; then
        echo "  Nuclei Layer ARN:    $(cat "${ARTIFACTS_DIR}/nuclei-layer-arn.txt")"
    fi

    if [ -f "${ARTIFACTS_DIR}/templates-layer-arn.txt" ]; then
        echo "  Templates Layer ARN: $(cat "${ARTIFACTS_DIR}/templates-layer-arn.txt")"
    fi

    echo ""
    echo "These layers will be automatically used when deploying the Lambda function."
}

main() {
    log_header "Lambda Layer Creation and Publishing"

    check_prerequisites
    build_artifacts
    publish_all_layers
    display_layer_info

    log_success "Lambda layers created and published successfully!"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
