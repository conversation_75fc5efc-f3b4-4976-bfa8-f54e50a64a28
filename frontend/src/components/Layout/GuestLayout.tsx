import type React from "react";
import { cn } from "@/lib/utils";

interface GuestLayoutProps {
  children: React.ReactNode;
  className?: string;
}

const GuestLayout: React.FC<GuestLayoutProps> = ({ children, className }) => {
  return (
    <div
      className={cn(
        "min-h-screen bg-background text-foreground",
        "transition-all duration-page ease-in-out", // Brand timing: 300ms for page transitions
        "antialiased", // Brand typography
        className,
      )}
    >
      {/* Brand gradient overlay for visual interest */}
      <div className="fixed inset-0 bg-gradient-to-br from-lightning-blue/5 to-electric-cyan/5 pointer-events-none" />

      {/* Content container with brand spacing */}
      <div className="relative z-10 min-h-screen">{children}</div>
    </div>
  );
};

export default GuestLayout;
