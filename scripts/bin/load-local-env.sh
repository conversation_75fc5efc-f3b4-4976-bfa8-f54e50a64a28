#!/usr/bin/env bash

# ==============================================================================
# Load Local Development Environment Variables
# ==============================================================================
# This script loads environment variables for local development with LocalStack

set -euo pipefail

if [ -z "$PROJECT_ROOT" ]; then
    # If not sourced, determine PROJECT_ROOT dynamically.
    SCRIPT_DIR_INNER="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "${SCRIPT_DIR_INNER}/../.." && pwd)"
fi

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

load_env_file() {
    local env_file="$1"
    local component="$2"
    
    if [ -f "$env_file" ]; then
        log_info "Loading $component environment variables from $env_file"
        
        while IFS= read -r line || [[ -n "$line" ]]; do
            if [[ ! "$line" =~ ^#.*$ ]] && [[ -n "$line" ]]; then
                export "$line"
                var_name="${line%%=*}"
                log_success "  Loaded: $var_name"
            fi
        done < "$env_file"
    else
        log_warn "$component .env.local file not found: $env_file"
    fi
}

main() {
    log_info "Loading local development environment variables..."
    
    load_env_file "$PROJECT_ROOT/backend/.env.local" "Nuclear Pond"
    load_env_file "$PROJECT_ROOT/lambda-nuclei-scanner/.env.local" "Lambda Nuclei Scanner"
    
    # Source the main development config
    if [ -f "$PROJECT_ROOT/scripts/config/development.conf" ]; then
        log_info "Loading shared development configuration..."
        source "$PROJECT_ROOT/scripts/config/development.conf"
        log_success "Shared configuration loaded."
    else
        log_warn "Shared development configuration not found."
    fi

    # Set common LocalStack variables if not already set
    export LOCAL_DEVELOPMENT="${LOCAL_DEVELOPMENT:-true}"
    export AWS_ENDPOINT_URL="${AWS_ENDPOINT_URL:-http://localhost:4566}"
    export AWS_REGION="${AWS_REGION:-us-east-1}"
    export AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID:-test}"
    export AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY:-test}"

    log_success "Local development environment variables loaded successfully!"
}

main "$@"