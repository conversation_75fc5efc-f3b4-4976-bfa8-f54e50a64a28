#!/usr/bin/env bash

# ==============================================================================
# LocalStack S3 Bucket Inspector
# ==============================================================================
# This script provides utilities for inspecting and managing S3 buckets in LocalStack

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

log_header() {
    log_info "--- S3: $1 ---"
}

# Function to list all buckets
list_buckets() {
    log_header "Listing all S3 buckets"
    
    local buckets
    buckets=$(awslocal s3 ls 2>&1) || fail "Failed to list buckets: $buckets"
    
    if [ -z "$buckets" ]; then
        log_info "No buckets found"
    else
        echo "$buckets"
    fi
}

# Function to list bucket contents
list_bucket_contents() {
    local bucket="$1"
    local prefix="${2:-}"
    
    log_header "Listing contents of bucket: $bucket"
    
    if [ -n "$prefix" ]; then
        log_info "Prefix filter: $prefix"
    fi
    
    local contents
    if [ -n "$prefix" ]; then
        contents=$(awslocal s3 ls "s3://$bucket/$prefix" --recursive 2>&1) || fail "Failed to list bucket contents: $contents"
    else
        contents=$(awslocal s3 ls "s3://$bucket" --recursive 2>&1) || fail "Failed to list bucket contents: $contents"
    fi
    
    if [ -z "$contents" ]; then
        log_info "Bucket is empty"
    else
        echo "$contents"
    fi
}

# Function to display help
display_help() {
    echo "LocalStack S3 Bucket Inspector"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  list               List all buckets"
    echo "  ls <bucket> [prefix] List contents of a bucket with optional prefix"
    echo "  help               Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 ls ${S3_BUCKET}"
    echo "  $0 ls ${S3_BUCKET} templates/"
}

# Main function
main() {
    require_tool "awslocal"
    require_tool "jq"

    local command="${1:-help}"
    
    case "$command" in
        list)
            list_buckets
            ;;
        ls)
            [ $# -lt 2 ] && fail "Bucket name required"
            list_bucket_contents "$2" "${3:-}"
            ;;
        help|--help|-h)
            display_help
            ;;
        *)
            fail "Unknown command: $command"
            ;;
    esac
}

# Run main function with all arguments
main "$@"