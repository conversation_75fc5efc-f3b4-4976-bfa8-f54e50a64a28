import { beforeEach, describe, expect, it, vi } from "vitest";
import { alovaClient } from "@/lib/api-client";
import { templateService } from "./services";

// Mock the alova client
vi.mock("@/lib/api-client", () => ({
  alovaClient: {
    Get: vi.fn(),
  },
}));

describe("templateService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getTemplates", () => {
    it("should create an Alova GET method for the templates API endpoint", () => {
      // Create a spy on alovaClient.Get to verify it's called correctly
      const getSpy = vi.spyOn(alovaClient, "Get");

      // Call the service method
      templateService.getTemplates();

      // Expect that the Get method was called with the correct API endpoint
      expect(getSpy).toHaveBeenCalledWith("/templates");

      // Restore the original method
      getSpy.mockRestore();
    });

    it("should return a method instance that can be used by useRequest", () => {
      // Mock the return value of alovaClient.Get
      const mockMethod = {
        config: {
          url: "/templates",
          method: "GET",
        },
      };
      alovaClient.Get = vi.fn().mockReturnValue(mockMethod);

      const result = templateService.getTemplates();

      // Check that the returned object is the one from alovaClient
      expect(result).toBe(mockMethod);
    });
  });
});
