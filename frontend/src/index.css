/** biome-ignore-all lint/correctness/noUnknownProperty: <theme names> */
@import "tailwindcss";
@plugin "daisyui" {
  themes:
    corporate --default,
    dark;
}

/* Inter font for better typography */
body {
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.5);
}

/* Theme customizations for corporate theme */

@plugin "daisyui/theme" {
  name: "corporate";

  default: true;

  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(93% 0 0);
  --color-base-300: oklch(86% 0 0);
  --color-base-content: oklch(22.389% 0.031 278.072);
  --color-primary: oklch(58% 0.158 241.966);
  --color-primary-content: oklch(100% 0 0);
  --color-secondary: oklch(55% 0.046 257.417);
  --color-secondary-content: oklch(100% 0 0);
  --color-accent: oklch(60% 0.118 184.704);
  --color-accent-content: oklch(100% 0 0);
  --color-neutral: oklch(0% 0 0);
  --color-neutral-content: oklch(100% 0 0);
  --color-info: oklch(60% 0.126 221.723);
  --color-info-content: oklch(100% 0 0);
  --color-success: oklch(69% 0.17 140.47);
  --color-success-content: oklch(100% 0 0);
  --color-warning: oklch(85% 0.199 91.936);
  --color-warning-content: oklch(0% 0 0);
  --color-error: oklch(70% 0.191 22.216);
  --color-error-content: oklch(0% 0 0);
  --radius-selector: 1rem;
  --radius-field: 0.25rem;
  --radius-box: 0.25rem;
  --size-selector: 0.1875rem;
  --size-field: 0.1875rem;
  --border: 0.5px;
  --depth: 0;
  --noise: 1;
}

@plugin "daisyui/theme" {
  name: "dark";
  default: false;
  prefersdark: false;
  color-scheme: "dark";

  --color-base-100: oklch(30.857% 0.023 264.149);
  --color-base-200: oklch(28.036% 0.019 264.182);
  --color-base-300: oklch(26.346% 0.018 262.177);

  --color-base-content: oklch(84.87% 0 0);
  --color-primary: oklch(75.351% 0.138 232.661);
  --color-primary-content: oklch(15.07% 0.027 232.661);
  --color-secondary: oklch(64.092% 0.027 229.389);
  --color-secondary-content: oklch(12.818% 0.005 229.389);
  --color-accent: oklch(67.271% 0.167 35.791);
  --color-accent-content: oklch(13.454% 0.033 35.791);
  --color-neutral: oklch(27.441% 0.013 253.041);
  --color-neutral-content: oklch(85.488% 0.002 253.041);
  --color-info: oklch(62.616% 0.143 240.033);
  --color-info-content: oklch(12.523% 0.028 240.033);
  --color-success: oklch(70.226% 0.094 156.596);
  --color-success-content: oklch(14.045% 0.018 156.596);
  --color-warning: oklch(77.482% 0.115 81.519);
  --color-warning-content: oklch(15.496% 0.023 81.519);
  --color-error: oklch(51.61% 0.146 29.674);
  --color-error-content: oklch(90.322% 0.029 29.674);

  --radius-selector: 1rem;
  --radius-field: 0.25rem;
  --radius-box: 0.25rem;
  --size-selector: 0.1875rem;
  --size-field: 0.1875rem;
  --border: 0.5px;
  --depth: 0;
  --noise: 1;
}
