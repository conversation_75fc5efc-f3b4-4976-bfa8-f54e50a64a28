import { Toaster as Sonner, type ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      className="toaster group"
      richColors
      closeButton
      toastOptions={{
        classNames: {
          toast: "group toast group-[.toaster]:bg-base-100 group-[.toaster]:text-base-content group-[.toaster]:border-base-300 group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-base-content/70",
          actionButton: "group-[.toast]:bg-primary group-[.toast]:text-primary-content",
          cancelButton: "group-[.toast]:bg-base-200 group-[.toast]:text-base-content",
          closeButton: "group-[.toast]:text-base-content/70 hover:group-[.toast]:text-base-content",
          icon: "group-[.toast]:text-base-content",
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
