package lambda

import (
	"encoding/json"
	"strings"
	"testing"
)

func TestLambdaInvoke_TemplateSelection(t *testing.T) {
	// Test that LambdaInvoke properly handles template selections
	invoke := LambdaInvoke{
		Targets:   []string{"example.com"},
		Templates: []string{"CVE-2024-3400", "CVE-2023-22518"},
	}

	// Verify templates are properly set
	if len(invoke.Templates) != 2 {
		t.<PERSON>rrorf("Expected 2 templates, got %d", len(invoke.Templates))
	}

	if invoke.Templates[0] != "CVE-2024-3400" {
		t.<PERSON><PERSON>rf("Expected first template to be CVE-2024-3400, got %s", invoke.Templates[0])
	}

	if invoke.Templates[1] != "CVE-2023-22518" {
		t.<PERSON><PERSON>rf("Expected second template to be CVE-2023-22518, got %s", invoke.Templates[1])
	}
}

func TestLambdaInvoke_BackwardCompatibility(t *testing.T) {
	// Test that LambdaInvoke works without template selections (backward compatibility)
	invoke := LambdaInvoke{
		Targets: []string{"example.com"},
	}

	// Verify templates field is empty but invoke is still valid
	if len(invoke.Templates) != 0 {
		t.Errorf("Expected 0 templates for backward compatibility, got %d", len(invoke.Templates))
	}

	// Verify other fields are properly set
	if len(invoke.Targets) != 1 {
		t.Errorf("Expected 1 target, got %d", len(invoke.Targets))
	}

	if invoke.Targets[0] != "example.com" {
		t.Errorf("Expected target to be example.com, got %s", invoke.Targets[0])
	}
}

func TestLambdaInvoke_JSONSerialization(t *testing.T) {
	// Test JSON serialization with templates
	invoke := LambdaInvoke{
		Targets:   []string{"example.com"},
		Templates: []string{"CVE-2024-3400"},
	}

	// Serialize to JSON
	jsonData, err := json.Marshal(invoke)
	if err != nil {
		t.Fatalf("Failed to marshal LambdaInvoke to JSON: %v", err)
	}

	// Deserialize from JSON
	var deserializedInvoke LambdaInvoke
	err = json.Unmarshal(jsonData, &deserializedInvoke)
	if err != nil {
		t.Fatalf("Failed to unmarshal LambdaInvoke from JSON: %v", err)
	}

	// Verify templates are preserved through serialization
	if len(deserializedInvoke.Templates) != 1 {
		t.Errorf("Expected 1 template after JSON round-trip, got %d", len(deserializedInvoke.Templates))
	}

	if deserializedInvoke.Templates[0] != "CVE-2024-3400" {
		t.Errorf("Expected template CVE-2024-3400 after JSON round-trip, got %s", deserializedInvoke.Templates[0])
	}

	// Verify other fields are preserved
	if deserializedInvoke.Targets[0] != "example.com" {
		t.Errorf("Expected target example.com after JSON round-trip, got %s", deserializedInvoke.Targets[0])
	}
}

func TestLambdaInvoke_JSONSerializationBackwardCompatibility(t *testing.T) {
	// Test JSON serialization without templates (backward compatibility)
	invoke := LambdaInvoke{
		Targets: []string{"example.com"},
	}

	// Serialize to JSON
	jsonData, err := json.Marshal(invoke)
	if err != nil {
		t.Fatalf("Failed to marshal LambdaInvoke to JSON: %v", err)
	}

	// Verify JSON doesn't include Templates field when empty (due to omitempty)
	jsonString := string(jsonData)
	if contains(jsonString, "\"templates\"") {
		t.Errorf("JSON should not contain Templates field when empty due to omitempty tag, got: %s", jsonString)
	}

	// Deserialize from JSON
	var deserializedInvoke LambdaInvoke
	err = json.Unmarshal(jsonData, &deserializedInvoke)
	if err != nil {
		t.Fatalf("Failed to unmarshal LambdaInvoke from JSON: %v", err)
	}

	// Verify templates field is empty
	if len(deserializedInvoke.Templates) != 0 {
		t.Errorf("Expected 0 templates after JSON round-trip for backward compatibility, got %d", len(deserializedInvoke.Templates))
	}
}

func TestLambdaInvoke_TemplateNaming(t *testing.T) {
	// Test that template names are sent as CVE IDs without .yaml extension
	// This matches the expected format that lambda scanner should handle
	invoke := LambdaInvoke{
		Targets:   []string{"example.com"},
		Templates: []string{"CVE-2023-22527", "CVE-2024-51378", "CVE-2024-55591"},
	}

	// Verify templates are stored as CVE IDs (without .yaml extension)
	for _, template := range invoke.Templates {
		if strings.HasSuffix(template, ".yaml") || strings.HasSuffix(template, ".yml") {
			t.Errorf("Template should be CVE ID without extension, got: %s", template)
		}
		if !strings.HasPrefix(template, "CVE-") {
			t.Errorf("Template should start with CVE-, got: %s", template)
		}
	}

	// Serialize to JSON to verify the format sent to lambda
	jsonData, err := json.Marshal(invoke)
	if err != nil {
		t.Fatalf("Failed to marshal LambdaInvoke to JSON: %v", err)
	}

	// Verify JSON contains CVE IDs without extensions
	jsonString := string(jsonData)
	if !contains(jsonString, "CVE-2023-22527") {
		t.Errorf("JSON should contain CVE-2023-22527, got: %s", jsonString)
	}
	if contains(jsonString, "CVE-2023-22527.yaml") {
		t.Errorf("JSON should not contain .yaml extension, got: %s", jsonString)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}

	return false
}
