<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apache Test Server - CVE-2021-41773</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { border-bottom: 1px solid #ccc; padding-bottom: 20px; }
        .vulnerability-info { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .warning { color: #856404; }
        .server-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Apache 2.4.49 Test Server</h1>
        <p><strong>Server:</strong> Apache/2.4.49 (Unix)</p>
    </div>

    <div class="vulnerability-info">
        <h2 class="warning">⚠️ Vulnerability Simulation</h2>
        <p>This server simulates <strong>CVE-2021-41773</strong> - Apache HTTP Server 2.4.49 Path Traversal and Remote Code Execution vulnerability.</p>
        <p>This is a <strong>test target</strong> designed for security scanning demonstrations and should only be used in controlled environments.</p>
    </div>

    <div class="server-info">
        <h3>Server Information</h3>
        <ul>
            <li><strong>Software:</strong> Apache HTTP Server</li>
            <li><strong>Version:</strong> 2.4.49</li>
            <li><strong>Platform:</strong> Unix</li>
            <li><strong>Purpose:</strong> Vulnerability Testing Target</li>
        </ul>
    </div>

    <div>
        <h3>Available Endpoints</h3>
        <ul>
            <li><code>/</code> - This index page</li>
            <li><code>/icons/</code> - Icons directory (vulnerable to path traversal)</li>
            <li><code>/cgi-bin/</code> - CGI directory (vulnerable to path traversal and RCE)</li>
        </ul>
    </div>

    <div class="vulnerability-info">
        <h3>Simulated Vulnerabilities</h3>
        <p><strong>Path Traversal:</strong> Attempts to access files outside the web root using encoded path traversal sequences.</p>
        <p><strong>Remote Code Execution:</strong> POST requests to CGI paths with traversal sequences may execute commands.</p>
        <p><strong>Note:</strong> All responses are simulated and no actual system files are accessed or commands executed.</p>
    </div>

    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ccc; color: #666;">
        <p><small>FastScan Test Target - CVE-2021-41773 Simulation</small></p>
    </footer>
</body>
</html>