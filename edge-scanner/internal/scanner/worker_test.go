package scanner

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"
)

func TestWorker(t *testing.T) {
	// Create a temporary directory for test results
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a task channel
	tasks := make(chan ScanTask, 3)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send test tasks
	tasks <- ScanTask{IP: "***********", Port: 80}
	tasks <- ScanTask{IP: "***********", Port: 443}
	tasks <- ScanTask{IP: "***********", Port: 8080}

	// Close the channel to signal completion
	close(tasks)

	// Wait for worker to finish
	wg.Wait()

	// Check that output files were created (even if nuclei fails, the worker should create the files)
	expectedFiles := []string{
		"***********-80.json",
		"***********-443.json",
		"***********-8080.json",
	}

	for _, filename := range expectedFiles {
		filePath := filepath.Join(tempDir, filename)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			t.Errorf("Expected output file %s was not created", filePath)
		}
	}
}

func TestWorkerWithEmptyTaskChannel(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask)
	var wg sync.WaitGroup

	// Start a worker with no tasks
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Close the channel immediately
	close(tasks)

	// Wait for worker to finish
	wg.Wait()

	// Should complete without error
}

func TestWorkerWithInvalidIP(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with invalid IP
	tasks <- ScanTask{IP: "invalid-ip", Port: 80}

	close(tasks)
	wg.Wait()

	// Should still create the output file (nuclei will handle the error)
	filePath := filepath.Join(tempDir, "invalid-ip-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithNegativePort(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with negative port
	tasks <- ScanTask{IP: "***********", Port: -1}

	close(tasks)
	wg.Wait()

	// Should still create the output file
	filePath := filepath.Join(tempDir, "***********--1.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithSpecialCharacters(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with special characters in IP
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "***********-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerConcurrentExecution(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 10)
	var wg sync.WaitGroup

	// Start multiple workers
	workerCount := 3
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go Worker(i, tempDir, tasks, &wg)
	}

	// Send tasks to all workers
	for i := 0; i < 15; i++ {
		tasks <- ScanTask{IP: "***********", Port: 80 + i}
	}

	close(tasks)
	wg.Wait()

	// Check that all expected files were created
	for i := 0; i < 15; i++ {
		filename := filepath.Join(tempDir, fmt.Sprintf("***********-%d.json", 80+i))
		if _, err := os.Stat(filename); os.IsNotExist(err) {
			t.Errorf("Expected output file %s was not created", filename)
		}
	}
}

func TestWorkerWithNonExistentDirectory(t *testing.T) {
	// Use a directory that doesn't exist
	nonExistentDir := "/tmp/non-existent-directory-for-testing"

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, nonExistentDir, tasks, &wg)

	// Send a task
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should complete without panic (nuclei will handle the error)
}

func TestWorkerWithLargePortNumber(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with large port number
	tasks <- ScanTask{IP: "***********", Port: 65535}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "***********-65535.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithZeroPort(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with zero port
	tasks <- ScanTask{IP: "***********", Port: 0}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "***********-0.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerTimeout(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start a worker
	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)

	// Wait for worker to finish with timeout
	done := make(chan bool)
	go func() {
		wg.Wait()
		done <- true
	}()

	select {
	case <-done:
		// Worker completed successfully
	case <-time.After(30 * time.Second):
		t.Error("Worker did not complete within 30 seconds")
	}
}

func TestScanTaskStruct(t *testing.T) {
	// Test the ScanTask struct
	task := ScanTask{
		IP:   "***********",
		Port: 80,
	}

	if task.IP != "***********" {
		t.Errorf("Expected IP '***********', got '%s'", task.IP)
	}

	if task.Port != 80 {
		t.Errorf("Expected Port 80, got %d", task.Port)
	}
}

func BenchmarkWorker(b *testing.B) {
	tempDir, err := os.MkdirTemp("", "scanner_benchmark")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tasks := make(chan ScanTask, 1)
		var wg sync.WaitGroup

		wg.Add(1)
		go Worker(1, tempDir, tasks, &wg)

		tasks <- ScanTask{IP: "***********", Port: 80}
		close(tasks)
		wg.Wait()
	}
}

// New comprehensive test cases

func TestWorkerWithEmptyIP(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with empty IP
	tasks <- ScanTask{IP: "", Port: 80}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithIPv6Address(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with IPv6 address
	tasks <- ScanTask{IP: "2001:db8::1", Port: 80}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "2001:db8::1-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithLocalhost(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with localhost
	tasks <- ScanTask{IP: "localhost", Port: 8080}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "localhost-8080.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithDomainName(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with domain name
	tasks <- ScanTask{IP: "example.com", Port: 443}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "example.com-443.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithSpecialCharactersInIP(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with special characters in IP
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "***********-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithVeryLongIP(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with very long IP
	longIP := "***********.very.long.domain.name.that.exceeds.normal.length"
	tasks <- ScanTask{IP: longIP, Port: 80}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, longIP+"-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithUnicodeCharacters(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task with unicode characters in IP
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should create the output file
	filePath := filepath.Join(tempDir, "***********-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}
}

func TestWorkerWithMultipleWorkersAndSingleTask(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	// Start multiple workers but only one task
	workerCount := 5
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go Worker(i, tempDir, tasks, &wg)
	}

	// Send only one task
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should create exactly one output file
	filePath := filepath.Join(tempDir, "***********-80.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", filePath)
	}

	// Count files to ensure only one was created
	files, err := os.ReadDir(tempDir)
	if err != nil {
		t.Fatalf("Failed to read directory: %v", err)
	}

	if len(files) != 1 {
		t.Errorf("Expected exactly 1 file, got %d", len(files))
	}
}

func TestWorkerWithNilWaitGroup(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 1)

	// This should panic, so we need to recover
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic when passing nil WaitGroup")
		}
	}()

	// Start a worker with nil WaitGroup
	go Worker(1, tempDir, tasks, nil)

	// Send a task
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
}

func TestWorkerWithNilTaskChannel(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	var wg sync.WaitGroup

	// This should panic, so we need to recover
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic when passing nil task channel")
		}
	}()

	// Start a worker with nil task channel
	wg.Add(1)
	go Worker(1, tempDir, nil, &wg)

	wg.Wait()
}

func TestWorkerWithReadOnlyDirectory(t *testing.T) {
	// Create a temporary directory
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Make the directory read-only
	err = os.Chmod(tempDir, 0444)
	if err != nil {
		t.Fatalf("Failed to make directory read-only: %v", err)
	}

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should complete without panic (nuclei will handle the error)
}

func TestWorkerWithExistingFile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create an existing file with the same name
	existingFile := filepath.Join(tempDir, "***********-80.json")
	err = os.WriteFile(existingFile, []byte("existing content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create existing file: %v", err)
	}

	tasks := make(chan ScanTask, 1)
	var wg sync.WaitGroup

	wg.Add(1)
	go Worker(1, tempDir, tasks, &wg)

	// Send a task that will overwrite the existing file
	tasks <- ScanTask{IP: "***********", Port: 80}

	close(tasks)
	wg.Wait()

	// Should still create/overwrite the output file
	if _, err := os.Stat(existingFile); os.IsNotExist(err) {
		t.Errorf("Expected output file %s was not created", existingFile)
	}
}

func TestWorkerStressTest(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "scanner_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tasks := make(chan ScanTask, 100)
	var wg sync.WaitGroup

	// Start multiple workers
	workerCount := 10
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go Worker(i, tempDir, tasks, &wg)
	}

	// Send many tasks
	taskCount := 50
	for i := 0; i < taskCount; i++ {
		tasks <- ScanTask{IP: fmt.Sprintf("192.168.1.%d", i+1), Port: 80 + i}
	}

	close(tasks)
	wg.Wait()

	// Check that all expected files were created
	for i := 0; i < taskCount; i++ {
		filename := filepath.Join(tempDir, fmt.Sprintf("192.168.1.%d-%d.json", i+1, 80+i))
		if _, err := os.Stat(filename); os.IsNotExist(err) {
			t.Errorf("Expected output file %s was not created", filename)
		}
	}
}

func BenchmarkWorkerConcurrent(b *testing.B) {
	tempDir, err := os.MkdirTemp("", "scanner_benchmark")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tasks := make(chan ScanTask, 10)
		var wg sync.WaitGroup

		// Start multiple workers
		workerCount := 5
		for j := 0; j < workerCount; j++ {
			wg.Add(1)
			go Worker(j, tempDir, tasks, &wg)
		}

		// Send tasks
		for j := 0; j < 10; j++ {
			tasks <- ScanTask{IP: fmt.Sprintf("192.168.1.%d", j+1), Port: 80 + j}
		}

		close(tasks)
		wg.Wait()
	}
}

func BenchmarkWorkerSingleTask(b *testing.B) {
	tempDir, err := os.MkdirTemp("", "scanner_benchmark")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tasks := make(chan ScanTask, 1)
		var wg sync.WaitGroup

		wg.Add(1)
		go Worker(1, tempDir, tasks, &wg)

		tasks <- ScanTask{IP: "***********", Port: 80}
		close(tasks)
		wg.Wait()
	}
}
