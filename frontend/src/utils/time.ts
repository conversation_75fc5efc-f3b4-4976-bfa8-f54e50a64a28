import { useEffect, useState } from "react";

/**
 * Formats a duration in milliseconds to a human-readable string
 * @param durationMs Duration in milliseconds
 * @param options Formatting options
 */
export const formatDuration = (
  durationMs: number,
  options: {
    showSeconds?: boolean;
    short?: boolean;
    maxUnits?: number;
  } = {}
): string => {
  const { showSeconds = true, short = false, maxUnits = 3 } = options;

  if (durationMs < 0) return "0s";

  const seconds = Math.floor(durationMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days}${short ? "d" : ` day${days !== 1 ? "s" : ""}`}`);
  }

  if (hours % 24 > 0 && parts.length < maxUnits) {
    parts.push(`${hours % 24}${short ? "h" : ` hour${hours % 24 !== 1 ? "s" : ""}`}`);
  }

  if (minutes % 60 > 0 && parts.length < maxUnits) {
    parts.push(`${minutes % 60}${short ? "m" : ` minute${minutes % 60 !== 1 ? "s" : ""}`}`);
  }

  if (showSeconds && seconds % 60 > 0 && parts.length < maxUnits) {
    parts.push(`${seconds % 60}${short ? "s" : ` second${seconds % 60 !== 1 ? "s" : ""}`}`);
  }

  if (parts.length === 0) {
    return showSeconds ? (short ? "0s" : "0 seconds") : short ? "0m" : "0 minutes";
  }

  return short ? parts.join(" ") : parts.join(", ");
};

/**
 * Gets a performance indicator based on duration and target count
 */
export const getDurationPerformanceIndicator = (
  durationMs: number,
  targetCount = 1
): {
  label: string;
  color: string;
  icon: "🚀" | "⚡" | "✅" | "⏳" | "🐌";
} => {
  const avgTimePerTarget = durationMs / targetCount;

  // Performance thresholds (milliseconds per target)
  if (avgTimePerTarget < 5000) {
    return { label: "Lightning Fast", color: "text-green-600", icon: "🚀" };
  }
  if (avgTimePerTarget < 15000) {
    return { label: "Fast", color: "text-green-500", icon: "⚡" };
  }
  if (avgTimePerTarget < 30000) {
    return { label: "Normal", color: "text-blue-500", icon: "✅" };
  }
  if (avgTimePerTarget < 60000) {
    return { label: "Slow", color: "text-yellow-500", icon: "⏳" };
  }
  return { label: "Very Slow", color: "text-red-500", icon: "🐌" };
};

/**
 * Hook for real-time duration tracking
 */
export const useRealTimeDuration = (startTime?: string, endTime?: string) => {
  const [currentTime, setCurrentTime] = useState(Date.now());

  useEffect(() => {
    if (!startTime || endTime) return;

    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, endTime]);

  if (!startTime) return 0;

  const start = new Date(startTime).getTime();
  const end = endTime ? new Date(endTime).getTime() : currentTime;

  return Math.max(0, end - start);
};

/**
 * Estimates completion time based on current progress
 */
export const estimateCompletion = (
  startTime: string,
  progress: number // 0-1
): string | null => {
  if (progress <= 0 || progress >= 1) return null;

  const elapsed = Date.now() - new Date(startTime).getTime();
  const estimated = elapsed / progress - elapsed;

  return formatDuration(estimated, { short: true, maxUnits: 2 });
};
