package nuclei

import (
	"bufio"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"main/internal/types"
	"os"
	"strings"
)

// ProcessOutput returns JSON content or base64-encoded CLI output based on mode.
func ProcessOutput(result *types.NucleiResult, outputMode string) (string, string) {
	switch outputMode {
	case types.OutputModeJSON:
		return ProcessJSONOutput(result)
	default:
		return base64.StdEncoding.EncodeToString([]byte(result.Output)), ""
	}
}

// ProcessJSONOutput reads the output file and returns its content or an empty array.
func ProcessJSONOutput(result *types.NucleiResult) (string, string) {
	if _, err := os.Stat(result.OutputFile); os.IsNotExist(err) {
		return "[]", ""
	}

	content, err := os.ReadFile(result.OutputFile)
	if err != nil {
		return "Error reading scan output file", err.Error()
	}

	if len(content) == 0 {
		return "[]", ""
	}

	return string(content), ""
}

// ParseJSONFindings parses NDJSON lines from the output file into a slice of JSON objects.
func ParseJSONFindings(scanOutputFile string) ([]any, error) {
	stat, err := os.Stat(scanOutputFile)
	if err != nil {
		if os.IsNotExist(err) {
			log.Printf("Output file %s does not exist", scanOutputFile)

			return []any{}, nil
		}

		return nil, fmt.Errorf("error accessing file %s: %w", scanOutputFile, err)
	}

	if stat.Size() == 0 {
		log.Printf("Output file %s is empty (no findings)", scanOutputFile)

		return []any{}, nil
	}

	file, err := os.Open(scanOutputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %w", scanOutputFile, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)

	var findings []any
	lineNumber := 0
	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())

		if line == "" {
			continue
		}

		var data any
		if err := json.Unmarshal([]byte(line), &data); err != nil {
			log.Printf("Skipping non-JSON line %d in %s: %v (line: %s)", lineNumber, scanOutputFile, err, line)

			continue
		}
		findings = append(findings, data)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file %s: %w", scanOutputFile, err)
	}

	log.Printf("Successfully parsed %d findings from %s", len(findings), scanOutputFile)

	return findings, nil
}
