# ==============================================================================
# NUCLEI LAMBDA MODULE
# ==============================================================================
# This module deploys the Nuclei scanner Lambda function with all required
# infrastructure including S3 bucket, Lambda layers, IAM roles, and artifacts.

# Data sources for AWS information
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# ==============================================================================
# LAMBDA FUNCTION AND LAYERS
# ==============================================================================

# Main Nuclei scanner Lambda function
# tfsec:ignore:aws-lambda-enable-tracing
resource "aws_lambda_function" "nuclei_function" {
  depends_on = [
    aws_lambda_layer_version.nuclei_layer,
    aws_lambda_layer_version.templates_layer,
    null_resource.build_artifacts,
    data.local_file.lambda_function
  ]

  filename      = data.local_file.lambda_function.filename
  function_name = "${var.project_name}-nuclei-function"

  role   = aws_iam_role.lambda_role.arn
  # Layer order matters - nuclei binary layer should be first to ensure proper mounting
  layers = [
    aws_lambda_layer_version.nuclei_layer.arn,
    aws_lambda_layer_version.templates_layer.arn,
  ]

  handler     = "bootstrap"
  runtime     = var.lambda_runtime
  timeout     = var.nuclei_timeout
  memory_size = var.memory_size

  # Reserved concurrency configuration
  reserved_concurrent_executions = var.reserved_concurrent_executions >= 0 ? var.reserved_concurrent_executions : null

  source_code_hash = data.local_file.lambda_function.content_base64sha256

  # VPC configuration for network isolation (optional)
  dynamic "vpc_config" {
    for_each = var.enable_vpc_isolation && length(var.private_subnet_ids) > 0 ? [1] : []
    content {
      subnet_ids         = var.private_subnet_ids
      security_group_ids = var.enable_vpc_isolation ? [aws_security_group.lambda_sg[0].id] : []
    }
  }

  environment {
    variables = {
      "BUCKET_NAME" = aws_s3_bucket.artifacts_bucket.id
    }
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-function"
    Component   = "nuclei-lambda"
    Type        = "compute"
    LayerCount  = "3"
  })
}

# Lambda alias for versioning
resource "aws_lambda_alias" "nuclei_alias" {
  name             = var.project_name
  description      = "Nuclei scanner lambda function"
  function_name    = aws_lambda_function.nuclei_function.arn
  function_version = "$LATEST"
}

# Layer to run nuclei in lambda
resource "aws_lambda_layer_version" "nuclei_layer" {
  depends_on = [aws_s3_object.upload_nuclei]
  
  layer_name          = "${var.project_name}-nuclei-layer"
  s3_bucket           = aws_s3_bucket.artifacts_bucket.id
  s3_key              = "nuclei-layer.zip"
  compatible_runtimes = ["provided.al2"]
}

# Layer to have nuclei templates
resource "aws_lambda_layer_version" "templates_layer" {
  depends_on = [aws_s3_object.upload_templates]
  
  layer_name          = "${var.project_name}-templates-layer"
  s3_bucket           = aws_s3_bucket.artifacts_bucket.id
  s3_key              = "templates-layer.zip"
  compatible_runtimes = ["provided.al2"]
}

