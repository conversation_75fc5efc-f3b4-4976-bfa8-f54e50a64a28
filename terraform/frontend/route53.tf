# ==============================================================================
# MANUAL DNS MANAGEMENT APPROACH
# ==============================================================================
# 
# Route53 automation has been removed to support manual DNS management.
# This approach provides flexibility to work with any DNS provider while
# maintaining optimal performance through ALIAS records when available.
# 
# RECOMMENDED APPROACH: Use ALIAS records for better performance
# FALLBACK APPROACH: Use CNAME records for universal compatibility
# 
# When using a custom domain (frontend_domain_name), you need to:
# 
# 1. Create an ACM certificate (handled automatically by Terraform)
# 2. Manually validate the certificate by adding DNS records to your domain
# 3. Manually create an ALIAS or CNAME record pointing your domain to CloudFront
# 
# STEP-BY-STEP INSTRUCTIONS:
# 
# 1. After running `terraform apply`, get the required information:
#    terraform output cloudfront_domain_name_for_dns
#    terraform output acm_certificate_validation_records
# 
# 2. Add certificate validation records to your DNS provider:
#    - Create CNAME records as shown in the output above
#    - Wait for validation to complete (usually 5-10 minutes)
# 
# 3. Create the main domain record (CHOOSE ONE):
# 
#    OPTION A - ALIAS Record (RECOMMENDED for Route53):
#    - Type: A (ALIAS)
#    - Name: yourdomain.com (or subdomain)
#    - Alias Target: CloudFront distribution domain
#    - Evaluate Target Health: No
#    - Benefits: Faster DNS resolution, supports root domains, cost-efficient
# 
#    OPTION B - CNAME Record (FALLBACK for other DNS providers):
#    - Type: CNAME
#    - Name: subdomain.yourdomain.com (subdomains only)
#    - Value: CloudFront distribution domain
#    - TTL: 300 seconds (recommended)
#    - Limitation: Cannot be used for root domains
# 
# EXAMPLE DNS RECORDS:
# 
# Certificate validation (temporary - remove after validation):
# _abc123def456.yourdomain.com CNAME _xyz789abc123.acm-validations.aws.
# 
# Main domain record (permanent):
# Route53 ALIAS (recommended):
#   yourdomain.com A (ALIAS) d1234567890.cloudfront.net
# 
# Other providers CNAME (fallback):
#   www.yourdomain.com CNAME d1234567890.cloudfront.net
# 
# ==============================================================================

# Output the CloudFront domain name for manual DNS setup
output "cloudfront_domain_name_for_dns" {
  description = "CloudFront domain name to use for manual DNS ALIAS record (recommended) or CNAME record (fallback)"
  value       = var.enable_cloudfront && length(aws_cloudfront_distribution.frontend) > 0 ? aws_cloudfront_distribution.frontend[0].domain_name : null
}

# Output ACM certificate validation records for manual DNS setup
output "acm_certificate_validation_records" {
  description = "ACM certificate validation records to add to your DNS provider"
  value = var.frontend_domain_name != "" && var.enable_cloudfront && length(aws_acm_certificate.frontend) > 0 ? {
    for dvo in aws_acm_certificate.frontend[0].domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      value  = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  } : {}
}

