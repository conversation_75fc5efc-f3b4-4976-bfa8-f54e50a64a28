import { Menu } from "lucide-react";
import ModeToggle from "./ModeToggle";

const Header = () => {
  return (
    <header className="navbar bg-base-100 border-b border-base-300 px-4 h-16">
      <div className="flex-none lg:hidden">
        <label htmlFor="drawer-toggle" className="btn btn-square btn-ghost" aria-label="Toggle sidebar">
          <Menu className="h-5 w-5" />
        </label>
      </div>
      <div className="flex-1 px-4"></div>
      <div className="flex-none">
        <ModeToggle />
      </div>
    </header>
  );
};

export default Header;
