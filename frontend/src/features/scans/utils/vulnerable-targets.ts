import type { VulnerableTargetData } from "../types";

/**
 * Calculate vulnerability count for a target
 * Returns 1 for targets with empty arrays (indicating vulnerability detected but details not populated)
 */
export const calculateVulnerabilityCount = (templateIds: string[]): number => {
  if (!Array.isArray(templateIds)) return 0;
  // If we have template IDs, return the count
  if (templateIds.length > 0) return templateIds.length;
  // If we have an empty array but the target is in vulnerable_targets, assume at least 1 vulnerability
  return 1;
};

/**
 * Transform vulnerable targets map to display array
 * Filters out invalid entries and sorts by severity and vulnerability count
 * Note: Includes targets with empty template arrays as they may still be vulnerable
 */
export const transformVulnerableTargets = (vulnerableTargets: Record<string, string[]>): VulnerableTargetData[] => {
  if (!vulnerableTargets || typeof vulnerableTargets !== "object") {
    return [];
  }

  return Object.entries(vulnerableTargets)
    .filter(([target, templateIds]) => target && Array.isArray(templateIds))
    .map(([target, templateIds]) => ({
      target,
      templateIds,
      vulnerabilityCount: calculateVulnerabilityCount(templateIds),
    }));
};

/**
 * Format table display limits and messaging
 */
export interface TableDisplayConfig {
  displayLimit: number;
  targetsToShow: VulnerableTargetData[];
  hasMoreTargets: boolean;
  remainingCount: number;
  showTopMessage: boolean;
  topMessageText: string;
  moreTargetsText: string;
}

/**
 * Calculate table display configuration for vulnerable targets
 */
export const calculateTableDisplayConfig = (transformedTargets: VulnerableTargetData[], totalVulnerableTargets: number, displayLimit: number = 10): TableDisplayConfig => {
  const targetsToShow = transformedTargets.slice(0, displayLimit);
  const hasMoreTargets = totalVulnerableTargets > displayLimit;
  const remainingCount = Math.max(0, totalVulnerableTargets - displayLimit);

  // Show "top X of Y" message for lists with more than 10 items but under the limit
  const showTopMessage = !hasMoreTargets && transformedTargets.length > 10;
  const topMessageText = `Showing top ${displayLimit} vulnerable targets`;

  const moreTargetsText = remainingCount > 0 ? `Showing top ${displayLimit} of vulnerable targets` : "";

  return {
    displayLimit,
    targetsToShow,
    hasMoreTargets,
    remainingCount,
    showTopMessage,
    topMessageText,
    moreTargetsText,
  };
};

/**
 * Format numbers with locale-specific formatting
 */
export const formatCount = (count: number): string => {
  return count.toLocaleString();
};

/**
 * Validate vulnerable targets data structure
 */
export const isValidVulnerableTargetsData = (vulnerableTargets: unknown): vulnerableTargets is Record<string, string[]> => {
  if (!vulnerableTargets || typeof vulnerableTargets !== "object") {
    return false;
  }

  const targets = vulnerableTargets as Record<string, unknown>;

  return Object.entries(targets).every(([key, value]) => typeof key === "string" && Array.isArray(value) && value.every((item) => typeof item === "string"));
};
