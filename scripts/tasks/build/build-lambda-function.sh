#!/bin/bash

# Build Lambda Function
# Compiles Go code for AWS Lambda and packages it into a deployment-ready zip file

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT
LAMBDA_SOURCE_DIR="${PROJECT_ROOT}/lambda-nuclei-scanner"
GO_ARCH="${GO_ARCH:-amd64}"
GO_OS="${GO_OS:-linux}"

source "${PROJECT_ROOT}/scripts/lib/logging.sh"

# Validate input parameters
if [[ $# -ne 1 ]]; then
    log_error "Usage: $0 <output_directory>"
    log_error "Example: $0 /path/to/artifacts"
    exit 1
fi

OUTPUT_DIR="$1"
TEMP_DIR=$(mktemp -d)
BUILD_DIR="${TEMP_DIR}/lambda-build"

# Cleanup function
cleanup() {
    if [[ -d "${TEMP_DIR}" ]]; then
        rm -rf "${TEMP_DIR}"
    fi
}

trap cleanup EXIT

# Validate source directory
validate_source_directory() {
    log_info "Validating Lambda source directory..."
    
    if [[ ! -d "${LAMBDA_SOURCE_DIR}" ]]; then
        log_error "Lambda source directory not found: ${LAMBDA_SOURCE_DIR}"
        return 1
    fi
    
    # Check for go.mod file
    if [[ ! -f "${LAMBDA_SOURCE_DIR}/go.mod" ]]; then
        log_error "go.mod file not found in: ${LAMBDA_SOURCE_DIR}"
        return 1
    fi
    
    # Check for lambda main.go
    if [[ ! -f "${LAMBDA_SOURCE_DIR}/cmd/lambda/main.go" ]]; then
        log_error "Lambda main.go not found: ${LAMBDA_SOURCE_DIR}/cmd/lambda/main.go"
        return 1
    fi
    
    log_success "Lambda source directory validated"
}

# Validate Go environment
validate_go_environment() {
    log_info "Validating Go environment..."
    
    # Check if Go is available (using devbox)
    if ! command -v go >/dev/null 2>&1; then
        log_error "Go not found in PATH. Make sure to run with devbox: devbox run -- $0"
        return 1
    fi
    
    # Get Go version
    local go_version=$(go version 2>/dev/null || echo "unknown")
    log_info "Go version: ${go_version}"
    
    # Check if we can access the lambda source directory
    cd "${LAMBDA_SOURCE_DIR}" || {
        log_error "Cannot access Lambda source directory: ${LAMBDA_SOURCE_DIR}"
        return 1
    }
    
    # Verify go.mod is valid
    if ! go mod verify >/dev/null 2>&1; then
        log_warning "go.mod verification failed, attempting to tidy..."
        if ! go mod tidy; then
            log_error "Failed to tidy Go modules"
            return 1
        fi
    fi
    
    log_success "Go environment validated"
}

# Build Lambda binary
build_lambda_binary() {
    log_info "Building Lambda binary..."
    log_info "Target: ${GO_OS}/${GO_ARCH}"
    
    # Create build directory
    mkdir -p "${BUILD_DIR}"
    
    # Change to lambda source directory
    cd "${LAMBDA_SOURCE_DIR}"
    
    # Set environment variables for cross-compilation
    export GOOS="${GO_OS}"
    export GOARCH="${GO_ARCH}"
    export CGO_ENABLED=0
    
    # Build the binary
    local binary_path="${BUILD_DIR}/bootstrap"
    
    log_info "Compiling Lambda function..."
    if ! go build -ldflags="-s -w" -o "${binary_path}" ./cmd/lambda; then
        log_error "Failed to build Lambda binary"
        return 1
    fi
    
    # Validate binary was created
    if [[ ! -f "${binary_path}" ]]; then
        log_error "Lambda binary not created: ${binary_path}"
        return 1
    fi
    
    # Check binary size
    local binary_size=$(stat -f%z "${binary_path}" 2>/dev/null || stat -c%s "${binary_path}" 2>/dev/null || echo "0")
    local binary_size_mb=$((binary_size / 1024 / 1024))
    
    if [[ "${binary_size}" -eq 0 ]]; then
        log_error "Lambda binary is empty: ${binary_path}"
        return 1
    fi
    
    # Make binary executable
    chmod +x "${binary_path}"
    
    log_success "Lambda binary built successfully (${binary_size_mb}MB)"
}

# Validate binary
validate_binary() {
    log_info "Validating Lambda binary..."
    
    local binary_path="${BUILD_DIR}/bootstrap"
    
    # Check if binary exists and is executable
    if [[ ! -f "${binary_path}" ]]; then
        log_error "Lambda binary not found: ${binary_path}"
        return 1
    fi
    
    if [[ ! -x "${binary_path}" ]]; then
        log_error "Lambda binary is not executable: ${binary_path}"
        return 1
    fi
    
    # Check binary architecture (if file command is available)
    if command -v file >/dev/null 2>&1; then
        local file_info=$(file "${binary_path}")
        log_info "Binary info: ${file_info}"
        
        # Verify it's a Linux binary for Lambda
        if [[ "${GO_OS}" == "linux" ]] && ! echo "${file_info}" | grep -q "ELF"; then
            log_warning "Binary may not be built for Linux (required for Lambda)"
        fi
    fi
    
    # Check binary size constraints (Lambda has limits)
    local binary_size=$(stat -f%z "${binary_path}" 2>/dev/null || stat -c%s "${binary_path}" 2>/dev/null || echo "0")
    local max_size=$((250 * 1024 * 1024)) # 250MB uncompressed limit
    
    if [[ "${binary_size}" -gt "${max_size}" ]]; then
        log_error "Binary size (${binary_size} bytes) exceeds Lambda limit (${max_size} bytes)"
        return 1
    fi
    
    log_success "Binary validation completed"
}

# Create deployment package
create_deployment_package() {
    log_info "Creating Lambda deployment package..."
    
    local output_file="${OUTPUT_DIR}/lambda-function.zip"
    
    # Ensure output directory exists
    mkdir -p "${OUTPUT_DIR}"
    
    # Create zip file from build directory
    cd "${BUILD_DIR}"
    if ! zip -r "${output_file}" . >/dev/null; then
        log_error "Failed to create deployment package: ${output_file}"
        return 1
    fi
    
    # Validate created zip file
    if [[ ! -f "${output_file}" ]]; then
        log_error "Deployment package not created: ${output_file}"
        return 1
    fi
    
    if ! unzip -t "${output_file}" >/dev/null 2>&1; then
        log_error "Created zip file is invalid: ${output_file}"
        return 1
    fi
    
    # Check zip file size
    local zip_size=$(stat -f%z "${output_file}" 2>/dev/null || stat -c%s "${output_file}" 2>/dev/null || echo "0")
    local zip_size_mb=$((zip_size / 1024 / 1024))
    local max_zip_size=$((50 * 1024 * 1024)) # 50MB compressed limit
    
    if [[ "${zip_size}" -gt "${max_zip_size}" ]]; then
        log_error "Deployment package size (${zip_size} bytes) exceeds Lambda limit (${max_zip_size} bytes)"
        return 1
    fi
    
    log_success "Lambda deployment package created: ${output_file} (${zip_size_mb}MB)"
    
    # Show package contents
    log_info "Package contents:"
    unzip -l "${output_file}" | grep -E "^\s*[0-9]+" | while read -r line; do
        echo "  ${line}"
    done
}

# Verify deployment package
verify_deployment_package() {
    log_info "Verifying deployment package..."
    
    # Extract to temporary location and verify structure
    local verify_dir="${TEMP_DIR}/verify"
    mkdir -p "${verify_dir}"
    
    local output_file="${OUTPUT_DIR}/lambda-function.zip"
    if ! unzip -q "${output_file}" -d "${verify_dir}"; then
        log_error "Failed to extract deployment package for verification"
        return 1
    fi
    
    # Check if bootstrap binary exists (required for Lambda)
    if [[ ! -f "${verify_dir}/bootstrap" ]]; then
        log_error "Deployment package does not contain 'bootstrap' binary"
        return 1
    fi
    
    # Check if bootstrap is executable
    if [[ ! -x "${verify_dir}/bootstrap" ]]; then
        log_error "Bootstrap binary is not executable in deployment package"
        return 1
    fi
    
    log_success "Deployment package verification completed"
}

# Clean up build artifacts
cleanup_build() {
    log_info "Cleaning up build artifacts..."
    
    # Reset environment variables
    unset GOOS GOARCH CGO_ENABLED
    
    # Return to original directory
    cd "${PROJECT_ROOT}"
    
    log_success "Build cleanup completed"
}

# Main execution
main() {
    log_info "Building Lambda function..."
    log_info "Source directory: ${LAMBDA_SOURCE_DIR}"
    log_info "Output directory: ${OUTPUT_DIR}"
    log_info "Target architecture: ${GO_OS}/${GO_ARCH}"
    
    # Validate source directory
    if ! validate_source_directory; then
        log_error "Source directory validation failed"
        exit 1
    fi
    
    # Validate Go environment
    if ! validate_go_environment; then
        log_error "Go environment validation failed"
        exit 1
    fi
    
    # Build Lambda binary
    if ! build_lambda_binary; then
        log_error "Failed to build Lambda binary"
        exit 1
    fi
    
    # Validate binary
    if ! validate_binary; then
        log_error "Binary validation failed"
        exit 1
    fi
    
    # Create deployment package
    if ! create_deployment_package; then
        log_error "Failed to create deployment package"
        exit 1
    fi
    
    # Verify deployment package
    if ! verify_deployment_package; then
        log_error "Deployment package verification failed"
        exit 1
    fi
    
    # Clean up
    cleanup_build
    
    log_success "Lambda function build completed successfully!"
}

# Execute main function
main "$@"