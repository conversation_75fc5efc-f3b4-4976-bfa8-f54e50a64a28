#!/usr/bin/env bash

# ==============================================================================
# Checks the health and status of the LocalStack environment.
# ==============================================================================

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
# source "${PROJECT_ROOT}/scripts/lib/aws.sh" # To be created
source "${PROJECT_ROOT}/scripts/config/development.conf"

usage() {
    echo "Usage: $0"
    echo "Checks the health of all LocalStack services and configured resources."
}

# NOTE: These functions will be moved to a dedicated aws.sh library later.

check_docker_status() {
    log_info "Checking Docker Container Status for $1"
    require_tool "docker"
    
    local container_status
    container_status=$(docker ps --filter "name=$1" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "")
    
    if [ -n "$container_status" ] && echo "$container_status" | grep -q "$1"; then
        log_success "✓ Docker container '$1' is running"
        echo "$container_status"
    else
        log_error "✗ Docker container '$1' is not running"
        log_info "Start with: docker-compose up -d localstack"
        return 1
    fi
}

check_localstack_health() {
    log_info "Checking LocalStack Service Health at $1"
    
    if curl -s "${1}/health" >/dev/null 2>&1; then
        local health_response
        health_response=$(curl -s "${1}/_localstack/health")
        
        if echo "$health_response" | grep -q "available"; then
            log_success "✓ LocalStack is running and healthy"
            echo "$health_response" | jq -r '.services | to_entries[] | "  \(.key): \(.value)"' 2>/dev/null || echo "$health_response"
        else
            log_warn "⚠ LocalStack is responding but may not be fully ready"
            echo "$health_response"
        fi
    else
        log_error "✗ LocalStack is not responding at $1"
        return 1
    fi
}

check_s3_bucket_exists() {
    log_info "Checking S3 bucket: $1"
    require_tool "awslocal"
    if awslocal s3 ls "s3://$1" >/dev/null 2>&1; then
        log_success "✓ S3 bucket '$1' is accessible"
    else
        log_error "✗ S3 bucket '$1' is not accessible"
        return 1
    fi
}

check_dynamodb_table_exists() {
    log_info "Checking DynamoDB table: $1"
    require_tool "awslocal"
    if awslocal dynamodb describe-table --table-name "$1" >/dev/null 2>&1; then
        log_success "✓ DynamoDB table '$1' is accessible"
    else
        log_error "✗ DynamoDB table '$1' is not accessible"
        return 1
    fi
}

check_lambda_function_exists() {
    log_info "Checking Lambda function: $1"
    require_tool "awslocal"
    if awslocal lambda get-function --function-name "$1" >/dev/null 2>&1; then
        log_success "✓ Lambda function '$1' is deployed"
    else
        log_warn "⚠ Lambda function '$1' not found"
        return 1
    fi
}

main() {
    log_info "Starting FastScan LocalStack Health Check..."
    require_tool "curl"
    require_tool "jq"
    
    local errors=0

    check_docker_status "fastscan-localstack" || ((errors++))
    check_localstack_health "${LOCALSTACK_ENDPOINT}" || ((errors++))
    check_s3_bucket_exists "${S3_BUCKET}" || ((errors++))
    check_dynamodb_table_exists "${DYNAMODB_TABLE}" || ((errors++))
    check_lambda_function_exists "${LAMBDA_FUNCTION_NAME}" || ((errors++))

    if [ $errors -eq 0 ]; then
        log_success "All health checks passed! ✓"
    else
        log_error "$errors health check(s) failed."
        log_info "Run './scripts/setup-local-development.sh' to fix issues."
        exit 1
    fi
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi