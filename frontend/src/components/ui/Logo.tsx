import type React from "react";
import { cn } from "@/lib/utils";
import { useTheme } from "@/providers/theme-provider";

type Size = "sm" | "md" | "lg" | "xl";

const logoSizes: Record<Size, string> = {
  sm: "h-8", // 32px
  md: "h-12", // 48px
  lg: "h-16", // 64px
  xl: "h-24", // 96px
};

interface LogoProps {
  size?: Size;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = "md", className }) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  return (
    <div className={cn("flex items-center gap-3", className)}>
      <img src="/logo.png" alt="Evidence Logo" className={cn(logoSizes[size], "w-auto transition-all duration-300 ease-in-out")} />
      <img src={isDark ? "/evidence_light.png" : "/evidence_dark.png"} alt="Evidence" className={cn(logoSizes[size], "w-auto transition-all duration-300 ease-in-out")} />
    </div>
  );
};

export default Logo;
