# Outputs for the PoW module
# Note: Many outputs are already defined in their respective .tf files

output "pow_enabled" {
  description = "Whether the PoW infrastructure is enabled"
  value       = var.enable_pow_targets
}

output "pow_domain" {
  description = "The domain name used for PoW targets"
  value       = var.enable_pow_targets ? var.pow_domain_name : null
}

output "pow_summary" {
  description = "Summary of deployed PoW infrastructure"
  value = var.enable_pow_targets ? {
    domain                    = var.pow_domain_name
    alb_dns_name              = aws_lb.pow_alb[0].dns_name
    target_group_arn          = aws_lb_target_group.pow_ec2_tg[0].arn
    zone_id                   = local.pow_zone_id
    instance_count            = 2
    example_target_urls = [
      "http://${var.pow_domain_name}",
      "http://test1.${var.pow_domain_name}",
      "http://test2.${var.pow_domain_name}",
      "http://random-subdomain.${var.pow_domain_name}"
    ]
  } : null
} 