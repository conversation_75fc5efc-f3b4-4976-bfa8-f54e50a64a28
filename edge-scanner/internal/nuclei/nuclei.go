package nuclei

import (
	"context"
	"fmt"
	"os/exec"
	"time"
)

// RunNuclei runs a Nuclei scan on the given IP and port, saving output to outputPath in JSONL format.
func RunNuclei(ip string, outputPath string, templateID string) error {
	var url string
	url = fmt.Sprintf("http://%s", ip)
	// if port == 443 {
	// 	url = fmt.Sprintf("https://%s:%d", ip, port)
	// } else {
	// }

	fmt.Printf("[INFO] Running Nuclei scan for %s with template %s, output: %s\n", url, templateID, outputPath)

	cmd := exec.Command(
		"nuclei",
		"-u", url, // Target URL or IP
		"-jsonl",          // Output results in JSONL format
		"-id", templateID, // Template ID to run
		"-o", outputPath, // Output file path
		"-disable-update-check", // Disable update check
		"-duc",                  // Disable template update checks
		"-ni",                   // Disable interactsh server
		"-disable-clustering",   // Disable request clustering
		"-stats",                // Show runtime scan statistics
		"-si", "5",              // Show statistics every 5 seconds
		"-timeout", "30", // Timeout for each request (seconds)
		"-rl", "150", // Rate limit (requests per second)
		"-c", "25", // Concurrency (parallel requests)
		"-retries", "1", // Retry failed requests once
	)

	// Set a timeout for the entire command
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	cmd = exec.CommandContext(ctx, cmd.Path, cmd.Args[1:]...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("nuclei execution failed: %v, output: %s", err, string(output))
	}
	return nil
}
