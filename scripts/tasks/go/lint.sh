#!/usr/bin/env bash
set -euo pipefail

# Run golangci-lint across all Go modules in the monorepo.

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")"/../../.. && pwd)"
cd "$ROOT_DIR"

FIX_FLAG=""
if [[ "${1:-}" == "--fix" ]]; then
  FIX_FLAG="--fix"
fi

if ! command -v golangci-lint >/dev/null 2>&1; then
  echo "golangci-lint is not available in PATH. Ensure Devbox shell is active (devbox shell)." >&2
  exit 1
fi

echo "🔍 Discovering Go modules..."
mapfile -t MODULE_DIRS < <(find . -name go.mod -not -path '*/node_modules/*' -not -path '*/.git/*' -printf '%h\n' | sort)

if [[ ${#MODULE_DIRS[@]} -eq 0 ]]; then
  echo "No Go modules found."
  exit 0
fi

echo "Found ${#MODULE_DIRS[@]} Go modules:"
printf ' - %s\n' "${MODULE_DIRS[@]}"

EXIT_CODE=0
for MOD_DIR in "${MODULE_DIRS[@]}"; do
  echo "\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
  echo "📦 Linting module: ${MOD_DIR}"
  echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
  pushd "$MOD_DIR" >/dev/null
  # Ensure dependencies for linting are present
  go mod download >/dev/null 2>&1 || true
  if ! golangci-lint run ${FIX_FLAG} ./...; then
    EXIT_CODE=1
  fi
  popd >/dev/null
done

exit $EXIT_CODE


