# Compatible with golangci-lint v1.x
run:
  timeout: 5m
  tests: true
  modules-download-mode: mod
  allow-parallel-runners: true

issues:
  max-issues-per-linter: 0
  max-same-issues: 0
  exclude-use-default: false
  exclude-rules:
    - path: _test\.go
      linters:
        - funlen
        - gocognit
        - cyclop
        - dupl
        - maintidx

linters:
  disable-all: true
  enable:
    - govet
    - staticcheck
    - ineffassign
    - errcheck
    - unused
    - gocyclo
    - goconst
    - unconvert
    - unparam
    - whitespace
    - misspell
    - prealloc
    - copyloopvar
    - errname
    - errorlint
    - nakedret
    - nilerr
    - nlreturn
    - revive
    - gofmt
    - goimports
    - gofumpt
    - nolintlint
    - gocritic
    - gosimple
    - bodyclose
    - exhaustive

    - forcetypeassert
    - makezero
    - predeclared

linters-settings:
  gocyclo:
    min-complexity: 20
  govet:
    enable-all: true
  misspell:
    locale: US
  revive:
    severity: warning
    confidence: 0.8
    rules:
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: error-return
      - name: error-strings
      - name: error-naming
      - name: exported
        arguments: ["checkPrivateReceivers" ]
      - name: if-return
      - name: increment-decrement
      - name: indent-error-flow
      - name: receiver-naming
      - name: time-naming
      - name: var-declaration
      - name: superfluous-else
  staticcheck:
    checks: ["all"]
  gofumpt:
    # Enable stricter formatting (extra spaces, better imports)
    extra-rules: true
  goimports:
    # Group imports: std, third-party, local
    local-prefixes: "github.com/fast-scan"
  whitespace:
    multi-if: false   # don't require \n before "else if"
    multi-func: false # don't require \n before function

service:
  golangci-lint-version: 1.64.x
  prepare:
    - echo "Using golangci-lint $(golangci-lint --version 2>/dev/null | awk '{print $4}')"


