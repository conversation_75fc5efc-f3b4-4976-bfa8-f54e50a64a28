package aws

import (
	"os"
	"testing"
)

func TestNewAWSConfigProduction(t *testing.T) {
	// Clear any existing LOCAL_DEVELOPMENT env var
	os.Unsetenv("LOCAL_DEVELOPMENT")

	// Set required production environment variables
	os.Setenv("AWS_S3_BUCKET", "test-prod-bucket")
	os.Setenv("AWS_DYNAMODB_TABLE", "test-prod-table")
	os.Setenv("AWS_LAMBDA_FUNCTION_NAME", "test-prod-function")
	os.Setenv("AWS_REGION", "us-west-2")

	defer func() {
		os.Unsetenv("AWS_S3_BUCKET")
		os.Unsetenv("AWS_DYNAMODB_TABLE")
		os.Unsetenv("AWS_LAMBDA_FUNCTION_NAME")
		os.Unsetenv("AWS_REGION")
	}()

	config := NewAWSConfig()

	if config.IsLocal {
		t.<PERSON><PERSON>("Expected IsLocal to be false for production mode")
	}

	if config.Endpoint != "" {
		t.<PERSON>rrorf("Expected empty endpoint for production, got %s", config.Endpoint)
	}

	if config.Region != "us-west-2" {
		t.Errorf("Expected region us-west-2, got %s", config.Region)
	}

	if config.S3Bucket != "test-prod-bucket" {
		t.Errorf("Expected S3 bucket test-prod-bucket, got %s", config.S3Bucket)
	}

	if config.DynamoDBTable != "test-prod-table" {
		t.Errorf("Expected DynamoDB table test-prod-table, got %s", config.DynamoDBTable)
	}

	if config.LambdaFunction != "test-prod-function" {
		t.Errorf("Expected Lambda function test-prod-function, got %s", config.LambdaFunction)
	}
}

func TestNewAWSConfigLocal(t *testing.T) {
	// Set LOCAL_DEVELOPMENT to enable local mode
	os.Setenv("LOCAL_DEVELOPMENT", "true")
	os.Setenv("AWS_ENDPOINT_URL", "http://localhost:4566")
	os.Setenv("AWS_REGION", "us-east-1")

	defer func() {
		os.Unsetenv("LOCAL_DEVELOPMENT")
		os.Unsetenv("AWS_ENDPOINT_URL")
		os.Unsetenv("AWS_REGION")
	}()

	config := NewAWSConfig()

	if !config.IsLocal {
		t.Error("Expected IsLocal to be true for local development mode")
	}

	if config.Endpoint != "http://localhost:4566" {
		t.Errorf("Expected endpoint http://localhost:4566, got %s", config.Endpoint)
	}

	if config.Region != "us-east-1" {
		t.Errorf("Expected region us-east-1, got %s", config.Region)
	}

	if config.S3Bucket != "fastscan-local-bucket" {
		t.Errorf("Expected S3 bucket fastscan-local-bucket, got %s", config.S3Bucket)
	}

	if config.DynamoDBTable != "fastscan-local-scans" {
		t.Errorf("Expected DynamoDB table fastscan-local-scans, got %s", config.DynamoDBTable)
	}

	if config.LambdaFunction != "fastscan-nuclei-function" {
		t.Errorf("Expected Lambda function fastscan-nuclei-function, got %s", config.LambdaFunction)
	}
}

func TestCreateSessionProduction(t *testing.T) {
	// Clear any existing LOCAL_DEVELOPMENT env var
	os.Unsetenv("LOCAL_DEVELOPMENT")

	// Set required production environment variables
	os.Setenv("AWS_S3_BUCKET", "test-prod-bucket")
	os.Setenv("AWS_DYNAMODB_TABLE", "test-prod-table")
	os.Setenv("AWS_LAMBDA_FUNCTION_NAME", "test-prod-function")
	os.Setenv("AWS_REGION", "us-west-2")

	defer func() {
		os.Unsetenv("AWS_S3_BUCKET")
		os.Unsetenv("AWS_DYNAMODB_TABLE")
		os.Unsetenv("AWS_LAMBDA_FUNCTION_NAME")
		os.Unsetenv("AWS_REGION")
	}()

	config := NewAWSConfig()
	session, err := config.CreateSession()
	if err != nil {
		t.Errorf("Failed to create AWS session: %v", err)
	}

	if session == nil {
		t.Error("Expected non-nil session")
	}

	// Verify session configuration
	if *session.Config.Region != "us-west-2" {
		t.Errorf("Expected session region us-west-2, got %s", *session.Config.Region)
	}

	// In production mode, endpoint should be nil
	if session.Config.Endpoint != nil {
		t.Errorf("Expected nil endpoint for production session, got %s", *session.Config.Endpoint)
	}
}

func TestCreateSessionLocal(t *testing.T) {
	// Set LOCAL_DEVELOPMENT to enable local mode
	os.Setenv("LOCAL_DEVELOPMENT", "true")
	os.Setenv("AWS_ENDPOINT_URL", "http://localhost:4566")
	os.Setenv("AWS_REGION", "us-east-1")
	os.Setenv("AWS_ACCESS_KEY_ID", "test")
	os.Setenv("AWS_SECRET_ACCESS_KEY", "test")

	defer func() {
		os.Unsetenv("LOCAL_DEVELOPMENT")
		os.Unsetenv("AWS_ENDPOINT_URL")
		os.Unsetenv("AWS_REGION")
		os.Unsetenv("AWS_ACCESS_KEY_ID")
		os.Unsetenv("AWS_SECRET_ACCESS_KEY")
	}()

	config := NewAWSConfig()
	session, err := config.CreateSession()
	if err != nil {
		t.Errorf("Failed to create AWS session: %v", err)
	}

	if session == nil {
		t.Error("Expected non-nil session")
	}

	// Verify session configuration for LocalStack
	if *session.Config.Region != "us-east-1" {
		t.Errorf("Expected session region us-east-1, got %s", *session.Config.Region)
	}

	if session.Config.Endpoint == nil || *session.Config.Endpoint != "http://localhost:4566" {
		t.Errorf("Expected endpoint http://localhost:4566, got %v", session.Config.Endpoint)
	}

	if session.Config.S3ForcePathStyle == nil || !*session.Config.S3ForcePathStyle {
		t.Error("Expected S3ForcePathStyle to be true for LocalStack")
	}

	// Verify credentials are set for LocalStack
	creds, err := session.Config.Credentials.Get()
	if err != nil {
		t.Errorf("Failed to get credentials: %v", err)
	}

	if creds.AccessKeyID != "test" {
		t.Errorf("Expected AccessKeyID 'test', got %s", creds.AccessKeyID)
	}

	if creds.SecretAccessKey != "test" {
		t.Errorf("Expected SecretAccessKey 'test', got %s", creds.SecretAccessKey)
	}
}

func TestGetEnvOrDefault(t *testing.T) {
	// Test with existing environment variable
	os.Setenv("TEST_VAR", "test_value")
	defer os.Unsetenv("TEST_VAR")

	result := getEnvOrDefault("TEST_VAR", "default_value")
	if result != "test_value" {
		t.Errorf("Expected 'test_value', got %s", result)
	}

	// Test with non-existing environment variable
	result = getEnvOrDefault("NON_EXISTING_VAR", "default_value")
	if result != "default_value" {
		t.Errorf("Expected 'default_value', got %s", result)
	}
}
