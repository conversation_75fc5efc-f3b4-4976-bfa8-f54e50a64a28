#!/usr/bin/env python3
"""
Vulnerable Apache 2.4.49 simulator for CVE-2021-41773 testing
Simulates the path traversal and RCE vulnerability for nuclei scanning tests
"""

import http.server
import socketserver
import os
import sys
import urllib.parse
from pathlib import Path

class VulnerableApacheHandler(http.server.BaseHTTPRequestHandler):
    """Handler that simulates Apache 2.4.49 with CVE-2021-41773 vulnerability"""
    
    def end_headers(self):
        # Simulate Apache 2.4.49 headers
        self.send_header('Server', 'Apache/2.4.49 (Unix)')
        self.send_header('X-Powered-By', 'Apache/2.4.49')
        super().end_headers()
    
    def log_message(self, format, *args):
        """Log requests in Apache-like format"""
        print(f"[{self.date_time_string()}] {self.client_address[0]} - {format % args}")
    
    def do_GET(self):
        """Handle GET requests with path traversal vulnerability simulation"""
        self.log_message("GET %s", self.path)
        
        # Decode URL-encoded path
        decoded_path = urllib.parse.unquote(self.path)
        
        # Check for path traversal patterns that match the CVE-2021-41773 template
        if self._is_path_traversal_attempt(decoded_path):
            if '/etc/passwd' in decoded_path:
                self._serve_passwd_file()
                return
        
        # Handle normal requests
        if self.path == '/':
            self._serve_index()
        elif self.path.startswith('/icons/'):
            self._serve_icon()
        else:
            self._serve_404()
    
    def do_POST(self):
        """Handle POST requests with RCE vulnerability simulation"""
        self.log_message("POST %s", self.path)
        
        # Read the request body
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8', errors='ignore')
        
        # Decode URL-encoded path
        decoded_path = urllib.parse.unquote(self.path)
        
        # Check for RCE attempt pattern from the template
        if self._is_rce_attempt(decoded_path, post_data):
            self._serve_rce_response(post_data)
            return
        
        # Handle normal POST requests
        self._serve_normal_post_response()
    
    def _is_path_traversal_attempt(self, path):
        """Check if the path matches CVE-2021-41773 traversal patterns"""
        traversal_patterns = [
            '/.%2e/',  # URL-encoded ../
            '/..',     # Direct ../
            '/icons/.%2e/%2e%2e/',  # Specific pattern from template
            '/cgi-bin/.%2e/.%2e/'   # Alternative pattern from template
        ]
        return any(pattern in path for pattern in traversal_patterns)
    
    def _is_rce_attempt(self, path, post_data):
        """Check if this is an RCE attempt matching the template"""
        return (
            '/cgi-bin/' in path and 
            self._is_path_traversal_attempt(path) and
            '/bin/sh' in path and
            'echo' in post_data
        )
    
    def _serve_passwd_file(self):
        """Serve simulated /etc/passwd content"""
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        
        # Simulated /etc/passwd content that matches the regex in the template
        passwd_content = """root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync
games:x:5:60:games:/usr/games:/usr/sbin/nologin
man:x:6:12:man:/var/cache/man:/usr/sbin/nologin
lp:x:7:7:lp:/var/spool/lpd:/usr/sbin/nologin
mail:x:8:8:mail:/var/mail:/usr/sbin/nologin
news:x:9:9:news:/var/spool/news:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
"""
        self.wfile.write(passwd_content.encode())
    
    def _serve_rce_response(self, post_data):
        """Serve RCE response that matches the template expectation"""
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        
        # The template looks for "CVE-2021-41773-POC" in the response
        # The template variable is: cmd: "echo COP-37714-1202-EVC | rev"
        # When reversed, "COP-37714-1202-EVC" becomes "CVE-2021-41773-POC"
        response = """Content-Type: text/plain

CVE-2021-41773-POC
"""
        self.wfile.write(response.encode())
    
    def _serve_index(self):
        """Serve a basic index page"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        
        html_content = """<!DOCTYPE html>
<html>
<head>
    <title>Apache Test Server</title>
</head>
<body>
    <h1>Apache 2.4.49 Test Server</h1>
    <p>This is a test server simulating Apache 2.4.49 for vulnerability testing.</p>
    <p>Server: Apache/2.4.49 (Unix)</p>
</body>
</html>"""
        self.wfile.write(html_content.encode())
    
    def _serve_icon(self):
        """Serve a basic response for icon requests"""
        self.send_response(200)
        self.send_header('Content-type', 'image/png')
        self.end_headers()
        # Send minimal PNG data
        self.wfile.write(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82')
    
    def _serve_404(self):
        """Serve 404 response"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        
        html_content = """<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>404 Not Found</title>
</head><body>
<h1>Not Found</h1>
<p>The requested URL was not found on this server.</p>
<hr>
<address>Apache/2.4.49 (Unix) Server</address>
</body></html>"""
        self.wfile.write(html_content.encode())
    
    def _serve_normal_post_response(self):
        """Serve normal POST response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        import json
        response = {
            "status": "success",
            "server": "Apache/2.4.49",
            "message": "POST request processed"
        }
        self.wfile.write(json.dumps(response).encode())

def main():
    PORT = 8002  # Different port from other test apps
    
    print(f"Starting Vulnerable Apache 2.4.49 Simulator on port {PORT}")
    print(f"Simulating CVE-2021-41773 vulnerability for testing")
    print(f"Directory: {os.path.dirname(os.path.abspath(__file__))}")
    
    try:
        with socketserver.TCPServer(("", PORT), VulnerableApacheHandler) as httpd:
            print(f"Server running at http://0.0.0.0:{PORT}/")
            print("This server simulates Apache 2.4.49 with CVE-2021-41773 vulnerability")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()