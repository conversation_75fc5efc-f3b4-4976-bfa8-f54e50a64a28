#!/usr/bin/env bash

# ==============================================================================
# FastScan Full Stack Development Environment Shutdown
# ==============================================================================
# This script stops all components of the FastScan development environment
# including LocalStack, backend, and frontend application.

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

# PID file locations
BACKEND_PID_FILE="/tmp/fastscan-backend.pid"
FRONTEND_PID_FILE="/tmp/fastscan-frontend.pid"

# Stop a running service
stop_service() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        log_info "Stopping $service_name (PID: $pid)..."
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            sleep 2
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                log_info "$service_name still running, force killing..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            log_success "$service_name stopped"
        else
            log_info "$service_name is not running, cleaning up PID file"
        fi
        rm -f "$pid_file"
    else
        log_info "No PID file found for $service_name"
    fi
}

stop_docker_services() {
    log_header "Stopping Docker Services"
    
    log_info "Stopping LocalStack and other Docker services..."
    (cd "$PROJECT_ROOT" && docker-compose down --remove-orphans) || {
        log_warn "Error stopping Docker services, attempting to force stop containers"
        docker ps -q --filter "name=fastscan" | xargs -r docker stop
        docker ps -q --filter "name=localstack" | xargs -r docker stop
    }
    
    log_success "Docker services stopped"
}

cleanup_ports() {
    log_header "Checking for Processes on Development Ports"
    
    local ports=("5175" "8082" "4566")
    local port_names=("Frontend" "Backend" "LocalStack")
    
    for i in "${!ports[@]}"; do
        local port="${ports[$i]}"
        local name="${port_names[$i]}"
        
        log_info "Checking port $port ($name)..."
        local pid=$(lsof -ti:$port 2>/dev/null)
        
        if [ -n "$pid" ]; then
            log_warn "Found process using port $port (PID: $pid)"
            log_info "Attempting to terminate process..."
            kill "$pid" 2>/dev/null || {
                log_warn "Could not terminate normally, using force kill"
                kill -9 "$pid" 2>/dev/null || true
            }
            log_success "Process on port $port terminated"
        else
            log_info "No process found on port $port"
        fi
    done
}

main() {
    log_header "FastScan Full Stack Environment Shutdown"
    
    # Stop application services
    stop_service "$BACKEND_PID_FILE" "Nuclear Pond backend"
    stop_service "$FRONTEND_PID_FILE" "Frontend"
    
    # Stop Docker services
    stop_docker_services
    
    # Check for any processes still using our ports
    cleanup_ports
    
    log_success "All FastScan services have been stopped"
}

main "$@"