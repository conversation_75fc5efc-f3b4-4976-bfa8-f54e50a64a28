package scan

import (
	"fmt"
	"nuclear_pond/pkg/types"
)

// GetDashboardMetrics calculates and returns dashboard metrics
func GetDashboardMetrics() (*types.DashboardMetrics, error) {
	// Leverage existing GetAllScans with high limit to get comprehensive data
	allScans, err := GetAllScans(1000, nil, "")
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve scans: %w", err)
	}

	metrics := &types.DashboardMetrics{
		TotalScans:     len(allScans.Scans),
		ActiveScans:    0,
		TargetsScanned: 0,
		RecentScans:    []types.DashboardScanSummary{},
		RecentFindings: []interface{}{}, // Mock empty array
		SystemStatus: types.SystemStatus{
			Nuclei: "online",
			Queue:  "online",
			API:    "online",
		},
	}

	// Calculate metrics from scan data
	for _, scan := range allScans.Scans {
		// Count active scans
		if scan.Status == "running" || scan.Status == "queued" {
			metrics.ActiveScans++
		}

		// Sum targets from completed scans
		if scan.Status == "completed" {
			metrics.TargetsScanned += scan.TargetCount
		}
	}

	// Get recent scans (latest 5)
	recentCount := 5
	if len(allScans.Scans) < recentCount {
		recentCount = len(allScans.Scans)
	}

	for i := 0; i < recentCount; i++ {
		scan := allScans.Scans[i]
		metrics.RecentScans = append(metrics.RecentScans, types.DashboardScanSummary{
			ScanID:      scan.ScanID,
			RequestID:   scan.RequestID,
			Status:      scan.Status,
			TargetCount: scan.TargetCount,
			CreatedAt:   scan.CreatedAt,
			CompletedAt: scan.CompletedAt,
		})
	}

	return metrics, nil
}
