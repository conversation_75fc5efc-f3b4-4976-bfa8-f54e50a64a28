#!/usr/bin/env bash

# Utility functions for dependency checks, error handling, and user prompts.

# --- Error <PERSON>ling ---
# Exit script with a standardized error message.
# Usage: fail "Something went wrong."
fail() {
    log_error "$1"
    exit 1
}

# --- Dependency Checking ---
# Verify that a command-line tool is installed and in the PATH.
# Usage: require_tool "docker"
require_tool() {
    local tool="$1"
    if ! command -v "$tool" &> /dev/null; then
        fail "Required tool '$tool' is not installed or not in PATH."
    fi
}

# --- User Confirmation ---
# Ask for user confirmation before proceeding.
# Usage: if confirm "Are you sure you want to delete the database?"; then ...
confirm() {
    local prompt="$1 (y/n) "
    local response

    while true; do
        read -r -p "$(echo -e "${YELLOW}[PROMPT]${NC} ${prompt}")" response
        case "$response" in
            [yY][eE][sS]|[yY])
                return 0
                ;;
            [nN][oO]|[nN])
                return 1
                ;;
            *)
                log_warn "Please answer 'y' or 'n'."
                ;;
        esac
    done
}