# Nuclei Lambda Module

This Terraform module deploys a complete Nuclei scanner infrastructure on AWS Lambda with all required components including S3 storage, Lambda layers, IAM roles, and monitoring capabilities.

## Architecture

The module creates a serverless Nuclei scanning infrastructure:

- **AWS Lambda Function**: Serverless execution environment for Nuclei scans
- **Lambda Layers**: Separate layers for Nuclei binary, custom templates, and configurations
- **S3 Bucket**: Storage for artifacts, templates, and scan findings
- **IAM Roles**: Least-privilege access for Lambda execution
- **CloudWatch**: Comprehensive logging and optional monitoring/alerting

## Quick Start

### 1. Deploy

```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan -target=module.nuclei_lambda

# Deploy the module
terraform apply -target=module.nuclei_lambda
```

## Module Overview

This module encapsulates all AWS resources required to run the Nuclei scanner as a Lambda function. It is designed for independent deployment, follows consistent project patterns, and offers robust security and monitoring features.

### Core Features
- **Lambda Function**: Serverless Nuclei scanner execution using a Go-based application.
- **Lambda Layers**: Manages separate layers for the Nuclei binary, custom user templates, and Nuclei configuration files.
- **S3 Storage**: Provides a dedicated S3 bucket for storing Nuclei artifacts (binaries, templates, configs) and scan findings. Includes lifecycle management for cost optimization.
- **IAM Security**: Implements least-privilege IAM roles and policies for secure access to AWS resources.
- **Comprehensive Monitoring**: Integrates with CloudWatch for logging, and optionally provides alarms, dashboards, and Lambda Insights for enhanced observability.

## Module Structure

```
nuclei_lambda/
├── main.tf              # Main Lambda function and Glue resources
├── variables.tf         # Input variables with validation
├── outputs.tf          # Output values for other modules
├── iam.tf              # IAM roles and policies
├── s3.tf               # S3 bucket and lifecycle policies
├── artifacts.tf        # Nuclei binary and template management
├── cloudwatch.tf       # CloudWatch logs, alarms, and dashboards
└── README.md           # This documentation
```

## Monitoring and Observability

### CloudWatch Logs

All Lambda execution logs are stored in CloudWatch with configurable retention:

```bash
# View logs
aws logs tail /aws/lambda/fast-scan-nuclei-function --follow
```

### Useful Commands

```bash
# Check Lambda function status
aws lambda get-function --function-name fast-scan-nuclei-function

# Check DynamoDB table
aws dynamodb describe-table --table-name $(terraform output -raw dynamodb_state_table)

# Check S3 bucket
aws s3 ls $(terraform output -raw s3_bucket_name)

# View recent logs
aws logs tail /aws/lambda/fast-scan-nuclei-function --follow

# Test Lambda function
aws lambda invoke --function-name fast-scan-nuclei-function \
  --payload '{"target": "example.com"}' response.json

# Check S3 bucket contents
aws s3 ls s3://fast-scan-nuclei-artifacts/ --recursive

# Monitor CloudWatch metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Duration \
  --dimensions Name=FunctionName,Value=fast-scan-nuclei-function \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-02T00:00:00Z \
  --period 3600 \
  --statistics Average
```

## Related Documentation

- [Nuclei Lambda Application README](../../lambda-nuclei-scanner/README.md)
- [Nuclear Pond Backend Module](../nuclear_pond_backend/README.md)
- [Network Module](../network/README.md)
- [Nuclei Documentation](https://docs.projectdiscovery.io/tools/nuclei/)

## Useful commands

# Taint the Lambda function itself
terraform taint 'module.nuclei_lambda.aws_lambda_function.nuclei_function'

# Taint all Lambda layers
terraform taint 'module.nuclei_lambda.aws_lambda_layer_version.nuclei_layer'
terraform taint 'module.nuclei_lambda.aws_lambda_layer_version.templates_layer'

# Taint the build artifacts to ensure they get rebuilt
terraform taint 'module.nuclei_lambda.null_resource.build_artifacts'

# Taint the S3 objects that contain the layers
terraform taint 'module.nuclei_lambda.aws_s3_object.upload_nuclei'
terraform taint 'module.nuclei_lambda.aws_s3_object.upload_templates'

# Taint the Lambda alias
terraform taint 'module.nuclei_lambda.aws_lambda_alias.nuclei_alias'
