import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { useSidebarState } from "@/features/layout/hooks";
import { useAuthGuard } from "@/hooks/use-auth-guard";
import { cn } from "@/lib/utils";

export const Route = createFileRoute("/_authenticated")({
  beforeLoad: async () => {
    // Import authService dynamically to avoid circular dependencies
    const { authService } = await import("@/lib/auth");

    // Check if user has valid JWT token
    if (!authService.isAuthenticated()) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: AuthenticatedLayout,
});

function AuthenticatedLayout() {
  const { open, setOpen, isMobile } = useSidebarState();

  // Global auth guard - handles token expiration and automatic logout
  useAuthGuard();

  return (
    <div className={cn("drawer", !isMobile && "lg:drawer-open")}>
      <input id="drawer-toggle" type="checkbox" className="drawer-toggle" checked={open} onChange={(e) => setOpen(e.target.checked)} />
      <div className="drawer-content flex flex-col">
        <Header />
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </div>
      <label htmlFor="drawer-toggle" className="drawer-side z-50">
        <div className="drawer-overlay" />
        <Sidebar />
      </label>
    </div>
  );
}
