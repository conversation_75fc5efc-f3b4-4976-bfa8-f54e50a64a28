package server

import (
	"net/http"
	"nuclear_pond/pkg/handlers"

	customMiddleware "nuclear_pond/pkg/middleware"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
)

// NewRouter creates a new Chi router and sets up the routes.
func NewRouter() http.Handler {
	r := chi.NewRouter()

	// Global middleware
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(customMiddleware.CORSMiddleware)

	// Public endpoints (no authentication required)
	r.Get("/health-check", handlers.HealthHandler)
	r.Post("/auth", handlers.AuthHandler)

	// Protected routes (require JWT authentication)
	r.Group(func(r chi.Router) {
		r.Use(customMiddleware.JWTMiddleware)
		r.Get("/", handlers.IndexHandler)
		r.Post("/auth/refresh", handlers.RefreshHandler)
		r.Get("/dashboard/metrics", handlers.DashboardMetricsHandler)
		r.Post("/scan", handlers.ScanHandler)
		r.Get("/scan/{scanId}", handlers.ScanStatusHandler)
		r.Get("/scan/{scanId}/targets", handlers.ScanTargetsHandler)
		r.Get("/scans", handlers.ScansListHandler)
		r.Post("/pow/control", handlers.PowControlHandler)
		r.Get("/templates", handlers.GetTemplatesHandler)
	})

	return r
}
