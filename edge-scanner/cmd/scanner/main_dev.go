package main

import (
	"flag"
	"log"
	"net/url"
	"time"

	"github.com/RootEvidence/edge-scanner/internal/configs"
	"github.com/RootEvidence/edge-scanner/internal/controller"
)

func runDev() {
	var singleRun = flag.Bool("single-run", false, "Run once and exit (useful for testing)")
	var debugMode = flag.Bool("debug", false, "Enable debug mode")
	flag.Parse()

	cfg := configs.LoadConfig()
	
	// Override config with CLI flags if provided
	if *debugMode {
		cfg.DebugMode = true
	}

	product := cfg.PRODUCT
	baseURL := cfg.APIEndpoint + "/job/batch/next"

	u, err := url.Parse(baseURL)
	if err != nil {
		log.Fatal(err)
	}
	q := u.Query()
	q.Set("queue", product)
	u.RawQuery = q.Encode()

	apiURL := u.String()

	if cfg.DebugMode {
		log.Printf("[DEBUG] Development mode enabled")
		log.Printf("[DEBUG] Single run mode: %v", *singleRun)
		log.Printf("[DEBUG] API URL: %s", apiURL)
		log.Printf("[DEBUG] Worker count: %d", cfg.WorkerCount)
		log.Printf("[DEBUG] Scan interval: %d seconds", cfg.ScanIntervalSecs)
	}

	// Run once
	log.Println("[INFO] Starting scan cycle...")
	controller.FetchAndProcessTargets(cfg, apiURL)

	if *singleRun {
		log.Println("[INFO] Single run completed, exiting...")
		return
	}

	// Continue with periodic scanning
	scanInterval := time.Duration(cfg.ScanIntervalSecs) * time.Second
	ticker := time.NewTicker(scanInterval)
	defer ticker.Stop()

	for {
		log.Printf("[INFO] Waiting %v before next fetch...", scanInterval)
		<-ticker.C
		controller.FetchAndProcessTargets(cfg, apiURL)
	}
}