#!/usr/bin/env bash

# ==============================================================================
# LocalStack Lambda Test Script
# ==============================================================================
# This script invokes the deployed Nuclei Lambda function in LocalStack with a
# sample payload to verify its functionality.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

check_prerequisites() {
    log_info "Checking prerequisites..."
    require_tool "awslocal"
    require_tool "jq"
    require_tool "curl"

    if ! curl -s "${LOCALSTACK_ENDPOINT}/_localstack/health" | grep -q "available"; then
        fail "LocalStack is not running. Please start it with: devbox run localstack"
    fi

    log_info "Checking if Lambda function '${LAMBDA_FUNCTION_NAME}' exists..."
    if ! awslocal lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" >/dev/null 2>&1; then
        fail "Lambda function '${LAMBDA_FUNCTION_NAME}' not found. Please deploy it first with: devbox run local:deploy-lambda"
    fi

    log_success "All prerequisites met."
}

test_lambda() {
    log_header "Testing Lambda Function: ${LAMBDA_FUNCTION_NAME}"

    local test_payload
    test_payload=$(cat <<EOF
{"targets":["http://vulnerable-apache:8002"],"output":"json","templates":["CVE-2023-22527"]}
EOF
)

    log_info "Invoking Lambda function with test payload..."
    echo "Payload:"
    echo "$test_payload" | jq .

    local response_file="/tmp/lambda-response.json"
    if echo "$test_payload" | awslocal lambda invoke \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --payload file:///dev/stdin \
        --cli-binary-format raw-in-base64-out \
        "$response_file" >/dev/null 2>&1; then

        log_success "Lambda function invoked successfully."

        if [ -f "$response_file" ]; then
            log_info "Lambda response:"
            if jq . "$response_file"; then
                log_success "Response is valid JSON."
            else
                log_warn "Response is not valid JSON. Raw output:"
                cat "$response_file"
            fi
            rm -f "$response_file"
        fi
    else
        log_error "Lambda function invocation failed. Check LocalStack Docker logs for details."
        return 1
    fi
}

main() {
    log_header "Lambda Test Runner"
    check_prerequisites
    test_lambda
    log_success "Lambda test completed."
}

main "$@"
