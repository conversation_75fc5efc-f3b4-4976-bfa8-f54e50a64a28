import { createFileRoute, redirect } from "@tanstack/react-router";
import GuestLayout from "@/components/Layout/GuestLayout";
import Login from "@/pages/Login";

export const Route = createFileRoute("/login")({
  beforeLoad: async () => {
    // Import authService dynamically to avoid circular dependencies
    const { authService } = await import("@/lib/auth");

    // Check if user has valid JWT token, redirect to dashboard if so
    if (authService.isAuthenticated()) {
      throw redirect({
        to: "/dashboard",
      });
    }
  },
  component: () => (
    <GuestLayout>
      <Login />
    </GuestLayout>
  ),
});
