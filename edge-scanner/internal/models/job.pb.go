// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: proto/job.proto

package models

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Target struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addr          string                 `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr,omitempty"`
	Ports         []int32                `protobuf:"varint,2,rep,packed,name=ports,proto3" json:"ports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Target) Reset() {
	*x = Target{}
	mi := &file_proto_job_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Target) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Target) ProtoMessage() {}

func (x *Target) ProtoReflect() protoreflect.Message {
	mi := &file_proto_job_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Target.ProtoReflect.Descriptor instead.
func (*Target) Descriptor() ([]byte, []int) {
	return file_proto_job_proto_rawDescGZIP(), []int{0}
}

func (x *Target) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Target) GetPorts() []int32 {
	if x != nil {
		return x.Ports
	}
	return nil
}

type Probe struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TemplateId    string                 `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	ParamsBlob    []byte                 `protobuf:"bytes,2,opt,name=params_blob,json=paramsBlob,proto3" json:"params_blob,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Probe) Reset() {
	*x = Probe{}
	mi := &file_proto_job_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Probe) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Probe) ProtoMessage() {}

func (x *Probe) ProtoReflect() protoreflect.Message {
	mi := &file_proto_job_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Probe.ProtoReflect.Descriptor instead.
func (*Probe) Descriptor() ([]byte, []int) {
	return file_proto_job_proto_rawDescGZIP(), []int{1}
}

func (x *Probe) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Probe) GetParamsBlob() []byte {
	if x != nil {
		return x.ParamsBlob
	}
	return nil
}

type IPJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobId         int64                  `protobuf:"varint,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	BatchId       int64                  `protobuf:"varint,2,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	Priority      int32                  `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	Product       string                 `protobuf:"bytes,4,opt,name=product,proto3" json:"product,omitempty"`
	TargetCount   int32                  `protobuf:"varint,5,opt,name=target_count,json=targetCount,proto3" json:"target_count,omitempty"`
	Targets       []*Target              `protobuf:"bytes,6,rep,name=targets,proto3" json:"targets,omitempty"`
	Probes        []*Probe               `protobuf:"bytes,7,rep,name=probes,proto3" json:"probes,omitempty"`
	SoftTimeoutMs int32                  `protobuf:"varint,8,opt,name=soft_timeout_ms,json=softTimeoutMs,proto3" json:"soft_timeout_ms,omitempty"`
	HardTimeoutMs int32                  `protobuf:"varint,9,opt,name=hard_timeout_ms,json=hardTimeoutMs,proto3" json:"hard_timeout_ms,omitempty"`
	AuthToken     string                 `protobuf:"bytes,10,opt,name=auth_token,json=authToken,proto3" json:"auth_token,omitempty"`
	Timestamp     string                 `protobuf:"bytes,11,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IPJobResponse) Reset() {
	*x = IPJobResponse{}
	mi := &file_proto_job_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPJobResponse) ProtoMessage() {}

func (x *IPJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_job_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPJobResponse.ProtoReflect.Descriptor instead.
func (*IPJobResponse) Descriptor() ([]byte, []int) {
	return file_proto_job_proto_rawDescGZIP(), []int{2}
}

func (x *IPJobResponse) GetJobId() int64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

func (x *IPJobResponse) GetBatchId() int64 {
	if x != nil {
		return x.BatchId
	}
	return 0
}

func (x *IPJobResponse) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *IPJobResponse) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *IPJobResponse) GetTargetCount() int32 {
	if x != nil {
		return x.TargetCount
	}
	return 0
}

func (x *IPJobResponse) GetTargets() []*Target {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *IPJobResponse) GetProbes() []*Probe {
	if x != nil {
		return x.Probes
	}
	return nil
}

func (x *IPJobResponse) GetSoftTimeoutMs() int32 {
	if x != nil {
		return x.SoftTimeoutMs
	}
	return 0
}

func (x *IPJobResponse) GetHardTimeoutMs() int32 {
	if x != nil {
		return x.HardTimeoutMs
	}
	return 0
}

func (x *IPJobResponse) GetAuthToken() string {
	if x != nil {
		return x.AuthToken
	}
	return ""
}

func (x *IPJobResponse) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

var File_proto_job_proto protoreflect.FileDescriptor

const file_proto_job_proto_rawDesc = "" +
	"\n" +
	"\x0fproto/job.proto\x12\x03job\"2\n" +
	"\x06Target\x12\x12\n" +
	"\x04addr\x18\x01 \x01(\tR\x04addr\x12\x14\n" +
	"\x05ports\x18\x02 \x03(\x05R\x05ports\"I\n" +
	"\x05Probe\x12\x1f\n" +
	"\vtemplate_id\x18\x01 \x01(\tR\n" +
	"templateId\x12\x1f\n" +
	"\vparams_blob\x18\x02 \x01(\tR\n" +
	"paramsBlob\"\xf2\x02\n" +
	"\rIPJobResponse\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\x03R\x05jobId\x12\x19\n" +
	"\bbatch_id\x18\x02 \x01(\x03R\abatchId\x12\x1a\n" +
	"\bpriority\x18\x03 \x01(\x05R\bpriority\x12\x18\n" +
	"\aproduct\x18\x04 \x01(\tR\aproduct\x12!\n" +
	"\ftarget_count\x18\x05 \x01(\x05R\vtargetCount\x12%\n" +
	"\atargets\x18\x06 \x03(\v2\v.job.TargetR\atargets\x12\"\n" +
	"\x06probes\x18\a \x03(\v2\n" +
	".job.ProbeR\x06probes\x12&\n" +
	"\x0fsoft_timeout_ms\x18\b \x01(\x05R\rsoftTimeoutMs\x12&\n" +
	"\x0fhard_timeout_ms\x18\t \x01(\x05R\rhardTimeoutMs\x12\x1d\n" +
	"\n" +
	"auth_token\x18\n" +
	" \x01(\tR\tauthToken\x12\x1c\n" +
	"\ttimestamp\x18\v \x01(\tR\ttimestampB9Z7github.com/yourorg/evidence-core/internal/models;modelsb\x06proto3"

var (
	file_proto_job_proto_rawDescOnce sync.Once
	file_proto_job_proto_rawDescData []byte
)

func file_proto_job_proto_rawDescGZIP() []byte {
	file_proto_job_proto_rawDescOnce.Do(func() {
		file_proto_job_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_job_proto_rawDesc), len(file_proto_job_proto_rawDesc)))
	})
	return file_proto_job_proto_rawDescData
}

var file_proto_job_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_job_proto_goTypes = []any{
	(*Target)(nil),        // 0: job.Target
	(*Probe)(nil),         // 1: job.Probe
	(*IPJobResponse)(nil), // 2: job.IPJobResponse
}
var file_proto_job_proto_depIdxs = []int32{
	0, // 0: job.IPJobResponse.targets:type_name -> job.Target
	1, // 1: job.IPJobResponse.probes:type_name -> job.Probe
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_job_proto_init() }
func file_proto_job_proto_init() {
	if File_proto_job_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_job_proto_rawDesc), len(file_proto_job_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_job_proto_goTypes,
		DependencyIndexes: file_proto_job_proto_depIdxs,
		MessageInfos:      file_proto_job_proto_msgTypes,
	}.Build()
	File_proto_job_proto = out.File
	file_proto_job_proto_goTypes = nil
	file_proto_job_proto_depIdxs = nil
}

type TargetData struct {
	IP    string  `json:"ip"`
	Ports []int32 `json:"ports"`
}
type ScanPayload struct {
	Targets []TargetData `json:"targets"`
}
