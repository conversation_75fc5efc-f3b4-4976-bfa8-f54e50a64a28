#!/usr/bin/env bash

# ==============================================================================
# Nuclei Template Metadata Generator
# ==============================================================================
# This script builds and runs the metadata-generator to create a templates.json
# file from Nuclei templates stored in S3.

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
METADATA_GENERATOR_DIR="${PROJECT_ROOT}/scripts/metadata-generator"

# Source common utilities if they exist
if [ -f "${PROJECT_ROOT}/scripts/lib/logging.sh" ]; then
  source "${PROJECT_ROOT}/scripts/lib/logging.sh"
else
  # Simple logging fallbacks if the library doesn't exist
  log_header() { echo -e "\n=== $1 ==="; }
  log_info() { echo -e "[INFO] $1"; }
  log_success() { echo -e "[SUCCESS] $1"; }
  log_warn() { echo -e "[WARNING] $1"; }
  fail() { echo -e "[ERROR] $1"; exit 1; }
fi

usage() {
  echo "Usage: $0 [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  --bucket NAME     Override the S3 bucket name (default: from env var NUCLEI_TEMPLATES_BUCKET)"
  echo "  --local           Use LocalStack endpoint (sets LOCAL_DEVELOPMENT=true)"
  echo "  --help            Show this help message"
}

check_prerequisites() {
  log_header "Checking Prerequisites"
  
  # Check if Go is installed
  if ! command -v go &> /dev/null; then
    fail "Go is not installed. Please install Go to run the metadata generator."
  fi
  
  # Check if metadata-generator directory exists
  if [ ! -d "$METADATA_GENERATOR_DIR" ]; then
    fail "Metadata generator directory not found: $METADATA_GENERATOR_DIR"
  fi
  
  log_success "All prerequisites met"
}

build_generator() {
  log_header "Building Metadata Generator"
  
  (
    cd "$METADATA_GENERATOR_DIR"
    log_info "Building metadata-generator..."
    go build -o metadata-generator .
    if [ ! -f "./metadata-generator" ]; then
      fail "Failed to build metadata-generator"
    fi
    log_success "Metadata generator built successfully"
  )
}

run_generator() {
  log_header "Running Metadata Generator"
  
  local bucket="${BUCKET:-$NUCLEI_TEMPLATES_BUCKET}"
  if [ -z "$bucket" ]; then
    fail "No bucket specified. Set NUCLEI_TEMPLATES_BUCKET env var or use --bucket option."
  fi
  
  log_info "Generating metadata for templates in bucket: $bucket"
  
  (
    cd "$METADATA_GENERATOR_DIR"
    
    # Set environment variables
    export NUCLEI_TEMPLATES_BUCKET="$bucket"
    export TEMPLATES_SOURCE_DIR="${PROJECT_ROOT}/backend/templates"
    
    # Run the generator
    ./metadata-generator
    
    log_success "Metadata generation completed"
  )
}

main() {
  local BUCKET=""
  local USE_LOCAL=false
  
  while [[ $# -gt 0 ]]; do
    case $1 in
      --bucket) BUCKET="$2"; shift 2 ;;
      --local) USE_LOCAL=true; shift ;;
      --help) usage; exit 0 ;;
      *) fail "Unknown option: $1" ;;
    esac
  done
  
  # Set LocalStack environment if requested
  if [ "$USE_LOCAL" = "true" ]; then
    export LOCAL_DEVELOPMENT="true"
    export AWS_ENDPOINT_URL="http://localhost:4566"
  fi
  
  check_prerequisites
  build_generator
  run_generator
}

main "$@"