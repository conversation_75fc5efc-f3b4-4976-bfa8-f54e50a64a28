# ==============================================================================
# LOCALS CONFIGURATION
# ==============================================================================
# This file defines standardized naming conventions and tagging strategies
# for all AWS resources in the FastScan project.

# Data sources for dynamic values
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

locals {
  # ==============================================================================
  # DEPLOYMENT CONTEXT
  # ==============================================================================
  
  # Use deployment ID if provided, otherwise omit timestamp to avoid unnecessary updates
  deployment_timestamp = var.deployment_id != "" ? var.deployment_id : null
  
  # Account and region context
  account_id = data.aws_caller_identity.current.account_id
  region     = data.aws_region.current.name
  
  # ==============================================================================
  # STANDARDIZED NAMING CONVENTIONS
  # ==============================================================================
  
  # Base naming components
  name_prefix = "${var.project_name}-${var.environment}"
  
  # Resource-specific naming patterns
  naming = {
    # Compute resources
    lambda_function = "${local.name_prefix}-nuclei-function"
    ecs_cluster     = "${local.name_prefix}-cluster"
    ecs_service     = "${local.name_prefix}-nuclearpond-service"
    
    # Storage resources
    s3_bucket_prefix     = "${var.project_name}-${var.environment}"
    dynamodb_table       = "${var.project_name}-${var.environment}-server-state"
    ecr_repository       = "${var.project_name}/nuclearpond"
    
    # Network resources
    vpc                  = "${local.name_prefix}-vpc"
    subnet_public_prefix = "${local.name_prefix}-public-subnet"
    subnet_private_prefix = "${local.name_prefix}-private-subnet"
    alb                  = "${local.name_prefix}-alb"
    security_group_prefix = "${local.name_prefix}-sg"
    
    # Monitoring resources
    cloudwatch_log_group_prefix = "/aws/lambda/${local.name_prefix}"
    resource_group              = "${local.name_prefix}-resources"
    
    # Domain resources
    route53_zone         = var.pow_domain_name
    acm_certificate      = "*.${var.frontend_domain_name}"
  }
  
  # ==============================================================================
  # STREAMLINED TAGGING STRATEGY
  # ==============================================================================
  # AWS has tag limits (10 tags for S3 objects, 50 for most resources)
  # This strategy prioritizes essential tags for cost tracking and resource management
  
  # Core tags for all resources (max 8 tags to stay under S3 limit)
  common_tags = merge(var.tags, local.deployment_timestamp != null ? {
    Project     = var.project_name
    Environment = var.environment
    Owner       = var.owner
    CostCenter  = var.cost_center
    Terraform   = "true"
    DeployedAt  = local.deployment_timestamp
  } : {
    Project     = var.project_name
    Environment = var.environment
    Owner       = var.owner
    CostCenter  = var.cost_center
    Terraform   = "true"
  })
  
  # ==============================================================================
  # RESOURCE-SPECIFIC TAG SETS
  # ==============================================================================
  # These add 1-2 additional tags to common_tags for specific resource types
  
  # Tags for compute resources (Lambda, ECS)
  compute_tags = merge(local.common_tags, {
    Type = "compute"
  })
  
  # Tags for storage resources (S3, DynamoDB)
  storage_tags = merge(local.common_tags, {
    Type = "storage"
  })
  
  # Tags for network resources (VPC, ALB, Security Groups)
  network_tags = merge(local.common_tags, {
    Type = "network"
  })
  
  # Tags for monitoring resources (CloudWatch)
  monitoring_tags = merge(local.common_tags, {
    Type = "monitoring"
  })
  
  # ==============================================================================
  # S3 OBJECT TAGS (LIMITED TO 10 TAGS)
  # ==============================================================================
  # Special tag set for S3 objects with AWS 10-tag limit
  s3_object_tags = {
    Project     = var.project_name
    Environment = var.environment
    Owner       = var.owner
    CostCenter  = var.cost_center
    Terraform   = "true"
    # Leave 5 slots for resource-specific tags
  }
  
  # ==============================================================================
  # RESOURCE GROUP CONFIGURATION
  # ==============================================================================
  
  # Resource group query for all project resources
  resource_group_query = {
    ResourceTypeFilters = ["AWS::AllSupported"]
    TagFilters = [
      {
        Key    = "Project"
        Values = [var.project_name]
      },
      {
        Key    = "Environment"
        Values = [var.environment]
      }
    ]
  }
  
  # ==============================================================================
  # CONDITIONAL CONFIGURATIONS
  # ==============================================================================
  
  # Environment-specific configurations
  environment_config = {
    dev = {
      deletion_protection = false
      backup_required    = false
      monitoring_level   = "basic"
      log_retention_days = 7
    }
    staging = {
      deletion_protection = false
      backup_required    = false
      monitoring_level   = "enhanced"
      log_retention_days = 30
    }
    prod = {
      deletion_protection = true
      backup_required    = true
      monitoring_level   = "enhanced"
      log_retention_days = 30
    }
  }
  
  # Current environment configuration
  current_env_config = local.environment_config[var.environment]
} 