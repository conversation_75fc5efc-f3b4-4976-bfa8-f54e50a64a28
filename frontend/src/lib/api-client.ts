import { create<PERSON>lova } from "alova";
import adapterFetch from "alova/fetch";
import Re<PERSON>Hook from "alova/react";

// Get the API URL from environment variables
const apiUrl = import.meta.env.VITE_API_URL;

// Check if API URL is defined
if (!apiUrl) {
  console.warn("API URL is not defined in environment variables. Set VITE_API_URL in your .env file.");
}

// Function to get JWT token from localStorage
const getJWTToken = (): string | null => {
  return localStorage.getItem("jwt_token");
};

// Function to handle authentication errors
const handleAuthError = () => {
  // Clear all auth data
  localStorage.removeItem("jwt_token");
  localStorage.removeItem("auth");

  // Redirect to login page
  if (window.location.pathname !== "/login") {
    window.location.href = "/login";
  }
};

export const alovaClient = createAlova({
  baseURL: apiUrl,
  statesHook: ReactHook,
  requestAdapter: adapterFetch(),
  cacheFor: { GET: 30 * 1000 },
  beforeRequest(method) {
    method.config.headers["Content-Type"] = "application/json";

    // Add JWT token to Authorization header if available
    const token = getJWTToken();
    if (token) {
      method.config.headers.Authorization = `Bearer ${token}`;
    }
  },
  async responded(response: Response) {
    if (!response.ok) {
      // If unauthorized, clear auth data and redirect to login
      if (response.status === 401) {
        console.warn("Authentication failed - redirecting to login");
        handleAuthError();
        throw new Error("Authentication required");
      }
      const errorText = await response.text();
      throw new Error(errorText || `API error: ${response.status}`);
    }
    const json = await response.json();
    return json;
  },
});
