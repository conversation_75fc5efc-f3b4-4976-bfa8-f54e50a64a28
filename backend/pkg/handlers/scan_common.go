package handlers

import (
	"errors"
	"log"
	"net/http"
	"nuclear_pond/pkg/scan"
	"nuclear_pond/pkg/security"
	"nuclear_pond/pkg/types"
	"strconv"
	"strings"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

// decodeScanRequestBody decodes and validates the incoming scan request payload.
func decodeScanRequestBody(r *http.Request) (types.Request, error) {
	var req types.Request
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		return types.Request{}, err
	}
	if len(req.Targets) == 0 {
		return types.Request{}, &badRequestError{msg: "Targets field is required and must contain at least one target"}
	}

	// Security validation: Ensure all targets are demo targets
	validation := security.ValidateDemoTargets(req.Targets)
	if !validation.IsValid {
		return types.Request{}, &badRequestError{msg: security.ValidateTargetsSecurityError(validation.InvalidTargets).Error()}
	}
	// Require scanners and derive internal fields:
	// Threads = Scanners; Batches (size per scanner) = ceil(len(Targets) / Scanners)
	if req.Scanners > 0 {
		if req.Scanners > len(req.Targets) {
			req.Scanners = len(req.Targets)
		}
		if req.Scanners <= 0 {
			req.Scanners = 1
		}
		req.Threads = req.Scanners
		// Avoid division by zero
		denom := req.Scanners
		if denom <= 0 {
			denom = 1
		}
		// Compute batches as targets per scanner group size
		// We keep existing orchestrator contract that expects Batches to be the group size for helpers.SplitSlice
		// i.e., batch size = ceil(totalTargets / scanners)
		totalTargets := len(req.Targets)
		req.Batches = (totalTargets + denom - 1) / denom
	} else {
		return types.Request{}, &badRequestError{msg: "Scanners field is required and must be greater than 0"}
	}
	if len(req.Templates) > 0 {
		if err := scan.ValidateTemplateSelections(req.Templates); err != nil {
			return types.Request{}, &badRequestError{msg: "Template validation failed: " + err.Error()}
		}
	}

	return req, nil
}

// parseScanID extracts scanId from URL, returns normalized requestID (without dashes) and the raw scanId.
func parseScanID(r *http.Request) (requestID, scanID string, err error) {
	scanID = chi.URLParam(r, "scanId")
	if scanID == "" {
		return "", "", &badRequestError{msg: "Missing scan ID parameter"}
	}
	requestID = strings.ReplaceAll(scanID, "-", "")

	return requestID, scanID, nil
}

// fetchScanOr404 fetches a scan by requestID, converting common not-found conditions into an HTTP 404.
func fetchScanOr404(requestID string) (*types.ScanRequest, error) {
	sr, err := scan.GetScanByID(requestID)
	if err != nil {
		return nil, &notFoundError{msg: "Scan not found: " + err.Error()}
	}

	return sr, nil
}

// parseListQuery parses list-specific query parameters with sensible defaults and limits.
func parseListQuery(r *http.Request) (limit int, statusFilter string) {
	limit = 20
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if parsed, err := strconv.Atoi(limitStr); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}
	statusFilter = r.URL.Query().Get("status")
	if statusFilter == "" {
		statusFilter = "all"
	}

	return
}

// Common error helpers
type badRequestError struct{ msg string }

func (e *badRequestError) Error() string { return e.msg }

type notFoundError struct{ msg string }

func (e *notFoundError) Error() string { return e.msg }

// writeJSON writes an object with status.
func writeJSON(w http.ResponseWriter, r *http.Request, status int, v interface{}) {
	render.Status(r, status)
	render.JSON(w, r, v)
}

// writeAPIError standardizes API error responses.
func writeAPIError(w http.ResponseWriter, r *http.Request, err error) {
	var bre *badRequestError
	if errors.As(err, &bre) {
		writeJSON(w, r, http.StatusBadRequest, types.Response{Error: "Bad Request", Message: bre.msg})

		return
	}

	var nfe *notFoundError
	if errors.As(err, &nfe) {
		writeJSON(w, r, http.StatusNotFound, types.Response{Error: "Not Found", Message: nfe.msg})

		return
	}

	log.Printf("Internal error: %v", err)
	writeJSON(w, r, http.StatusInternalServerError, types.Response{Error: "Internal Server Error", Message: err.Error()})
}
