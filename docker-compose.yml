services:
  localstack:
    image: localstack/localstack:latest
    container_name: fastscan-localstack
    ports:
      - "4566:4566"          # LocalStack main port
      - "4510-4559:4510-4559" # LocalStack service range
    environment:
      - DEBUG=1
      - SERVICES=s3,lambda,dynamodb,logs,iam
      - LAMBDA_EXECUTOR=docker
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LAMBDA_RUNTIME_ENVIRONMENT_TIMEOUT=60
      # Docker Compose automatically prefixes network names with the project name
      - LAMBDA_DOCKER_NETWORK=fastscan_fastscan_network
      - PERSISTENCE=1
      - DATA_DIR=/var/lib/localstack/data
    volumes:
      - "./localstack-data:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "/tmp:/tmp"
    networks:
      - fastscan_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Test target applications for local development
  test-app-1:
    image: python:3.11-slim
    container_name: fastscan-test-app-1
    ports:
      - "8080:8000"
    command: python -m http.server 8000
    working_dir: /app
    volumes:
      - ./test-targets/app1:/app
    networks:
      - fastscan_network
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  test-app-2:
    image: python:3.11-slim
    container_name: fastscan-test-app-2
    ports:
      - "8081:8000"
    command: python -m http.server 8000
    working_dir: /app
    volumes:
      - ./test-targets/app2:/app
    networks:
      - fastscan_network
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Vulnerable Apache 2.4.49 simulator for CVE-2021-41773 testing
  vulnerable-apache:
    image: python:3.11-slim
    container_name: fastscan-vulnerable-apache
    ports:
      - "8083:8002"
    command: python server.py
    working_dir: /app
    volumes:
      - ./test-targets/vulnerable-apache:/app
    networks:
      - fastscan_network
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8002')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Reverse proxy for domain simulation
  test-proxy:
    image: nginx:alpine
    container_name: fastscan-test-proxy
    ports:
      - "80:80"
    volumes:
      - ./test-targets/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      fastscan_network:
        ipv4_address: **********0
    depends_on:
      - test-app-1
      - test-app-2
      - vulnerable-apache
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

networks:
  fastscan_network:
    name: fastscan_fastscan_network
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

