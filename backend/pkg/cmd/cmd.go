package cmd

import (
	"fmt"
	"log"
	"nuclear_pond/pkg/server"

	"github.com/common-nighthawk/go-figure"
	"github.com/spf13/cobra"
)

var asciiBanner = figure.NewFigure("Fast Scan", "", true).String()

var rootCmd = &cobra.Command{
	Use:     "nuclearpond",
	Short:   "A CLI tool for FastScan Nuclear Pond HTTP service",
	Long:    "FastScan Nuclear Pond provides an HTTP API service to execute nuclei scans through lambda functions.",
	Example: `nuclearpond service`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println(asciiBanner)
		cmd.Help()
	},
}

var serviceCmd = &cobra.Command{
	Use:   "service",
	Short: "Launch FastScan Nuclear Pond HTTP API service",
	Long:  "Launches the FastScan Nuclear Pond HTTP API service to execute nuclei scans through lambda functions",
	Run: func(cmd *cobra.Command, args []string) {
		// Print banner
		fmt.Println(asciiBanner)
		fmt.Println()
		// Start server
		log.Println("Running FastScan Nuclear Pond http server on port 8082")
		log.Println("http://localhost:8082")
		server.HandleRequests()
	},
}

func init() {
	// No additional flags needed for service command
}

// Execute executes the root command.
func Execute() error {
	rootCmd.CompletionOptions.DisableDefaultCmd = true
	rootCmd.SetHelpCommand(&cobra.Command{
		Use:    "no-help",
		Hidden: true,
	})

	rootCmd.HasHelpSubCommands()
	rootCmd.AddCommand(serviceCmd)

	return rootCmd.Execute()
}
