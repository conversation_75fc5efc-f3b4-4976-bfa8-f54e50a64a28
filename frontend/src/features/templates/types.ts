/**
 * Types for template selection feature
 */

/**
 * Template metadata
 */
export interface TemplateMetadata {
  id: string;
  name: string;
  path: string;
  author: string[];
  severity: TemplateSeverity;
  description: string;
  tags: string[];
  size: number;
}

/**
 * Template severity levels
 */
export type TemplateSeverity = "critical" | "high" | "medium" | "low" | "info";

/**
 * Template index containing all available templates
 */
export interface TemplateIndex {
  version: string;
  generated_at: string;
  templates: Record<string, TemplateMetadata>;
  summary: {
    total: number;
    by_severity: Record<TemplateSeverity, number>;
  };
}

/**
 * Template filter options
 */
export interface TemplateFilterOptions {
  search?: string;
  severities?: TemplateSeverity[];
  tags?: string[];
  categories?: string[];
}

// Simplicity: Removed unused and over-engineered types.
// The following types were removed to simplify the codebase, in line with simplicity.md:
// - TemplateTag: No longer needed as tag processing is handled in the hook.
// - TemplateSelectionState: State is managed directly by the hook.
// - TemplateErrorType, TemplateError, TemplateOperationResult, TemplateValidationResult:
//   Custom error and result wrappers are an anti-pattern. The hook now relies on
//   the simpler, built-in error handling from the request library.
