import { alovaClient } from "@/lib/api-client";
import type { TemplateIndex } from "./types";

/**
 * Service for template operations.
 *
 * This service is responsible for fetching the raw template data from the API.
 * All data processing, filtering, and state management should be handled in the corresponding hook.
 */
export const templateService = {
  /**
   * Creates an Alova method to get all available templates with structured metadata.
   * This method can be used with `useRequest` for automatic state management.
   *
   * @returns An Alova GET method instance for fetching the template index.
   */
  getTemplates: () => {
    return alovaClient.Get<TemplateIndex>("/templates");
  },
};
