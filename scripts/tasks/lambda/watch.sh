#!/usr/bin/env bash

# ==============================================================================
# Lambda Function Watcher Script
# ==============================================================================
# This script watches for changes in the Lambda function code and automatically
# redeploys the function to LocalStack when changes are detected.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

readonly LAMBDA_DIR="$PROJECT_ROOT/lambda-nuclei-scanner"
WATCH_INTERVAL=2  # seconds

usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --interval N     Set watch interval in seconds (default: 2)"
    echo "  --test           Test Lambda function after each deployment"
    echo "  --help           Show this help message"
}

check_prerequisites() {
    log_header "Checking Prerequisites"
    require_tool "curl"
    require_tool "md5sum"
    
    if [ ! -d "$LAMBDA_DIR" ]; then
        fail "Lambda directory not found: $LAMBDA_DIR"
    fi
    
    if [ ! -f "${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh" ]; then
        fail "Deploy script not found: ${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh"
    fi
    
    if ! curl -s "http://localhost:4566/_localstack/health" | grep -q "available"; then
        fail "LocalStack is not running. Please start it with: docker-compose up -d localstack"
    fi
    
    log_success "All prerequisites met"
}

deploy_lambda() {
    log_header "Building and Deploying Lambda Function"
    local deploy_script="${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh"
    local deploy_flags="--create-layers"

    if [ "${TEST_LAMBDA:-false}" = "true" ]; then
        deploy_flags="$deploy_flags --test"
    fi

    if bash "$deploy_script" $deploy_flags; then
        log_success "Lambda function deployed successfully"
    else
        log_error "Lambda function deployment failed"
    fi
}

calculate_hash() {
    find "$LAMBDA_DIR" -type f \( -name "*.go" -o -name "*.yaml" -o -name "*.yml" -o -name "*.json" \) -not -path "*/\.*" | sort | xargs cat | md5sum | awk '{print $1}'
}

watch_lambda() {
    log_header "Starting Lambda Function Watcher"
    log_info "Watching for changes in: $LAMBDA_DIR"
    log_info "Watch interval: ${WATCH_INTERVAL} seconds"
    log_info "Press Ctrl+C to stop watching"
    
    local last_hash
    last_hash=$(calculate_hash)
    
    deploy_lambda # Initial deployment
    
    while true; do
        sleep "$WATCH_INTERVAL"
        
        local current_hash
        current_hash=$(calculate_hash)
        
        if [ "$current_hash" != "$last_hash" ]; then
            log_info "Changes detected in Lambda function code."
            deploy_lambda
            last_hash=$current_hash
            log_info "Watching for changes... (Ctrl+C to stop)"
        fi
    done
}

main() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --interval) WATCH_INTERVAL=$2; shift 2 ;;
            --test) TEST_LAMBDA=true; shift ;;
            --help) usage; exit 0 ;;
            *) fail "Unknown option: $1" ;;
        esac
    done
    
    export TEST_LAMBDA=${TEST_LAMBDA:-false}
    
    trap 'echo -e "\n${YELLOW}[INFO]${NC} Watcher stopped"; exit 0' INT
    
    check_prerequisites
    watch_lambda
}

main "$@"