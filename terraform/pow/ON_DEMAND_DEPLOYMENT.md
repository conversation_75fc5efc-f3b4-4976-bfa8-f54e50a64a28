# On-Demand PoW Infrastructure Deployment

## Overview

The on-demand PoW system allows you to start and stop demo target infrastructure through the Nuclear Pond frontend, triggered via GitHub Actions and managed by Terraform.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend    │    │  Nuclear Pond │    │GitHub Actions │    │   Terraform   │
│   Dashboard   │───▶│   Backend API │───▶ │  Workflow     │───▶│ Apply/Destroy │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │                        │
                                │                        │                        ▼
                                │                        │              ┌───────────────────┐
                                │                        │              │   AWS Resources │
                                │                        │              │   - EC2 (2x)    │
                                │                        │              │   - ALB         │
                                │                        │              │   - Route53     │
                                │                        │              │   - Security    │
                                │                        │              │     Groups      │
                                │                        │              └───────────────────┘
                                │                        │                        
                                ▼                        ▼                        
                      ┌─────────────────┐    ┌─────────────────┐                  
                      │   Operation   │    │   Workflow    │                  
                      │   Tracking    │    │   Logs        │                  
                      └─────────────────┘    └─────────────────┘                  
```

## How It Works

The on-demand PoW system uses Terraform's conditional module creation to manage infrastructure:

1. **START Operation**: Sets `TF_VAR_enable_pow_targets="true"` → `module.pow_targets` has `count = 1` → Creates all AWS resources
2. **STOP Operation**: Sets `TF_VAR_enable_pow_targets="false"` → `module.pow_targets` has `count = 0` → Destroys all AWS resources

The workflow uses `terraform plan -target=module.pow_targets` and `terraform apply` for both operations. No `terraform destroy` is needed because the conditional module creation handles resource destruction automatically.

## Setup Instructions

### 1. Configure GitHub Repository

1. **Create GitHub Personal Access Token**:
   ```bash
   # Go to GitHub Settings → Developer settings → Personal access tokens → Fine-grained tokens
   # Create a NEW token with 'Actions' scope.
   # For organization repositories, ensure you select the organization as the 'Resource owner'
   # and grant 'Repository access' to the specific repository.
   ```

2. **Set Repository Secrets**:
   ```bash
   # In your GitHub repository settings (or organization settings if applicable) → Secrets and variables → Actions
   # These secrets are typically configured at the repository level, or at the organization level
   # with appropriate access policies if shared across multiple repositories.
   AWS_ACCESS_KEY_ID=your_aws_access_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key
   AWS_REGION=us-east-1
   ```

### 2. Configure Nuclear Pond Backend

Add environment variables to your backend deployment:

```bash
# Required for PoW control functionality
export GITHUB_TOKEN="ghp_your_fine_grained_github_token_here" # Use the fine-grained PAT created above
export GITHUB_REPOSITORY="your-organization/your-repo-name" # For organization repositories, use 'organization-name/repo-name'

# Existing required variables
export AWS_REGION="us-east-1"
export AWS_LAMBDA_FUNCTION_NAME="your_lambda_function"
export AWS_DYNAMODB_TABLE="your_dynamodb_table"
export AWS_S3_BUCKET="your_s3_bucket"
```

## Troubleshooting

### Common Issues

1. **GitHub Actions not triggering**:
   - Verify `GITHUB_TOKEN` has proper permissions
   - Check `GITHUB_REPOSITORY` format (owner/repo)
   - Ensure repository dispatch events are enabled

2. **Terraform failures**:
   - Verify AWS credentials in GitHub secrets
   - Check terraform state consistency
   - Review AWS service limits
   - **Network dependency**: Ensure both `module.network` and `module.pow_targets` are targeted for START operations

3. **VPC/Internet Gateway errors**:
   - **Most common**: Network infrastructure not deployed or improperly tagged
   - Check if VPC exists and has proper tags (Name: {project_name}-vpc)
   - Verify public subnets exist and have proper tags (Type: Public)
   - Ensure network module was deployed successfully before PoW targets

4. **Frontend errors**:
   - Verify API URL and key configuration
   - Check CORS settings in backend
   - Monitor browser console for errors

### Debugging Steps

1. **Check Backend Logs**:
   ```bash
   # Look for PoW control requests and GitHub API calls
   grep "PoW control" /var/log/nuclear-pond/server.log
   ```

2. **Check GitHub Actions**:
   - Navigate to repository → Actions tab
   - Review workflow runs for errors
   - Check step-by-step execution logs
   - Look for "terraform plan -target=module.pow_targets" commands

3. **Verify AWS Resources**:
   ```bash
   # Check actual resource status
   aws ec2 describe-instances --filters "Name=tag:Name,Values=*pow*"
   aws elbv2 describe-load-balancers --names "*pow*"
   
   # Check network infrastructure
   aws ec2 describe-vpcs --filters "Name=tag:Name,Values=*nuclear-pond*"
   aws ec2 describe-internet-gateways --filters "Name=attachment.vpc-id,Values=vpc-xxxxx"
   aws ec2 describe-route-tables --filters "Name=vpc-id,Values=vpc-xxxxx"
   
   # Check terraform state
   terraform state list | grep -E "(network|pow_targets)"
   terraform show -json | jq '.values.root_module.child_modules[] | select(.address == "module.network")'
   terraform show -json | jq '.values.root_module.child_modules[] | select(.address == "module.pow_targets")'
   ```

### Key Implementation Details

**Important**: The PoW infrastructure depends on the network module for VPC and internet connectivity.

**Deployment Strategy**:
- **START**: Targets only `module.pow_targets` - network infrastructure is discovered automatically via data sources
- **STOP**: Targets only `module.pow_targets` to destroy PoW resources while preserving network infrastructure

**Why this approach**:
1. Network infrastructure is shared across multiple modules (backend, frontend, PoW)
2. PoW targets now use data sources to discover existing network infrastructure automatically
3. No need to target network module - prevents unwanted changes to existing network resources
4. Preserving network on STOP saves costs while maintaining infrastructure for other services
