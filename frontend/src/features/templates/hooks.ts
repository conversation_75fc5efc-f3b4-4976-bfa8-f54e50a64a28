import { useRequest } from "alova/client";
import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { templateService } from "./services";
import type { TemplateFilterOptions, TemplateMetadata, TemplateSeverity } from "./types";

/**
 * A consolidated hook for managing the state of the template selection feature.
 *
 * This hook simplifies the previous implementation by:
 * - Fetching all template data with a single API call.
 * - Deriving all necessary data (templates, tags, summaries) from the single data source.
 * - Consolidating state management for filters and selections into one place.
 * - Exposing a simplified and consistent API to the UI components.
 *
 * This approach aligns with the simplicity guidelines by reducing redundant code,
 * minimizing API requests, and avoiding over-engineered state management.
 */
export function useTemplateSelection() {
  // State for user selections and filter criteria
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);
  const [filterOptions, setFilterOptions] = useState<TemplateFilterOptions>({
    search: "",
    severities: [],
    tags: [],
  });

  // Single API request to fetch all template data
  const {
    data: templatesData,
    loading: isLoading,
    error: requestError,
  } = useRequest(templateService.getTemplates(), {
    immediate: true,
    initialData: { templates: {}, summary: { total: 0, by_severity: {} } },
  });

  // Memoized derivation of data from the single API response
  const { availableTemplates, templateSummary, availableTags } = useMemo(() => {
    const allTemplates = templatesData?.templates ? (Object.values(templatesData.templates) as TemplateMetadata[]) : [];
    const summary = templatesData?.summary || { total: 0, by_severity: {} };

    const tags = new Set<string>();
    allTemplates.forEach((template) => {
      template.tags.forEach((tag) => tags.add(tag));
    });

    return {
      availableTemplates: allTemplates,
      templateSummary: summary,
      availableTags: Array.from(tags).sort(),
    };
  }, [templatesData]);

  // Memoized filtering logic
  const filteredTemplates = useMemo(() => {
    if (!availableTemplates.length) return [];

    return availableTemplates.filter((template) => {
      // Search filter
      const searchTerm = filterOptions.search?.toLowerCase().trim();
      if (searchTerm) {
        const searchableText = `${template.id} ${template.name} ${template.description} ${template.tags.join(" ")} ${template.author.join(" ")}`.toLowerCase();
        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Severity filter
      if (filterOptions.severities && filterOptions.severities.length > 0 && !filterOptions.severities.includes(template.severity)) {
        return false;
      }

      // Tag filter
      if (filterOptions.tags && filterOptions.tags.length > 0 && !template.tags.some((tag) => filterOptions.tags?.includes(tag))) {
        return false;
      }

      return true;
    });
  }, [availableTemplates, filterOptions]);

  // Simplified error message
  const error = useMemo(() => {
    if (requestError) {
      return `Failed to load templates: ${requestError.message || "Unknown error"}.`;
    }
    return null;
  }, [requestError]);

  // Memoized statistics for the current selection
  const selectionStats = useMemo(() => {
    const stats: { total: number; bySeverity: Record<TemplateSeverity, number> } = {
      total: selectedTemplates.length,
      bySeverity: { critical: 0, high: 0, medium: 0, low: 0, info: 0 },
    };

    selectedTemplates.forEach((id) => {
      const template = availableTemplates.find((t) => t.id === id);
      if (template) {
        stats.bySeverity[template.severity]++;
      }
    });
    return stats;
  }, [selectedTemplates, availableTemplates]);

  // --- ACTIONS ---

  const toggleTemplateSelection = useCallback((templateId: string) => {
    setSelectedTemplates((prev) => (prev.includes(templateId) ? prev.filter((id) => id !== templateId) : [...prev, templateId]));
  }, []);

  const selectAllFiltered = useCallback(() => {
    setSelectedTemplates(filteredTemplates.map((t) => t.id));
  }, [filteredTemplates]);

  const selectBySeverity = useCallback(
    (severity: TemplateSeverity) => {
      const severityIds = filteredTemplates.filter((t) => t.severity === severity).map((t) => t.id);
      setSelectedTemplates((prev) => Array.from(new Set([...prev, ...severityIds])));
    },
    [filteredTemplates],
  );

  const clearSelection = useCallback(() => {
    setSelectedTemplates([]);
  }, []);

  const updateFilterOptions = useCallback((newOptions: Partial<TemplateFilterOptions>) => {
    setFilterOptions((prev) => ({ ...prev, ...newOptions }));
  }, []);

  const filterBySeverity = useCallback((severity: TemplateSeverity) => {
    setFilterOptions((prev) => {
      const currentSeverities = prev.severities || [];
      const newSeverities = currentSeverities.includes(severity) ? currentSeverities.filter((s) => s !== severity) : [...currentSeverities, severity];
      return { ...prev, severities: newSeverities };
    });
  }, []);

  const filterByTag = useCallback((tag: string) => {
    setFilterOptions((prev) => {
      const currentTags = prev.tags || [];
      const newTags = currentTags.includes(tag) ? currentTags.filter((t) => t !== tag) : [...currentTags, tag];
      return { ...prev, tags: newTags };
    });
  }, []);

  const clearFilters = useCallback(() => {
    setFilterOptions({ search: "", severities: [], tags: [] });
  }, []);

  return {
    // Data
    selectedTemplates,
    filteredTemplates,
    availableTags,
    templateSummary,
    selectionStats,

    // State
    isLoading,
    error,
    filterOptions,

    // Actions
    toggleTemplateSelection,
    selectAllFiltered,
    selectBySeverity,
    clearSelection,
    updateFilterOptions,
    filterBySeverity,
    filterByTag,
    clearFilters,
  };
}
