package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/template"
	"nuclear_pond/pkg/types"
	"strings"

	"github.com/go-chi/render"
)

// GetTemplatesHandler handles the templates endpoint.
func GetTemplatesHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Templates request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	// Get template metadata with comprehensive error handling
	templateIndex, err := template.GetTemplateMetadata()
	if err != nil {
		log.Printf("Error getting template metadata: %v", err)

		// Determine appropriate HTTP status and error message based on error type
		var statusCode int
		var errorType string
		var userMessage string

		if strings.Contains(err.<PERSON>r(), "NoSuchKey") || strings.Contains(err.Error(), "not found") {
			statusCode = http.StatusNotFound
			errorType = "Templates Not Available"
			userMessage = "Template metadata is not available. The metadata generation process may not have completed yet. Please try again in a few minutes."
		} else if strings.Contains(err.<PERSON><PERSON>r(), "AccessDenied") || strings.Contains(err.<PERSON><PERSON><PERSON>(), "Forbidden") {
			statusCode = http.StatusForbidden
			errorType = "Access Denied"
			userMessage = "Unable to access template metadata. Please contact your administrator."
		} else if strings.Contains(err.Error(), "NoSuchBucket") {
			statusCode = http.StatusServiceUnavailable
			errorType = "Service Configuration Error"
			userMessage = "Template service is not properly configured. Please contact your administrator."
		} else if strings.Contains(err.Error(), "parse") || strings.Contains(err.Error(), "decode") {
			statusCode = http.StatusServiceUnavailable
			errorType = "Data Format Error"
			userMessage = "Template metadata is corrupted. The system administrator has been notified."
		} else {
			statusCode = http.StatusInternalServerError
			errorType = "Internal Server Error"
			userMessage = "An unexpected error occurred while loading templates. Please try again later."
		}

		render.Status(r, statusCode)
		render.JSON(w, r, types.Response{
			Error:   errorType,
			Message: userMessage,
		})

		return
	}

	// Add metadata about the response for debugging
	log.Printf("Successfully retrieved template metadata with %d templates (version: %s)",
		len(templateIndex.Templates), templateIndex.Version)

	// Log template summary for debugging
	if templateIndex.Summary.Total > 0 {
		log.Printf("Template summary - Total: %d, Critical: %d, High: %d, Medium: %d, Low: %d, Info: %d",
			templateIndex.Summary.Total,
			templateIndex.Summary.BySeverity["critical"],
			templateIndex.Summary.BySeverity["high"],
			templateIndex.Summary.BySeverity["medium"],
			templateIndex.Summary.BySeverity["low"],
			templateIndex.Summary.BySeverity["info"])
	}

	render.JSON(w, r, templateIndex)
}
