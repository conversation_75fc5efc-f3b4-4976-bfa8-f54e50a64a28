import { useRequest } from "alova/client";
import { useEffect, useState } from "react";
import { powControlService } from "./services";
import type { PowControlRequest, PowControlResponse } from "./types";

/**
 * Hook for controlling PoW infrastructure (start/stop)
 */
export const usePowControl = () => {
  const {
    send: controlInfrastructure,
    loading: isOperationInProgress,
    data: lastOperation,
    error: operationError,
    onSuccess,
  } = useRequest((request: PowControlRequest) => powControlService.controlInfrastructure(request), {
    immediate: false,
  });

  onSuccess(({ data }: { data: PowControlResponse }) => {
    // In a real app, you would invalidate a query that fetches the PoW status
    console.log("PoW control success:", data);
  });

  return {
    controlInfrastructure,
    isOperationInProgress,
    lastOperation,
    operationError,
  };
};

const usePowStatus = (): { status: "online" | "offline" | "loading" } => {
  const [status, setStatus] = useState<"online" | "offline" | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    powControlService
      .getStatus()
      .then((data: { status: "online" | "offline" }) => {
        setStatus(data.status as "online" | "offline");
        setIsLoading(false);
      })
      .catch(() => {
        setStatus("offline");
        setIsLoading(false);
      });
  }, []);

  return {
    status: isLoading ? "loading" : (status as "online" | "offline"),
  };
};

/**
 * Combined hook for PoW management with all necessary state and actions
 */
export const usePowManagement = () => {
  const { controlInfrastructure, isOperationInProgress, lastOperation, operationError } = usePowControl();
  const { status } = usePowStatus();

  const startInfrastructure = () => {
    controlInfrastructure({ action: "start" });
  };

  const stopInfrastructure = () => {
    controlInfrastructure({ action: "stop" });
  };

  return {
    // Status
    status,

    // Operations
    startInfrastructure,
    stopInfrastructure,
    isOperationInProgress,
    lastOperation,
    operationError,
  };
};
