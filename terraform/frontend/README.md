# Frontend Module

This Terraform module deploys the Nuclear Pond frontend as a static website on AWS S3, with an optional AWS CloudFront CDN distribution for enhanced performance, security, and global content delivery. It automates the creation of all necessary AWS infrastructure.

For a comprehensive understanding of the overall frontend architecture, including AWS components, data flow, security, and cost considerations, please refer to the [Frontend Architecture document](../../frontend/docs/frontend-architecture.md).

The module creates a complete frontend hosting solution:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Architecture                    │
├─────────────────────────┬───────────────────────────────────┤
│     Development Mode    │        Production Mode            │
│                         │                                   │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ S3 Static       │   │   │ CloudFront Distribution     │ │
│  │ Website         │   │   │ - Global CDN                │ │
│  │ - Direct access │   │   │ - HTTPS/SSL                 │ │
│  │ - HTTP only     │   │   │ - Custom domain support    │ │
│  │ - Fast deploy   │   │   │ - Caching                   │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
│           │             │                   │               │
│           │             │                   │               │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ S3 Bucket       │   │   │ S3 Bucket (Origin)          │ │
│  │ - Static files  │   │   │ - Static files              │ │
│  │ - Public read   │   │   │ - Private (OAC access)     │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
└─────────────────────────┴───────────────────────────────────┘
```

## Features

- ✅ **Dual Deployment Modes**: Supports Development (S3 direct access) and Production (S3 origin with CloudFront CDN) setups.
- ✅ **Automated Deployment Scripts**: Generates shell scripts to streamline the building of the React application and synchronization of assets to S3, including CloudFront cache invalidation where applicable.
- ✅ **Environment Configuration**: Facilitates environment-specific configurations for the frontend application, often through `.env` file generation assistance (see deployment guide for details).
- ✅ **Custom Domain Support**: Integrates with AWS Route53 and ACM for using custom domain names with HTTPS via CloudFront.
- ✅ **Security Best Practices**: Implements Origin Access Control (OAC) when CloudFront is used, ensuring S3 content is served only through the CDN. Marks sensitive variables like API keys appropriately.
- ✅ **Cost Optimization**: Allows selection of CloudFront price classes to balance performance and cost.
- ✅ **Terraform Best Practices**: Designed for compatibility with Terraform features like `count` for conditional creation, `for_each`, and `depends_on`.

## Quick Start

### Basic Usage

```hcl
# Configure the required us-east-1 provider for ACM certificates
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

module "frontend" {
  source = "./frontend"

  # Provider configuration
  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # Required variables
  project_name = "fast-scan"
  environment  = "dev"
  api_url      = "http://your-backend-alb-dns-name"
  api_key      = "your-secure-api-key"

  tags = {
    Environment = "dev"
    Project     = "fast-scan"
  }
}
```

### Production Configuration

```hcl
module "frontend" {
  source = "./frontend"

  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  project_name = "fast-scan"
  environment  = "prod"
  api_url      = "http://your-backend-alb-dns-name"
  api_key      = "your-secure-api-key"

  # Enable CloudFront for production
  enable_cloudfront = true
  cloudfront_price_class = "PriceClass_100"

  # Custom domain configuration (manual DNS management)
  frontend_domain_name = "app.yourdomain.com"
  # Route53 variables removed - manual DNS setup required

  tags = {
    Environment = "prod"
    Project     = "fast-scan"
  }
}
```

### Conditional Deployment

```hcl
module "frontend" {
  source = "./frontend"
  count  = var.enable_frontend_deployment ? 1 : 0

  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # ... other configuration
}
```

## Input Variables

### Required Variables

| Name | Description | Type |
|------|-------------|------|
| `project_name` | Name of the project | `string` |
| `api_url` | URL of the backend API | `string` |
| `api_key` | API key for backend authentication | `string` |

### Optional Variables

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `environment` | Deployment environment | `string` | `"dev"` |
| `enable_cloudfront` | Whether to create CloudFront distribution | `bool` | `false` |
| `frontend_domain_name` | Custom domain name | `string` | `""` |
| `demo_password` | Demo password for the application | `string` | `"TestPass"` |

For a complete list of variables, see [variables.tf](./variables.tf).

## Outputs

### Key Outputs

| Name | Description |
|------|-------------|
| `frontend_url` | Primary URL for accessing the frontend |
| `frontend_s3_bucket_name` | Name of the S3 bucket |
| `frontend_cloudfront_domain_name` | CloudFront distribution domain (if enabled) |
| `deployment_info` | Complete deployment information |

For a complete list of outputs, see [outputs.tf](./outputs.tf).

## Deployment Modes Overview

This module supports two primary deployment modes, controlled by the `enable_cloudfront` variable. For a detailed architectural comparison, see the [Frontend Architecture document](../../frontend/docs/frontend-architecture.md).

### Development Mode (`enable_cloudfront = false`)

- **Infrastructure**: Provisions an S3 bucket configured for static website hosting.
- **Access**: Direct HTTP access to the S3 website endpoint.
- **Benefits**: Faster deployment iterations (no CDN cache invalidation), lower cost, simpler for debugging direct file access.
- **Use Cases**: Ideal for local development, rapid testing, and internal demos.

### Production Mode (`enable_cloudfront = true`)

- **Infrastructure**: Provisions an S3 bucket (as origin) and an AWS CloudFront distribution. Optionally configures Route53 for custom domains and ACM for SSL/TLS certificates.
- **Access**: Via CloudFront URL or custom domain (HTTPS).
- **Benefits**: Global content delivery, HTTPS, DDoS protection (AWS Shield Standard), improved performance through caching, and secure access to S3 origin via OAC.
- **Use Cases**: Recommended for production, staging, and any public-facing deployments.

Detailed instructions for configuring and deploying in either mode are available in the [Frontend Deployment Guide](../../frontend/docs/frontend-deployment.md).

## Automated Deployment Support

This module aids in automating the deployment process by generating environment-specific shell scripts (e.g., `deploy-frontend-dev.sh`, `deploy-frontend-prod.sh`) in the module's output directory (`./frontend/` relative to your Terraform root after apply).

**Typical Workflow:**
1.  **Deploy Infrastructure**: Run `terraform apply -target=module.frontend` to provision AWS resources.
2.  **Deploy Frontend Code**: Navigate to the directory containing the generated scripts (e.g., `cd terraform/frontend`) and execute the script corresponding to your target environment (e.g., `./deploy-frontend-prod.sh`).

These scripts typically handle:
    *   Building the React application (e.g., `yarn build:prod`).
    *   Syncing static assets to the S3 bucket.
    *   Creating CloudFront cache invalidations (if CloudFront is enabled).

For the complete deployment steps, including script usage and manual alternatives, please refer to the [Frontend Deployment Guide](../../frontend/docs/frontend-deployment.md).

## Provider Requirements

This module requires two AWS provider configurations:

1. **Default provider**: For most resources (S3, Route53, etc.)
2. **us-east-1 provider**: For ACM certificates used with CloudFront

```hcl
# Required in calling module
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

module "frontend" {
  source = "./frontend"
  
  providers = {
    aws.us_east_1 = aws.us_east_1
  }
  
  # ... other variables
}
```

## Security Features

- **Origin Access Control (OAC)**: Secure CloudFront to S3 access
- **Private S3 bucket**: When CloudFront is enabled
- **HTTPS enforcement**: Automatic SSL certificates
- **Sensitive variable handling**: API keys marked as sensitive


## Troubleshooting

This section covers common issues specific to this Terraform module. For broader frontend deployment troubleshooting (application errors, access issues, script failures), consult the comprehensive troubleshooting section in the [Frontend Deployment Guide](../../frontend/docs/frontend-deployment.md).

### Common Module Issues

1. **Count/for_each errors**: Ensure no local provider configurations in module
2. **ACM certificate issues**: Verify us-east-1 provider is configured
3. **CloudFront deployment slow**: Cache invalidation can take 5-15 minutes
4. **Custom domain issues**: Check Route53 zone configuration

### Useful Commands

```bash
# Check S3 bucket
aws s3 ls s3://your-bucket-name

# Check CloudFront distribution
aws cloudfront list-distributions

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id ABCD123 --paths "/*"

# Check ACM certificates
aws acm list-certificates --region us-east-1
```

## Migration from Legacy Module

If migrating from a module with local provider configurations:

1. Remove any `provider` blocks from module files
2. Add `terraform.tf` with required_providers configuration
3. Update calling module to pass providers
4. Test with `terraform plan` before applying

## Contributing

When modifying this module:

1. Update variable descriptions and validation
2. Add appropriate tags to all resources
3. Update this documentation
4. Test with both CloudFront enabled and disabled
5. Verify provider configuration works correctly

## Related Documentation

- [Frontend Architecture](../../frontend/docs/frontend-architecture.md) - Detailed architectural overview.
- [Frontend Deployment Guide](../../frontend/docs/frontend-deployment.md) - Comprehensive deployment instructions.
- `DEPLOYMENT-{ENV}.md` (auto-generated in `./frontend/`) - Environment-specific output details after Terraform apply.
- [Main Infrastructure README](../README.md) - Overview of the entire project's infrastructure.
- [Nuclear Pond Backend Module](../nuclear_pond_backend/README.md) - Information on the backend service.
- [AWS CloudFront Documentation](https://docs.aws.amazon.com/cloudfront/) - Official CloudFront reference.
