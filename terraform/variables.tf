variable "project_name" {
  description = "Name of the project to create and must be unique as S3 bucket names are global"
  default     = "fast-scan"
}

# ==============================================================================
# ENVIRONMENT AND DEPLOYMENT CONFIGURATION
# ==============================================================================

variable "aws_region" {
  description = "AWS region for resource deployment"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Deployment environment (dev, staging, prod)"
  type        = string
  default     = "dev"

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "deployment_id" {
  description = "Unique deployment identifier (e.g., timestamp, git commit, or custom ID)"
  type        = string
  default     = ""
}

# ==============================================================================
# ENHANCED TAGGING SYSTEM
# ==============================================================================

variable "tags" {
  description = "Common tags to apply to all resources for cost tracking and organization"
  type        = map(string)
  default     = {}
}

variable "owner" {
  description = "Owner of the infrastructure (team or individual)"
  type        = string
  default     = "security-team"
}

variable "cost_center" {
  description = "Cost center for billing and chargeback"
  type        = string
  default     = "security"
}

variable "business_unit" {
  description = "Business unit responsible for the infrastructure"
  type        = string
  default     = "security"
}

variable "backup_policy" {
  description = "Backup policy for resources (none, daily, weekly)"
  type        = string
  default     = "none"
  
  validation {
    condition     = contains(["none", "daily", "weekly"], var.backup_policy)
    error_message = "Backup policy must be one of: none, daily, weekly."
  }
}

variable "monitoring_policy" {
  description = "Monitoring policy for resources (none, basic, enhanced)"
  type        = string
  default     = "basic"
  
  validation {
    condition     = contains(["none", "basic", "enhanced"], var.monitoring_policy)
    error_message = "Monitoring policy must be one of: none, basic, enhanced."
  }
}

variable "compliance_framework" {
  description = "Compliance framework requirements (none, soc2, pci, hipaa)"
  type        = string
  default     = "none"
}

# ==============================================================================
# RESOURCE DISCOVERY CONFIGURATION
# ==============================================================================

variable "enable_resource_discovery" {
  description = "Enable AWS Resource Groups and AWS Config for resource discovery"
  type        = bool
  default     = true
}


# ==============================================================================
# NUCLEI BINARY CONFIGURATION
# ==============================================================================

# Nuclei binary configuration
variable "nuclei_version" {
  description = "Nuclei version to use"
  default     = "3.1.7"
}

variable "nuclei_arch" {
  description = "Nuclei architecture to use"
  default     = "linux_amd64"
}

variable "nuclei_timeout" {
  type        = number
  description = "Lambda function timeout"
  default     = 900
}

variable "memory_size" {
  type        = number
  description = "Memory size for the Nuclei Lambda function in MB"
  default     = 512
}

variable "nuclei_lambda_environment" {
  description = "Deployment environment for the Nuclei Lambda module (dev, staging, prod)"
  type        = string
  default     = "dev"
}



# ==============================================================================
# MODULE FEATURE FLAGS
# ==============================================================================

# Proof of Work (PoW) target infrastructure variables
variable "enable_pow_targets" {
  description = "Whether to create PoW demo target infrastructure"
  type        = bool
  default     = false
}

variable "pow_create_hosted_zone" {
  description = "Whether to create the Route53 hosted zone if it doesn't exist"
  type        = bool
  default     = false
}

variable "pow_domain_name" {
  description = "Domain name registered through Route53 to use for PoW targets (e.g., fast-scan-demo-target.click)"
  type        = string
  default     = "fast-scan-demo-target.click"
}

# Frontend deployment variables (always enabled - mandatory service)

variable "frontend_domain_name" {
  description = "Domain name for the frontend (e.g., app.fast-scan.com)"
  type        = string
  default     = ""
}

variable "use_custom_domain" {
  description = "Whether to use the custom domain for CloudFront (allows staged deployment for manual DNS validation)"
  type        = bool
  default     = true
}



variable "enable_frontend_cloudfront" {
  description = "Whether to create CloudFront distribution for the frontend"
  type        = bool
  default     = true
}

variable "frontend_cloudfront_price_class" {
  description = "CloudFront price class (PriceClass_All, PriceClass_200, PriceClass_100)"
  type        = string
  default     = "PriceClass_100"
}

variable "frontend_environment" {
  description = "Deployment environment for the frontend (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# ==============================================================================
# AUTHENTICATION CONFIGURATION
# ==============================================================================

variable "jwt_secret" {
  description = "Secret key for JWT token signing and validation."
  type        = string
  sensitive   = true
  default     = "demo-jwt-secret-change-in-production"
}

variable "demo_password" {
  description = "Demo password for backend authentication."
  type        = string
  sensitive   = true
  default     = "TestPass"
}

# Network Infrastructure Variables
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_az1_cidr" {
  description = "CIDR block for public subnet in AZ1"
  type        = string
  default     = "********/24"
}

variable "public_subnet_az2_cidr" {
  description = "CIDR block for public subnet in AZ2"
  type        = string
  default     = "********/24"
}

variable "private_subnet_az1_cidr" {
  description = "CIDR block for private subnet in AZ1"
  type        = string
  default     = "********/24"
}



# Nuclear Pond Backend Variables

variable "nuclear_pond_environment" {
  description = "Deployment environment for the Nuclear Pond backend (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "nuclear_pond_task_cpu" {
  description = "CPU units for the Nuclear Pond ECS task (256, 512, 1024, etc.)"
  type        = string
  default     = "256"
}

variable "nuclear_pond_task_memory" {
  description = "Memory for the Nuclear Pond ECS task in MB (512, 1024, 2048, etc.)"
  type        = string
  default     = "512"
}

variable "nuclear_pond_desired_count" {
  description = "Desired number of Nuclear Pond ECS tasks"
  type        = number
  default     = 1
}

variable "nuclear_pond_container_port" {
  description = "Port on which the Nuclear Pond container listens"
  type        = number
  default     = 8082
}

variable "nuclear_pond_health_check_path" {
  description = "Path for ALB health checks for Nuclear Pond"
  type        = string
  default     = "/health-check"
}

variable "nuclear_pond_log_retention_days" {
  description = "Number of days to retain CloudWatch logs for Nuclear Pond"
  type        = number
  default     = 30
}

# ==============================================================================
# NUCLEI LAMBDA SECURITY VARIABLES
# ==============================================================================

variable "enable_vpc_isolation" {
  description = "Enable VPC isolation for Nuclei Lambda function"
  type        = bool
  default     = false
}

variable "nuclear_pond_enable_deletion_protection" {
  description = "Whether to enable deletion protection for the Nuclear Pond ALB"
  type        = bool
  default     = false
}

variable "github_token" {
  description = "GitHub token for PoW targets on demand deployment"
  type        = string
  default     = ""
}

variable "github_repository" {
  description = "GitHub repository for PoW targets on demand deployment"
  type        = string
  default     = ""
}