# ==============================================================================
# CLOUDWATCH RESOURCES FOR NUCLEAR POND BACKEND
# ==============================================================================

# CloudWatch Log Group for ECS Tasks
resource "aws_cloudwatch_log_group" "ecs_nuclear_pond_lg" {
  name              = "/ecs/${var.project_name}/${var.environment}/nuclearpond"
  retention_in_days = var.log_retention_days

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-ecs-logs"
    Environment = var.environment
    Component   = "backend"
  })
}

# CloudWatch Log Stream for the application
resource "aws_cloudwatch_log_stream" "nuclear_pond_app_stream" {
  name           = "nuclear-pond-app"
  log_group_name = aws_cloudwatch_log_group.ecs_nuclear_pond_lg.name
}

# CloudWatch Metric Filters for monitoring
resource "aws_cloudwatch_log_metric_filter" "error_count" {
  name           = "${var.project_name}-${var.environment}-error-count"
  log_group_name = aws_cloudwatch_log_group.ecs_nuclear_pond_lg.name
  pattern        = "[timestamp, request_id, level=\"ERROR\", ...]"

  metric_transformation {
    name      = "ErrorCount"
    namespace = "NuclearPond/${var.environment}"
    value     = "1"
  }
}

# CloudWatch Alarms for monitoring
resource "aws_cloudwatch_metric_alarm" "high_error_rate" {
  alarm_name          = "${var.project_name}-${var.environment}-high-error-rate"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "ErrorCount"
  namespace           = "NuclearPond/${var.environment}"
  period              = "300"
  statistic           = "Sum"
  threshold           = "10"
  alarm_description   = "This metric monitors error rate"
  alarm_actions       = [] # Add SNS topic ARN for notifications

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-high-error-rate"
    Environment = var.environment
    Component   = "backend"
  })
}

resource "aws_cloudwatch_metric_alarm" "ecs_cpu_high" {
  alarm_name          = "${var.project_name}-${var.environment}-ecs-cpu-high"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ECS CPU utilization"

  dimensions = {
    ServiceName = aws_ecs_service.nuclear_pond_service.name
    ClusterName = aws_ecs_cluster.nuclear_pond_cluster.name
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-ecs-cpu-high"
    Environment = var.environment
    Component   = "backend"
  })
}

resource "aws_cloudwatch_metric_alarm" "ecs_memory_high" {
  alarm_name          = "${var.project_name}-${var.environment}-ecs-memory-high"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ECS memory utilization"

  dimensions = {
    ServiceName = aws_ecs_service.nuclear_pond_service.name
    ClusterName = aws_ecs_cluster.nuclear_pond_cluster.name
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-ecs-memory-high"
    Environment = var.environment
    Component   = "backend"
  })
}

resource "aws_cloudwatch_metric_alarm" "alb_target_response_time" {
  alarm_name          = "${var.project_name}-${var.environment}-alb-response-time"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "TargetResponseTime"
  namespace           = "AWS/ApplicationELB"
  period              = "300"
  statistic           = "Average"
  threshold           = "5"
  alarm_description   = "This metric monitors ALB target response time"

  dimensions = {
    LoadBalancer = aws_lb.nuclear_pond_alb.arn_suffix
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-alb-response-time"
    Environment = var.environment
    Component   = "backend"
  })
}
