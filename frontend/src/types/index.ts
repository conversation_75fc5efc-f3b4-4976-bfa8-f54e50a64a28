// Scan types
export interface ScanTarget {
  domain: string;
}

export interface ScanConfig {
  target_count: number; // Number of targets in the scan
  targets: string[]; // Actual targets (may be empty for performance, populated on demand with include_targets=true)
  // Deprecated in UI: keep for compatibility with backend responses
  batches?: number;
  threads?: number;
  // New frontend input: desired number of scanners (concurrency)
  scanners?: number;
  templates: string[]; // Template IDs for specific template selection
}

export interface ScanRequest {
  requestId: string;
  timestamp: string;
  status: "queued" | "running" | "completed" | "failed";
  config: ScanConfig;
  completedAt?: string;
  performance?: {
    durationSeconds: number;
  };
}

// Severity level type for consistent severity handling across the application
export type SeverityLevel = "critical" | "high" | "medium" | "low" | "info";

export interface Vulnerability {
  id: string;
  target: string;
  title: string;
  description: string;
  severity: SeverityLevel;
  discoveredAt: string;
  remediation?: string;
}

export interface ScanResult {
  requestId: string;
  timestamp: string;
  status: "completed" | "failed";
  summary: {
    totalVulnerabilities: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    infoCount: number;
  };
  vulnerabilities: Vulnerability[];
}

// Results tracking structure grouped as nested object (server-provided)
export interface ResultsTracking {
  status?: "waiting_results" | "partial" | "aggregated" | "validated" | string;
  findings_keys?: string[];
  aggregated_findings_key?: string;
  batches_received?: number;
  expected_batches?: number;
  updated_at?: string;
}

// Enhanced scan status details matching backend response
export interface ScanStatusDetails {
  scanId: string;
  requestId: string;
  status: "queued" | "running" | "completed" | "failed" | "unknown";
  config: ScanConfig;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  message?: string;
  error?: string;
  results_tracking?: ResultsTracking;
}

// Scan results details matching backend response
export interface ScanResultsDetails {
  scanId: string;
  requestId: string;
  status: string;
  config: ScanConfig;
  createdAt: string;
  completedAt?: string;
  resultsSummary?: {
    totalFindings: number;
    targetsScanned: number;
    durationSeconds: number;
  };
  resultsLocation?: string;
  findings?: Record<string, unknown>[];
}

// Scan execution details for enhanced visibility
export interface ScanExecutionDetails {
  workersUsed?: number;
  templatesUsed?: string[];
  executionDuration?: number;
  targetsProcessed?: number;
  progressPercentage?: number;
  currentPhase?: string;
}

// Template metadata structure matching backend
export interface Template {
  id: string;
  name: string;
  author: string[];
  description: string;
  severity: SeverityLevel;
  tags: string[];
  reference: string[];
  metadata: Record<string, unknown>;
}

// Template index structure matching backend
export interface TemplateIndex {
  version: string;
  templates: Record<string, Template>;
  summary: TemplateSummary;
}

// Template summary structure matching backend
export interface TemplateSummary {
  total: number;
  by_severity: Record<string, number>;
}

// Scan status enumeration
export type ScanStatus = "queued" | "running" | "completed" | "failed" | "unknown";
