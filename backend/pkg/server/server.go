package server

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/config"

	"github.com/joho/godotenv"
)

// HandleRequests sets up and starts the HTTP server.
func HandleRequests() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found or error loading .env file:", err)
	}

	// Check if the server is configured correctly
	config.ServerCheck()

	log.Printf("Starting Nuclear Pond server")

	router := NewRouter()

	log.Println("Nuclear Pond server starting on port 8082...")
	log.Println("CORS enabled for all origins")
	log.Println("API key authentication enabled for protected endpoints")

	if err := http.ListenAndServe(":8082", router); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
