import { calculateProgressEstimate } from "@/utils/progress-estimation";
import { CheckCircleIcon, ClockIcon, DatabaseIcon, PlayIcon, RefreshCwIcon, XCircleIcon } from "lucide-react";
import type React from "react";
import { useEffect, useMemo, useRef, useState } from "react";

// Status badge component with visual indicators
export const ScanStatusBadge: React.FC<{
  status: string;
  size?: "sm" | "md" | "lg";
  batchProgress?: { received: number; expected: number };
}> = ({ status, size = "md", batchProgress }) => {
  const getStatusConfig = () => {
    switch (status) {
      case "queued":
        return {
          badge: "badge-outline",
          icon: ClockIcon,
          text: "Queued",
          color: "text-base-content/70",
        };
      case "running":
        return {
          badge: "badge-info",
          icon: RefreshCwIcon,
          text: "Running",
          color: "text-info-content",
          animate: "animate-spin",
        };
      case "completed":
        return {
          badge: "badge-success",
          icon: CheckCircleIcon,
          text: "Completed",
          color: "text-success-content",
        };
      case "failed":
        return {
          badge: "badge-error",
          icon: XCircleIcon,
          text: "Failed",
          color: "text-error-content",
        };
      case "waiting_results":
        return {
          badge: "badge-info",
          icon: ClockIcon,
          text: "Processing Results",
          color: "text-info-content",
        };
      case "partial":
        return {
          badge: "badge-warning",
          icon: RefreshCwIcon,
          text: `Processing (${batchProgress?.received}/${batchProgress?.expected})`,
          color: "text-warning-content",
        };
      case "aggregated":
        return {
          badge: "badge-info",
          icon: DatabaseIcon,
          text: "Aggregating Results",
          color: "text-info-content",
        };
      case "validated":
        return {
          badge: "badge-success",
          icon: CheckCircleIcon,
          text: "Validated",
          color: "text-success-content",
        };
      default:
        return {
          badge: "badge-outline",
          icon: ClockIcon,
          text: status,
          color: "text-base-content/70",
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;
  const sizeClass = size === "sm" ? "badge-sm" : size === "lg" ? "badge-lg" : "";

  return (
    <div className={`badge ${config.badge} ${sizeClass} gap-1`}>
      <Icon className={`h-3 w-3 ${config.color} ${config.animate || ""}`} />
      {config.text}
    </div>
  );
};

// Progress metrics display component
export const ProgressMetrics: React.FC<{
  targetCount: number;
  targetsScanned?: number | undefined;
  vulnerabilitiesFound?: number | undefined;
  status: "queued" | "running" | "completed" | "failed";
}> = ({ targetCount, targetsScanned, vulnerabilitiesFound, status }) => {
  return (
    <div className="space-y-3">
      {/* Metrics grid */}
      <div className="grid grid-cols-2 gap-3">
        <div className="text-center p-3 bg-base-200/30 rounded-lg">
          <div className="text-lg font-bold text-primary">{(status === "completed" ? targetsScanned || targetCount : targetCount).toLocaleString()}</div>
          <div className="text-xs text-base-content/70">{status === "completed" ? "Targets Scanned" : "Total Targets"}</div>
        </div>

        <div className="text-center p-3 bg-base-200/30 rounded-lg">
          <div className="text-lg font-bold text-warning">{vulnerabilitiesFound || 0}</div>
          <div className="text-xs text-base-content/70">Vulnerabilities</div>
        </div>
      </div>

      {/* Running scan additional info */}
      {status === "running" && targetsScanned !== undefined && (
        <div className="text-center text-xs text-base-content/70">
          {targetsScanned} of {targetCount} targets processed
        </div>
      )}
    </div>
  );
};

export const RealTimeDuration: React.FC<{
  startTime?: string;
  endTime?: string | undefined;
  status: "queued" | "running" | "completed" | "failed";
  realTime?: boolean;
}> = ({ startTime, endTime, status, realTime = true }) => {
  const [now, setNow] = useState(Date.now());
  const runningStartRef = useRef<number | null>(null);

  // Capture a stable local start when we enter running; clear when leaving
  useEffect(() => {
    if (status === "running" && runningStartRef.current === null) {
      runningStartRef.current = Date.now();
    }
    if (status !== "running") {
      runningStartRef.current = null;
    }
  }, [status]);

  // Tick every second only while running
  useEffect(() => {
    if (!realTime || status !== "running") return;
    setNow(Date.now());
    const id = setInterval(() => setNow(Date.now()), 1000);
    return () => clearInterval(id);
  }, [realTime, status]);

  const formatDuration = (seconds: number) => {
    if (seconds < 0) return "0s";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    if (hours > 0) return `${hours}h ${minutes}m ${secs}s`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  const seconds =
    status === "running"
      ? Math.max(0, Math.floor((now - (runningStartRef.current ?? now)) / 1000))
      : startTime && endTime
        ? Math.max(0, Math.floor((new Date(endTime).getTime() - new Date(startTime).getTime()) / 1000))
        : 0;

  return (
    <div className="text-center">
      <div className="text-lg font-semibold">{formatDuration(seconds)}</div>
    </div>
  );
};

// Scan phase indicator component
export const ScanPhaseIndicator: React.FC<{
  status: "queued" | "running" | "completed" | "failed";
  currentPhase?: string | undefined;
  showAnimation?: boolean;
}> = ({ status, currentPhase, showAnimation = true }) => {
  const phases = [
    { key: "queued", label: "Queued", icon: ClockIcon },
    { key: "running", label: "Scanning", icon: PlayIcon },
    { key: "completed", label: "Complete", icon: CheckCircleIcon },
  ];

  const getCurrentPhaseIndex = () => {
    switch (status) {
      case "queued":
        return 0;
      case "running":
        return 1;
      case "completed":
        return 2;
      case "failed":
        return -1; // Special case for failed
      default:
        return 0;
    }
  };

  const currentPhaseIndex = getCurrentPhaseIndex();

  if (status === "failed") {
    return (
      <div className="flex items-center justify-center p-4 bg-error/10 rounded-lg">
        <XCircleIcon className="h-6 w-6 text-error mr-2" />
        <span className="text-error font-medium">Scan Failed</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Phase progress bar */}
      <div className="flex items-center justify-between">
        {phases.map((phase, index) => {
          const Icon = phase.icon;
          const isActive = index === currentPhaseIndex;
          const isCompleted = index < currentPhaseIndex;
          const isUpcoming = index > currentPhaseIndex;

          return (
            <div key={phase.key} className="flex flex-col items-center flex-1">
              {/* Phase icon */}
              <div
                className={`
                  w-8 h-8 rounded-full flex items-center justify-center mb-2 transition-all duration-300
                  ${isActive ? "bg-info text-info-content" : ""}
                  ${isCompleted ? "bg-success text-success-content" : ""}
                  ${isUpcoming ? "bg-base-200 text-base-content/50" : ""}
                `}
              >
                <Icon className={`h-4 w-4 ${isActive && showAnimation && phase.key === "running" ? "animate-spin" : ""}`} />
              </div>

              {/* Phase label */}
              <span
                className={`
                  text-xs font-medium transition-colors duration-300
                  ${isActive ? "text-info" : ""}
                  ${isCompleted ? "text-success" : ""}
                  ${isUpcoming ? "text-base-content/50" : ""}
                `}
              >
                {phase.label}
              </span>

              {/* Connection line */}
              {index < phases.length - 1 && (
                <div className="absolute top-4 left-1/2 w-full h-0.5 -z-10">
                  <div
                    className={`
                      h-full transition-colors duration-300
                      ${index < currentPhaseIndex ? "bg-success" : "bg-base-200"}
                    `}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Current phase description */}
      {currentPhase && <div className="text-center text-sm text-base-content/70">{currentPhase}</div>}
    </div>
  );
};

// Enhanced ScanProgressDisplay with progress estimation
export const ScanProgressDisplay: React.FC<{
  status: "queued" | "running" | "completed" | "failed";
  startTime?: string;
  endTime?: string | undefined;
  targetCount: number;
  targetsScanned?: number | undefined;
  vulnerabilitiesFound?: number | undefined;
  currentPhase?: string;
  showPhaseIndicator?: boolean;
  showMetrics?: boolean;
  showDuration?: boolean;
  templateCount?: number;
}> = ({
  status,
  startTime,
  endTime,
  targetCount,
  targetsScanned,
  vulnerabilitiesFound,
  currentPhase,
  showPhaseIndicator = true,
  showMetrics = true,
  showDuration = true,
  templateCount = 10,
}) => {
  // Use state to track current time for real-time progress updates
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Update current time every second for running scans
  useEffect(() => {
    if (status !== "running" || !startTime) {
      return;
    }

    // Update immediately
    setCurrentTime(Date.now());

    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, [status, startTime]);

  // Calculate progress estimation for running scans - recalculate as time passes
  // biome-ignore lint/correctness/useExhaustiveDependencies: <recalc every sec>
  const progressEstimate = useMemo(() => {
    if (status === "running" && startTime) {
      // Use currentTime to force recalculation every second
      return calculateProgressEstimate(targetCount, templateCount, startTime, status);
    }
    return null;
  }, [status, startTime, targetCount, templateCount, currentTime]);

  const displayPhase = currentPhase || progressEstimate?.currentPhase || getDefaultPhase(status);
  const displayMessage = progressEstimate?.statusMessage || getDefaultMessage(status);

  return (
    <div className="space-y-4">
      {/* Phase Indicator */}
      {showPhaseIndicator && (
        <div className="text-center">
          <div className="text-lg font-semibold mb-2">{displayPhase}</div>
          <div className="text-sm text-base-content/70">{displayMessage}</div>

          {/* Progress Bar for Running Scans */}
          {status === "running" && progressEstimate && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>{Math.round(progressEstimate.completionPercentage)}%</span>
              </div>
              <div className="w-full bg-base-300 rounded-full h-2">
                <div className="bg-primary h-2 rounded-full transition-all duration-1000 ease-out" style={{ width: `${progressEstimate.completionPercentage}%` }} />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Progress Metrics */}
      {showMetrics && <ProgressMetrics targetCount={targetCount} targetsScanned={targetsScanned} vulnerabilitiesFound={vulnerabilitiesFound} status={status} />}

      {/* Duration Display */}
      {showDuration && startTime && (
        <div className="flex justify-center">
          <div className="p-4 bg-base-200/50 rounded-lg">
            <RealTimeDuration startTime={startTime} endTime={endTime} status={status} realTime={status === "running"} />
          </div>
        </div>
      )}
    </div>
  );
};

const getDefaultPhase = (status: string): string => {
  switch (status) {
    case "queued":
      return "Scan Queued";
    case "running":
      return "Scanning in Progress";
    case "completed":
      return "Scan Completed";
    case "failed":
      return "Scan Failed";
    default:
      return "Unknown Status";
  }
};

const getDefaultMessage = (status: string): string => {
  switch (status) {
    case "queued":
      return "Your scan is waiting for available resources";
    case "running":
      return "Analyzing targets for security vulnerabilities";
    case "completed":
      return "All targets have been successfully scanned";
    case "failed":
      return "The scan encountered an error and could not complete";
    default:
      return "";
  }
};
