import { createRootRoute, Outlet } from "@tanstack/react-router";
// import { TanStackRouterDevtools } from "@tanstack/router-devtools";
import { ThemeProvider } from "@/providers/theme-provider";
import "@/index.css";
import { Toaster } from "@/components/ui/sonner";

export const Route = createRootRoute({
  component: () => (
    <ThemeProvider defaultTheme="corporate" storageKey="theme">
      <div className="h-screen min-h-screen bg-background text-foreground">
        <Outlet />
      </div>
      <Toaster />
      {/* <TanStackRouterDevtools position="bottom-right" /> */}
    </ThemeProvider>
  ),
});
