package nuclei

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"main/internal/types"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
)

// resolveTemplatesDir finds a usable templates directory across Lambda and local environments.
func resolveTemplatesDir() string {
	candidates := []string{
		"/opt/templates",
		"/var/task/opt/templates",
		"./opt/templates",
	}
	for _, p := range candidates {
		if stat, err := os.Stat(p); err == nil && stat.IsDir() {
			return p
		}
	}

	return "/opt/templates"
}

// PrepareCommand builds nuclei arguments for Lambda execution using a safe, fixed configuration.
func PrepareCommand(env *types.Environment, request *types.ScanRequest) ([]string, error) {
	log.Printf("Building Nuclei command with hardcoded parameters")

	// Validate targets first
	if len(request.Targets) == 0 {
		return nil, fmt.Errorf("no targets provided")
	}

	// Resolve template paths when templates are provided
	var templatePaths []string
	if len(request.Templates) > 0 {
		templatesDir := resolveTemplatesDir()
		log.Printf("Using templates directory: %s", templatesDir)

		for _, template := range request.Templates {
			templateFile := template
			ext := filepath.Ext(templateFile)
			if ext != ".yaml" && ext != ".yml" {
				templateFile = template + ".yaml"
			}
			templatePath := filepath.Join(templatesDir, templateFile)
			templatePaths = append(templatePaths, templatePath)
		}
	}

	// Build safe base arguments and validate targets/templates
	cmdArgs, err := buildSafeNucleiArgs(request.Targets, templatePaths)
	if err != nil {
		return nil, fmt.Errorf("failed to build safe arguments: %w", err)
	}

	// Targets: single uses -u, multiple are written to file and referenced via -l
	if len(request.Targets) == 1 {
		cmdArgs = append(cmdArgs, "-u", request.Targets[0])
	} else {
		if err := WriteTargetsToFile(request.Targets, env.TargetsFile); err != nil {
			return nil, fmt.Errorf("failed to write targets: %w", err)
		}
		cmdArgs = append(cmdArgs, "-l", env.TargetsFile)
	}

	// Output file: forces nuclei to write NDJSON to a file
	cmdArgs = append(cmdArgs, "-o", env.ScanOutput)

	return cmdArgs, nil
}

// buildSafeNucleiArgs builds safe nuclei arguments using a fixed configuration.
func buildSafeNucleiArgs(targets, templates []string) ([]string, error) {
	log.Printf("Building safe Nuclei arguments with %d targets and %d templates", len(targets), len(templates))

	baseArgs := []string{
		"-jsonl",
		// "-vv",
		"-silent",
		"-nc",
		"-disable-update-check",
		"-duc",
		"-ni",
		// should be based on templates set(same endpoint/protocol will let us cluster)
		"-disable-clustering",
		// "-stats",
		// "-si", "10",

		// "-ss", "host-spray",
		// "-bs", "50",
	}

	args := make([]string, len(baseArgs))
	copy(args, baseArgs)

	// for our synthetic load thats fine, in real scans we should raise a lot
	args = append(args, "-timeout", "1")

	// global req/s limiter
	args = append(args, "-rl", "34")

	// concurrency should be > rl, beyond that it just provides runway for the rate limiter
	args = append(args, "-c", "64")

	// no retries, we want to fail fast
	args = append(args, "-retries", "0")

	// TODO: host-spray + -bs smooths per-host pressure vs. template-spray (default) which can pile on a single host
	// Validate and add templates
	for _, template := range templates {
		args = append(args, "-t", template)
	}

	// Validate targets
	for _, target := range targets {
		if err := validateTarget(target); err != nil {
			return nil, fmt.Errorf("target validation failed for '%s': %w", target, err)
		}
	}

	log.Printf("All %d targets validated successfully", len(targets))
	log.Printf("Nuclei arguments: %v", args)

	return args, nil
}

// runCommand executes nuclei with optional real-time output streaming and returns combined stdout/stderr.
func runCommand(env *types.Environment, args []string, streamOutput bool) (string, error) {
	if _, err := os.Stat(env.NucleiBinary); err != nil {
		return "", fmt.Errorf("nuclei binary not found at %s: %w", env.NucleiBinary, err)
	}

	cmd := exec.Command(env.NucleiBinary, args...)

	if env.IsLambda {
		cmd.Dir = "/tmp"
	}

	var outputStr string
	var err error

	if streamOutput {
		// Real-time streaming mode - shows nuclei stats and progress
		outputStr, err = runCommandWithStreaming(cmd)
	} else {
		// Buffered mode - faster execution, no real-time output
		outputStr, err = runCommandBuffered(cmd)
	}

	if err != nil {
		// Check if it's an exit code error
		if exitError, ok := err.(*exec.ExitError); ok {
			log.Printf("Nuclei exited with code %d", exitError.ExitCode())
			if strings.Contains(outputStr, "Error") || strings.Contains(outputStr, "FATAL") || strings.Contains(outputStr, "panic") {
				return outputStr, fmt.Errorf("nuclei execution failed with exit code %d: %w", exitError.ExitCode(), err)
			}
			log.Printf("Nuclei returned non-zero exit code but output seems normal, treating as success")

			return outputStr, nil
		}

		return outputStr, fmt.Errorf("nuclei execution failed: %w", err)
	}

	log.Printf("Nuclei execution completed successfully")

	return outputStr, nil
}

// runCommandWithStreaming executes the command with real-time output streaming
func runCommandWithStreaming(cmd *exec.Cmd) (string, error) {
	// Set up pipes for stdout and stderr
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return "", fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return "", fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start the command
	if err := cmd.Start(); err != nil {
		return "", fmt.Errorf("failed to start nuclei command: %w", err)
	}

	// Use goroutines to read from both pipes concurrently
	var wg sync.WaitGroup
	var combinedOutput strings.Builder
	var mu sync.Mutex

	// Function to read from a pipe and log output in real-time
	readPipe := func(pipe io.ReadCloser, prefix string) {
		defer wg.Done()
		scanner := bufio.NewScanner(pipe)
		for scanner.Scan() {
			line := scanner.Text()

			// Log the line in real-time with prefix to distinguish stdout/stderr
			log.Printf("[nuclei-%s] %s", prefix, line)

			// Also collect it for the return value
			mu.Lock()
			combinedOutput.WriteString(line)
			combinedOutput.WriteString("\n")
			mu.Unlock()
		}
		if err := scanner.Err(); err != nil {
			log.Printf("Error reading from %s: %v", prefix, err)
		}
	}

	// Start goroutines to read from both pipes
	wg.Add(2)
	go readPipe(stdout, "stdout")
	go readPipe(stderr, "stderr")

	// Wait for all output to be read
	wg.Wait()

	// Wait for the command to complete
	err = cmd.Wait()

	return combinedOutput.String(), err
}

// runCommandBuffered executes the command using buffered output for better performance
func runCommandBuffered(cmd *exec.Cmd) (string, error) {
	// Capture both stdout and stderr - faster but no real-time output
	output, err := cmd.CombinedOutput()

	return string(output), err
}

// ExecuteWithValidation runs nuclei and validates JSON output when requested.
func ExecuteWithValidation(env *types.Environment, cmdArgs []string, outputMode string, streamOutput bool) (*types.NucleiResult, error) {
	if outputMode == types.OutputModeJSON {
		os.Remove(env.ScanOutput)
		if err := TestWritePermissions(env); err != nil {
			return &types.NucleiResult{Error: err}, err
		}
	}

	output, err := runCommand(env, cmdArgs, streamOutput)
	if err != nil {
		return &types.NucleiResult{
			Output: output,
			Error:  fmt.Errorf("nuclei execution failed: %w", err),
		}, err
	}

	hasResults := DetectResults(output)

	if err := ValidateWithDelay(env, outputMode, hasResults); err != nil {
		return &types.NucleiResult{
			Output:     output,
			HasResults: hasResults,
			OutputFile: env.ScanOutput,
			Error:      err,
		}, err
	}

	return &types.NucleiResult{
		Output:     output,
		HasResults: hasResults,
		OutputFile: env.ScanOutput,
	}, nil
}

// WriteTargetsToFile writes targets to a file, one per line.
func WriteTargetsToFile(targets []string, filename string) error {
	log.Printf("Writing %d targets to file: %s", len(targets), filename)

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create targets file %s: %w", filename, err)
	}
	defer file.Close()

	w := bufio.NewWriter(file)
	targetsWritten := 0
	for i, target := range targets {
		if target == "" {
			log.Printf("WARNING: Skipping empty target at index %d", i)

			continue
		}
		if _, err := fmt.Fprintln(w, target); err != nil {
			return fmt.Errorf("failed to write target '%s' to file %s: %w", target, filename, err)
		}
		targetsWritten++
	}

	if err := w.Flush(); err != nil {
		return fmt.Errorf("failed to flush targets to file %s: %w", filename, err)
	}

	if stat, err := os.Stat(filename); err != nil {
		return fmt.Errorf("failed to stat targets file %s: %w", filename, err)
	} else {
		log.Printf("Targets file created successfully: %s (%d bytes, %d targets written)", filename, stat.Size(), targetsWritten)
	}

	if content, err := os.ReadFile(filename); err != nil {
		log.Printf("WARNING: Could not read back targets file for validation: %v", err)
	} else {
		lines := strings.Split(strings.TrimSpace(string(content)), "\n")
		log.Printf("Targets file validation: %d lines read back", len(lines))
		if len(lines) != targetsWritten {
			log.Printf("WARNING: Targets file line count mismatch - wrote %d, read %d", targetsWritten, len(lines))
		}
	}

	return nil
}

// ReadTargetsFromFile reads targets from a file, one per line.
func ReadTargetsFromFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var targets []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		target := strings.TrimSpace(scanner.Text())
		if target != "" && !strings.HasPrefix(target, "#") {
			targets = append(targets, target)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return targets, nil
}

// validateTarget validates a target URL to prevent injection attacks.
func validateTarget(target string) error {
	if target == "" {
		return fmt.Errorf("target cannot be empty")
	}

	dangerousPatterns := []string{
		"&&", "||", ";", "|", ">", "<", "$", "`", "$(", "${",
		"../", "./", "~", "*", "?", "[", "]", "{", "}",
		"\n", "\r", "\t", "\\",
	}

	for _, pattern := range dangerousPatterns {
		if strings.Contains(target, pattern) {
			return fmt.Errorf("target contains dangerous pattern: %s", pattern)
		}
	}

	if !strings.HasPrefix(target, "http://") && !strings.HasPrefix(target, "https://") {
		return fmt.Errorf("target must start with http:// or https://")
	}

	return nil
}
