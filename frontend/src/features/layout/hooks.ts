// Simple atom for drawer state
import { atom, useAtom } from "jotai";
import { useCallback } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

const drawerOpenAtom = atom(false);

export const useSidebarState = () => {
  const isMobile = useIsMobile();
  const [drawerOpen, setDrawerOpen] = useAtom(drawerOpenAtom);

  const toggleDrawer = useCallback(() => {
    setDrawerOpen((prev) => !prev);
  }, [setDrawerOpen]);

  return {
    open: drawerOpen,
    setOpen: setDrawerOpen,
    toggleDrawer,
    isMobile,
  };
};
