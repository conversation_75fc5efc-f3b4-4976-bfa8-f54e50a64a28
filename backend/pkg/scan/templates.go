package scan

import (
	"encoding/json"
	"fmt"
	"log"
	"nuclear_pond/pkg/aws"
	"nuclear_pond/pkg/types"
	"os"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
)

// getTemplateMetadata downloads and parses templates.json from the configured bucket.
func getTemplateMetadata() (*types.TemplateIndex, error) {
	awsConfig := aws.NewAWSConfig()
	sess, err := awsConfig.CreateSession()
	if err != nil {
		return nil, fmt.Errorf("aws session: %w", err)
	}

	bucket := os.Getenv("NUCLEI_TEMPLATES_BUCKET")
	if bucket == "" {
		return nil, fmt.Errorf("NUCLEI_TEMPLATES_BUCKET environment variable not set")
	}

	s3c := s3.New(sess)
	out, err := s3c.GetObject(&s3.GetObjectInput{Bucket: awssdk.String(bucket), Key: awssdk.String("templates.json")})
	if err != nil {
		return nil, fmt.Errorf("download templates.json: %w", err)
	}
	defer out.Body.Close()

	var idx types.TemplateIndex
	if err := json.NewDecoder(out.Body).Decode(&idx); err != nil {
		return nil, fmt.Errorf("parse templates.json: %w", err)
	}
	if idx.Templates == nil || idx.Summary.Total == 0 {
		return nil, fmt.Errorf("templates.json is invalid or empty")
	}
	log.Printf("Loaded templates metadata %s (%d templates)", idx.Version, idx.Summary.Total)

	return &idx, nil
}
