import { useRouter } from "@tanstack/react-router";
import { useAtom } from "jotai";
import { useEffect } from "react";
import { authAtom } from "@/features/auth/state";
import { authService } from "@/lib/auth";

/**
 * Global authentication guard hook that:
 * 1. Validates JWT token on mount and periodically
 * 2. Handles token expiration by redirecting to login
 * 3. Syncs auth state with JWT validity
 */
export const useAuthGuard = () => {
  const [auth, setAuth] = useAtom(authAtom);
  const router = useRouter();

  useEffect(() => {
    const validateAuth = async () => {
      // Skip validation if already loading or not authenticated
      if (auth.isLoading || !auth.isAuthenticated) return;

      // Check if JWT token is still valid
      if (!authService.isAuthenticated()) {
        console.warn("JWT token expired or invalid - attempting refresh");

        // Try to refresh the token first
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log("Token refreshed successfully");
          return; // Continue with current session
        }

        console.warn("Token refresh failed - logging out");

        // Clear auth state
        setAuth({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: "Session expired",
        });

        // Redirect to login if not already there
        if (window.location.pathname !== "/login") {
          router.navigate({ to: "/login" });
        }
      }
    };

    // Validate immediately
    validateAuth();

    // Set up periodic validation (every 5 minutes)
    const interval = setInterval(validateAuth, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [auth.isAuthenticated, auth.isLoading, setAuth, router]);

  // Also validate on window focus (user might have been away for a while)
  useEffect(() => {
    const handleFocus = async () => {
      if (auth.isAuthenticated && !authService.isAuthenticated()) {
        console.warn("JWT token expired while away - attempting refresh");

        // Try to refresh the token first
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log("Token refreshed successfully after focus");
          return;
        }

        console.warn("Token refresh failed after focus - logging out");

        setAuth({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: "Session expired",
        });

        if (window.location.pathname !== "/login") {
          router.navigate({ to: "/login" });
        }
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [auth.isAuthenticated, setAuth, router]);
};
