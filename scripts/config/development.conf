# Configuration for the local development environment

# --- AWS / LocalStack ---
export LOCALSTACK_ENDPOINT="http://localhost:4566"
export AWS_REGION="us-east-1"

# --- Resource Names ---
readonly PROJECT_NAME="fastscan"
readonly ENVIRONMENT="local"
export S3_BUCKET="${PROJECT_NAME}-nuclei-artifacts-${ENVIRONMENT}-${AWS_REGION}"
export DYNAMODB_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-server-state"
export LAMBDA_FUNCTION_NAME="${PROJECT_NAME}-${ENVIRONMENT}-nuclei-function"
export LAMBDA_ROLE_NAME="${PROJECT_NAME}-nuclei-lambda-role"