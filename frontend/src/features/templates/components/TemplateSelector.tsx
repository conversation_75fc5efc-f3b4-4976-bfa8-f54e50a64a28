import React from "react";
import { useTemplateSelection } from "../hooks";
import type { TemplateMetadata, TemplateSeverity } from "../types";

const severityColors: Record<TemplateSeverity, string> = {
  critical: "badge-error",
  high: "badge-warning",
  medium: "badge-info",
  low: "badge-success",
  info: "badge-neutral",
};

interface TemplateSelectorProps {
  onSelectionChange: (selectedTemplates: string[]) => void;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({ onSelectionChange }) => {
  const { filteredTemplates, selectedTemplates, toggleTemplateSelection, clearSelection, selectAllFiltered, isLoading } = useTemplateSelection();

  React.useEffect(() => {
    onSelectionChange(selectedTemplates);
  }, [selectedTemplates, onSelectionChange]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading templates...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Available Templates ({selectedTemplates.length})</h3>
        <div className="flex space-x-2">
          <button type="button" className="btn btn-sm btn-primary" onClick={selectAllFiltered}>
            Select All
          </button>
          <button type="button" className="btn btn-sm btn-ghost" onClick={clearSelection}>
            Clear
          </button>
        </div>
      </div>

      <div className="max-h-96 overflow-y-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th></th>
              <th>Template</th>
              <th>Severity</th>
            </tr>
          </thead>
          <tbody>
            {filteredTemplates.map((template: TemplateMetadata) => (
              <tr key={template.id} className="hover">
                <td>
                  <input type="checkbox" className="checkbox" checked={selectedTemplates.includes(template.id)} onChange={() => toggleTemplateSelection(template.id)} />
                </td>
                <td>
                  <div className="font-bold">{template.name}</div>
                  <div className="text-sm opacity-50">{template.id}</div>
                </td>
                <td>
                  <span className={`badge ${severityColors[template.severity as TemplateSeverity]}`}>{template.severity}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
