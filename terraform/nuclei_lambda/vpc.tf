# ==============================================================================
# VPC CONFIGURATION FOR LAMBDA SECURITY
# ==============================================================================

# Security group for Lambda function
resource "aws_security_group" "lambda_sg" {
  count       = var.enable_vpc_isolation ? 1 : 0
  name        = "${var.project_name}-nuclei-lambda-sg"
  description = "Security group for Nuclei Lambda function"
  vpc_id      = var.vpc_id

  # Outbound rules for Lambda to access AWS services
  egress {
    description = "HTTPS to AWS services"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "HTTP for external scanning (if needed)"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "Custom ports for security scanning"
    from_port   = 1
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = var.allowed_scan_cidrs
  }

  tags = merge(var.tags, {
    Name      = "${var.project_name}-nuclei-lambda-sg"
    Component = "nuclei-lambda"
    Type      = "security"
  })
}

# VPC endpoint for S3 (if using VPC)
resource "aws_vpc_endpoint" "s3" {
  count           = var.enable_vpc_isolation ? 1 : 0
  vpc_id          = var.vpc_id
  service_name    = "com.amazonaws.${data.aws_region.current.name}.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids = var.route_table_ids

  tags = merge(var.tags, {
    Name      = "${var.project_name}-s3-endpoint"
    Component = "nuclei-lambda"
    Type      = "networking"
  })
}

# VPC endpoint for Lambda (for internal invocations)
resource "aws_vpc_endpoint" "lambda" {
  count             = var.enable_vpc_isolation ? 1 : 0
  vpc_id            = var.vpc_id
  service_name      = "com.amazonaws.${data.aws_region.current.name}.lambda"
  vpc_endpoint_type = "Interface"
  subnet_ids        = [var.private_subnet_ids[0]]
  security_group_ids = [aws_security_group.lambda_sg[0].id]

  tags = merge(var.tags, {
    Name      = "${var.project_name}-lambda-endpoint"
    Component = "nuclei-lambda"
    Type      = "networking"
  })
}