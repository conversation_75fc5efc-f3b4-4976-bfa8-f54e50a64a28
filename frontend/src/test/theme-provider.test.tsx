import { fireEvent, render, screen } from "@testing-library/react";
import { ThemeProvider, useTheme } from "@/providers/theme-provider";

// Test component to interact with theme provider
const TestComponent = () => {
  const { theme, setTheme } = useTheme();

  return (
    <div>
      <span data-testid="current-theme">{theme}</span>
      <button onClick={() => setTheme("corporate")} data-testid="set-corporate">
        Set Corporate
      </button>
      <button onClick={() => setTheme("dark")} data-testid="set-dark">
        Set Dark
      </button>
    </div>
  );
};

describe("ThemeProvider", () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it("should default to corporate theme", () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    expect(screen.getByTestId("current-theme")).toHaveTextContent("corporate");
  });

  it("should allow switching between corporate and dark themes", () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    // Initially corporate
    expect(screen.getByTestId("current-theme")).toHaveTextContent("corporate");

    // Switch to dark
    fireEvent.click(screen.getByTestId("set-dark"));
    expect(screen.getByTestId("current-theme")).toHaveTextContent("dark");

    // Switch back to corporate
    fireEvent.click(screen.getByTestId("set-corporate"));
    expect(screen.getByTestId("current-theme")).toHaveTextContent("corporate");
  });

  it("should persist theme in localStorage", () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    fireEvent.click(screen.getByTestId("set-dark"));
    expect(localStorage.getItem("theme")).toBe("dark");
  });

  it("should handle invalid theme values by falling back to corporate", () => {
    // Set invalid theme in localStorage
    localStorage.setItem("theme", "invalid-theme");

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    expect(screen.getByTestId("current-theme")).toHaveTextContent("corporate");
  });

  it("should handle legacy theme values by falling back to corporate", () => {
    // Set legacy theme in localStorage
    localStorage.setItem("theme", "business");

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    expect(screen.getByTestId("current-theme")).toHaveTextContent("corporate");
  });
});
