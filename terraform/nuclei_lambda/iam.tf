# ==============================================================================
# IAM CONFIGURATION FOR NUCLEI LAMBDA
# ==============================================================================

# ==============================================================================
# LAMBDA EXECUTION ROLE
# ==============================================================================

# IAM role for Lambda function execution
resource "aws_iam_role" "lambda_role" {
  name = "${var.project_name}-nuclei-lambda-role"

  assume_role_policy = data.aws_iam_policy_document.lambda_trust_policy.json

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-lambda-role"
    Component   = "nuclei-lambda"
    Type        = "security"
  })
}

# Trust policy for Lambda service
data "aws_iam_policy_document" "lambda_trust_policy" {
  statement {
    effect = "Allow"
    
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
    
    actions = ["sts:AssumeRole"]
  }
}

# ==============================================================================
# LAMBDA EXECUTION POLICY
# ==============================================================================

# Custom IAM policy for Lambda function
resource "aws_iam_policy" "lambda_policy" {
  name        = "${var.project_name}-nuclei-lambda-policy"
  description = "Minimal IAM policy for Nuclei Lambda function - CloudWatch Logs and S3 access only"

  policy = data.aws_iam_policy_document.lambda_policy_document.json

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-lambda-policy"
    Component   = "nuclei-lambda"
    Type        = "security"
  })
}

# Policy document with minimal required permissions (CloudWatch Logs + S3 only)
data "aws_iam_policy_document" "lambda_policy_document" {
  # CloudWatch Logs permissions
  statement {
    sid    = "AllowCloudWatchLogs"
    effect = "Allow"
    
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    
    resources = [
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.project_name}-nuclei-function*"
    ]
  }

  # S3 permissions for uploading scan findings
  statement {
    sid    = "AllowS3FindingsUpload"
    effect = "Allow"
    
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl"
    ]
    
    resources = [
      "${aws_s3_bucket.artifacts_bucket.arn}/findings/*"
    ]
  }

  # S3 permissions for reading artifacts (binary,and individual templates)
  statement {
    sid    = "AllowS3ArtifactsRead"
    effect = "Allow"
    
    actions = [
      "s3:GetObject"
    ]
    
    resources = [
      "${aws_s3_bucket.artifacts_bucket.arn}/nuclei-layer.zip",
      "${aws_s3_bucket.artifacts_bucket.arn}/templates-layer.zip",
      "${aws_s3_bucket.artifacts_bucket.arn}/templates/*"
    ]
  }

  # S3 permissions for listing bucket contents (findings and templates)
  statement {
    sid    = "AllowS3BucketList"
    effect = "Allow"
    
    actions = [
      "s3:ListBucket"
    ]
    
    resources = [
      aws_s3_bucket.artifacts_bucket.arn
    ]
    
    condition {
      test     = "StringLike"
      variable = "s3:prefix"
      values   = ["findings/*", "templates/*"]
    }
  }


}

# Attach the custom policy to the Lambda role
resource "aws_iam_role_policy_attachment" "lambda_policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

# ==============================================================================
# AWS MANAGED POLICIES (OPTIONAL)
# ==============================================================================

# Attach AWS managed policy for basic Lambda execution (optional, for additional logging)
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  count      = var.enable_lambda_insights ? 1 : 0
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Attach AWS managed policy for Lambda Insights (if enabled)
resource "aws_iam_role_policy_attachment" "lambda_insights" {
  count      = var.enable_lambda_insights ? 1 : 0
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchLambdaInsightsExecutionRolePolicy"
}

# ==============================================================================
# LAMBDA RESOURCE-BASED POLICY (SECURITY ENHANCEMENT)
# ==============================================================================

# Restrict Lambda invocation to Nuclear Pond backend role only
resource "aws_lambda_permission" "allow_nuclear_pond_invoke" {
  count         = var.nuclear_pond_task_role_arn != "" ? 1 : 0
  statement_id  = "AllowNuclearPondInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.nuclei_function.function_name
  principal     = data.aws_caller_identity.current.account_id
  source_arn    = var.nuclear_pond_task_role_arn
}



