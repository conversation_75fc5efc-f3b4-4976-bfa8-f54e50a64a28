# Frontend Architecture

This document explains the architecture of the frontend deployment for the Nuclei scanning application, focusing on the AWS components and their roles in both development and production scenarios. For detailed deployment steps, see the [Frontend Deployment Guide](./frontend-deployment.md).


### 🛠️ Development Mode (localhost:5173)
See [devbox.md](../../devbox_setup.md) and devbox commands.


### 🚀 Production Mode (CloudFront + S3)
```
[User] → [CloudFront CDN] → [S3 Origin] → [React App]
         [Route53 DNS] ↗ 
```
This mode leverages CloudFront for global content delivery, HTTPS, and enhanced performance, with S3 as the origin. Custom domains are managed via Route53.

### Frontend Application Build
The React application is built into static assets (`dist/` directory). Key aspects of the build process include:
1. Environment-specific configuration injection (e.g., API URLs via `.env.{environment}` files).
2. Asset optimization and bundling using Vite.
3. Generation of static HTML, CSS, and JavaScript files.
These static files are then deployed to an S3 bucket.

## AWS Infrastructure Components

The core infrastructure is managed by Terraform.

#### 1. S3 Bucket (`aws_s3_bucket.frontend`)
This bucket is present in both development and production modes.
- **Purpose**:
    - Stores all static frontend assets (HTML, CSS, JS, images).
    - Serves as the origin for CloudFront in production mode.
    - Provides direct HTTP static website hosting in development mode.
- **Key Features**:
    - Static website hosting enabled.
    - Versioning enabled for rollbacks and asset history.
    - Public read access policy for serving website content.
    - CORS configuration to allow API calls to the backend.
- **Security**:
    - Bucket policy restricts write access and prevents unauthorized modifications.
    - While files are publicly readable for website serving, bucket-level public access blocks can be configured for more granular control.

#### 2. CloudFront Distribution (`aws_cloudfront_distribution.frontend`)
This component is conditionally created, primarily for production mode.
- **Purpose**:
    - Provides global content delivery (CDN) for low-latency access worldwide.
    - Handles HTTPS termination using an ACM SSL certificate.
    - Implements caching strategies to improve performance and reduce S3 load.
    - Offers DDoS protection (AWS Shield Standard).
- **Key Features**:
    - Origin configured to the S3 frontend bucket.
    - Origin Access Control (OAC) is used to ensure S3 bucket content is only accessible via CloudFront, not directly (in production).
    - Cache behaviors optimized for Single Page Applications (SPAs), e.g., routing all paths to `index.html`.
    - Custom error responses can be configured to serve `index.html` for 403/404 errors, supporting client-side routing.
- **Cache Strategy Example**:
    - `index.html`: Typically configured with a short TTL or no-cache to ensure users get the latest version.
    - Static assets (`/assets/*`): Long cache TTL (e.g., 1 year) leveraging cache-busting techniques (hashed filenames).

#### 3. Route53 DNS Record (`aws_route53_record.frontend`)
This is conditionally created when a custom domain is used with the production setup.
- **Purpose**: Maps a user-friendly custom domain (e.g., `app.yourdomain.com`) to the CloudFront distribution.
- **Key Features**: Usually an ALIAS record pointing to the CloudFront distribution for optimal performance and integration.

#### 4. ACM SSL Certificate (`aws_acm_certificate.frontend`)
This is conditionally created when a custom domain and CloudFront are used.
- **Purpose**: Provides the SSL/TLS certificate to enable HTTPS for the custom domain via CloudFront.
- **Key Features**:
    - Free certificates provided by AWS Certificate Manager.
    - Must be created in the `us-east-1` region for use with global CloudFront distributions.
    - Supports automatic renewal managed by AWS.
    - Domain validation is typically done via DNS records in Route53.

## 🔄 Data Flow & Request Lifecycle

### Development Mode Flow
1. Developer initiates a build (e.g., `yarn build:dev`).
2. Static assets are generated in the `dist/` directory.
3. Assets are uploaded directly to the S3 bucket.
4. Users access the application via the S3 static website endpoint (HTTP).
5. The React app, running in the user's browser, makes API calls directly to the configured backend API endpoint (e.g., an ALB).

### Production Mode Flow
1. Developer initiates a build (e.g., `yarn build:prod`).
2. Static assets are generated.
3. Assets are uploaded to the S3 bucket.
4. User requests `https://app.yourdomain.com`.
5. Route53 resolves the domain to the CloudFront distribution.
6. CloudFront checks its cache. If content is cached and fresh, it serves it directly.
7. If not cached or stale, CloudFront requests the object from the S3 origin (via OAC).
8. S3 provides the object to CloudFront.
9. CloudFront caches the object (based on cache-control headers and distribution settings) and serves it to the user via HTTPS.
10. The React app, running in the user's browser, makes API calls to the backend API (which might also be behind CloudFront or an ALB).
11. **Cache Invalidation**: If new code is deployed, a CloudFront cache invalidation (e.g., for `/*`) is typically created to force CloudFront to fetch the new files from S3.

## ⚙️ Configuration Management

Application-level configuration (like API endpoints) is injected into the React app at build time using environment files (e.g., `.env.dev`, `.env.prod`) which populate `VITE_*` environment variables. Example:
```bash
# .env.dev
VITE_API_URL=http://backend-dev-alb.example.com
VITE_ENVIRONMENT=dev

# .env.prod
VITE_API_URL=https://api.yourdomain.com
VITE_ENVIRONMENT=prod
```
The [Frontend Deployment Guide](./frontend-deployment.md) provides details on how these `.env` files are managed or generated, potentially with Terraform assistance.

## 🔧 Deployment Automation Insights

The frontend deployment process is largely automated. While the specific commands and step-by-step instructions are detailed in the [Frontend Deployment Guide](./frontend-deployment.md), the general automation flow involves:

1.  **Infrastructure Provisioning**: Terraform applies the configuration defined in `.tf` files (using variables from `terraform.tfvars`) to create or update the necessary AWS resources (e.g., `aws_s3_bucket`, `aws_cloudfront_distribution`).
2.  **Deployment Script Generation**: The Terraform module often generates environment-specific shell scripts (e.g., `deploy-frontend-dev.sh`, `deploy-frontend-prod.sh`) located in the `terraform/frontend/` directory.
3.  **Code Build and Sync**: These generated scripts typically orchestrate:
    *   Setting up the Node.js environment and installing dependencies (`yarn install`).
    *   Running the Vite build command for the target environment (e.g., `yarn build:dev` or `yarn build:prod`), which bundles the frontend and injects environment variables from the relevant `.env` file. This produces static assets in the `frontend/dist/` directory.
    *   Synchronizing the built assets from the `dist/` directory to the designated S3 bucket using `aws s3 sync`.
    *   If CloudFront is enabled for the environment, creating a cache invalidation to ensure users fetch the latest version of the application.
4.  **Environment-Specific Information**: Terraform may also generate a `DEPLOYMENT-{ENV}.md` file containing output details specific to the deployed environment (like S3 bucket names, CloudFront IDs, etc.), which is referenced by the deployment scripts.

For the complete, actionable steps to deploy the frontend, please consult the [Frontend Deployment Guide](./frontend-deployment.md).

## 🔒 Security Architecture Considerations

### S3 Bucket Security
- **Principle of Least Privilege**: Bucket policies are designed to grant only necessary permissions (e.g., public read for website files, restricted write for deployment).
- **Origin Access Control (OAC)**: For production (CloudFront) setups, OAC is used to ensure that the S3 bucket contents are only accessible via the CloudFront distribution, not directly via the S3 public URL. This prevents bypassing CloudFront.
- **Versioning**: Enabled on the S3 bucket to allow rollbacks in case of bad deployments.
- **Access Logging**: S3 server access logging can be enabled for auditing.

### CloudFront Security
- **HTTPS Enforcement**: Configured to redirect HTTP to HTTPS or use HTTPS only. TLS 1.2+ is typically enforced.
- **Web Application Firewall (WAF)**: AWS WAF can be integrated with CloudFront for protection against common web exploits (e.g., SQL injection, XSS). (Note: This is an additional configurable service).
- **Security Headers**: Custom headers like HTTP Strict Transport Security (HSTS), X-Frame-Options, X-Content-Type-Options, Content-Security-Policy (CSP) can be added via CloudFront Functions or Lambda@Edge, or directly by the application if served from S3.
- **AWS Shield Standard**: Provides DDoS protection automatically at no additional cost. AWS Shield Advanced is available for enhanced protection.

### Application Security
- **API Communication**: Ensure backend API (e.g., Nuclear Pond API) is secured (e.g., using API keys, authentication tokens). The frontend will make requests to this API.
- **CORS (Cross-Origin Resource Sharing)**: S3 buckets and backend APIs must be configured with appropriate CORS headers to allow the frontend (served from its domain) to make requests to the API (potentially on a different domain).
- **Input Validation**: Standard practice for any user inputs handled by the React application.

## 💰 Cost Architecture Considerations

The choice of deployment mode (Development vs. Production) significantly impacts costs. This section outlines the architectural factors influencing cost. For detailed cost breakdowns and monitoring advice related to specific services during deployment, refer to the [Frontend Deployment Guide](./frontend-deployment.md#cost-architecture).

### Development Mode (S3 Only)
- Primarily S3 storage and request costs. Typically very low (< $1-5/month depending on size and traffic).

### Production Mode (CloudFront + S3)
- S3 costs remain.
- CloudFront costs are based on data transfer out, number of HTTP/S requests, and potentially features like Lambda@Edge invocations. Price class selection for CloudFront (e.g., `PriceClass_100`, `PriceClass_200`, `PriceClass_All`) affects cost and reach.
- Route53 costs for hosted zones and queries if a custom domain is used.
- ACM SSL certificates are free.
- Costs are generally higher than dev mode but provide significant performance and security benefits.

## 🔄 Scaling and Performance

### Horizontal Scaling
- **CloudFront**: Natively designed for global scale and high availability. Caches content at edge locations closer to users.
- **S3**: Offers virtually unlimited scalability for storage and request throughput.
- **Route53**: A globally distributed and highly available DNS service.

### Performance Optimization Techniques
- **Caching**: Effective use of browser caching (via `Cache-Control` headers) and CloudFront caching.
    - Long-lived caches for versioned assets (e.g., `main.[hash].js`).
    - Shorter caches or no-cache for `index.html` to ensure users get updates quickly.
- **Content Compression**: Enable Gzip or Brotli compression at CloudFront and/or S3 (if S3 serves directly and files are pre-compressed).
- **Asset Minification**: Handled by the Vite build process (JS, CSS, HTML).
- **Code Splitting**: Implemented in React/Vite to load only necessary code chunks.
- **Image Optimization**: Use optimized image formats (e.g., WebP) and compression. Consider responsive images.
- **CloudFront Functions / Lambda@Edge**: Can be used for advanced request/response manipulation at the edge (e.g., A/B testing, advanced header manipulation, SEO optimizations), but add to complexity and cost.

### Rollback Strategy
- **S3 Versioning**: Allows restoring previous versions of individual files in the S3 bucket.
- **Code Repository**: Revert to a previous commit in Git and redeploy.
- **CloudFront Cache Invalidation**: After rolling back S3 content, invalidate CloudFront cache to force it to fetch the restored version. If a full rollback of a CloudFront distribution configuration is needed (rare), it would involve Terraform state/configuration changes.


## 🚨 Common Gotchas

1.  **Custom domains require CloudFront**: You cannot use custom domains with the S3-only (development) setup directly for HTTPS and apex domain support.
2.  **Route53 Zone ID**: Ensure you have the correct Route53 Hosted Zone ID if you are configuring a custom domain.
3.  **Environment Naming Consistency**: The `frontend_environment` variable impacts build processes and potentially resource naming. Ensure it's consistent with your `.env` files (e.g., `.env.dev`, `.env.prod`).
4.  **API URL Configuration**: Double-check that the `VITE_API_URL` (or equivalent) in your frontend's `.env` files correctly points to your backend API, accessible from where the frontend is served.
5.  **CloudFront Cache Invalidation**: For production deployments, remember that changes might not appear immediately due to CloudFront caching. Ensure invalidations are created (usually handled by deployment scripts).
6.  **ACM Certificate Region**: SSL Certificates for CloudFront *must* be created in `us-east-1` region. The Terraform module handles this with a provider alias.
