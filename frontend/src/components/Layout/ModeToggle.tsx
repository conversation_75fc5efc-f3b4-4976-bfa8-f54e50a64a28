import { Moon, Sun } from "lucide-react";
import { motion } from "motion/react";
import { useTheme } from "@/providers/theme-provider";

const ModeToggle = () => {
  const { theme, setTheme } = useTheme();
  const isDark = theme === "dark";

  const toggleTheme = () => {
    setTheme(isDark ? "corporate" : "dark");
  };

  return (
    <motion.button
      type="button"
      onClick={toggleTheme}
      className="relative p-2 rounded-lg bg-base-200 hover:bg-base-300 transition-colors cursor-pointer"
      title={`Switch to ${isDark ? "light" : "dark"} mode`}
      whileTap={{ scale: 0.95 }}
    >
      <motion.div className="relative w-6 h-6 flex items-center justify-center" animate={{ rotate: isDark ? 380 : 0 }} transition={{ duration: 0.3, ease: "easeInOut" }}>
        <motion.div
          className="absolute"
          initial={false}
          animate={{
            opacity: isDark ? 0 : 1,
            scale: isDark ? 0.5 : 1,
          }}
          transition={{ duration: 0.2 }}
        >
          <Sun className="h-4 w-4 text-yellow-500" />
        </motion.div>
        <motion.div
          className="absolute"
          initial={false}
          animate={{
            opacity: isDark ? 1 : 0,
            scale: isDark ? 1 : 0.5,
          }}
          transition={{ duration: 0.2 }}
        >
          <Moon className="h-4 w-4 text-blue-400" />
        </motion.div>
      </motion.div>
    </motion.button>
  );
};

export default ModeToggle;
