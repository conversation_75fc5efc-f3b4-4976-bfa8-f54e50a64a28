package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/pow"
	"nuclear_pond/pkg/types"

	"github.com/go-chi/render"
	"github.com/google/uuid"
)

// PowControlHandler handles the PoW control endpoint.
func PowControlHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("PoW control request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	var req types.PowControlRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		log.Printf("Error decoding PoW control JSON from %s: %v", r.RemoteAddr, err)
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, types.PowControlResponse{
			Error:   "Bad Request",
			Message: "Error decoding JSON: " + err.Error(),
		})

		return
	}

	// Validate action
	if req.Action != "start" && req.Action != "stop" {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, types.PowControlResponse{
			Error:   "Bad Request",
			Message: "Action must be 'start' or 'stop'",
		})

		return
	}

	log.Printf("Received PoW control request: action=%s", req.Action)

	// Generate request ID for tracking
	requestID := uuid.New().String()

	// Trigger GitHub Actions workflow
	if err := pow.TriggerGitHubWorkflow(req.Action, requestID); err != nil {
		log.Printf("Error triggering GitHub workflow: %v", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, types.PowControlResponse{
			Error:   "Internal Server Error",
			Message: "Failed to trigger infrastructure deployment: " + err.Error(),
		})

		return
	}

	// Determine message based on action
	var message string
	switch req.Action {
	case "start":
		message = "PoW infrastructure deployment initiated. This will take approximately 3-5 minutes."
	case "stop":
		message = "PoW infrastructure destruction initiated. This will take approximately 2-3 minutes."
	}

	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, types.PowControlResponse{
		RequestID: requestID,
		Status:    "accepted",
		Action:    req.Action,
		Message:   message,
	})
}
