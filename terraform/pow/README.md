# Proof of Work (PoW) Target Infrastructure Module

This module deploys AWS infrastructure for testing Nuclei scans with Nuclear Pond. It provides real HTTP endpoints that can be scanned for demonstration and validation purposes.

## Infrastructure Components

The module creates:

1. **Route53 DNS Configuration**
   - Uses your existing Route53 hosted zone for the domain
   - Creates wildcard DNS record pointing to the ALB
   - Creates root domain record pointing to the ALB

2. **Application Load Balancer (ALB)**
   - Public-facing load balancer
   - Security group allowing HTTP traffic
   - Target group for EC2 instances

3. **EC2 Instances**
   - Two t3.micro EC2 instances running Amazon Linux 2
   - Nginx web server serving a simple HTTP 200 response
   - Security group allowing traffic only from the ALB

## Usage

To use this module, include it in your main Terraform configuration:

```hcl
module "pow_targets" {
  source = "./pow"

  enable_pow_targets = true
  project_name       = var.project_name
  tags               = var.tags
  
  # Domain Configuration - use your Route53-registered domain
  pow_domain_name   = "fast-scan-demo-target.click"
  
  # Optional Parameters
  pow_instance_type = "t3.micro"
  pow_key_name      = null  # Set to your key pair name if SSH access is needed
}
```

## Domain Requirements

This module is designed to work with a domain registered through AWS Route53. When you register a domain through Route53, AWS automatically:

1. Creates a hosted zone for your domain
2. Configures the name servers for your domain

The module will use this existing hosted zone to create the necessary DNS records for the PoW targets.

## Example Target URLs for Nuclei Scanning

Once the infrastructure is deployed, you can use any of these URLs as targets for Nuclei scans:

- http://fast-scan-demo-target.click
- http://test1.fast-scan-demo-target.click
- http://test2.fast-scan-demo-target.click
- http://random-subdomain.fast-scan-demo-target.click

## Enabling/Disabling

You can easily enable or disable this module by setting the `enable_pow_targets` variable to `true` or `false`. When disabled, no resources will be created. 