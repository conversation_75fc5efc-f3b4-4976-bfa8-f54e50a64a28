# FastScan Infrastructure Deployment Guide

This guide explains how to deploy the enhanced FastScan infrastructure.

## Deployment Methods

### Method 1: Environment-Specific Files (Recommended)

This method uses separate `.tfvars` files for each environment, providing clear separation and configuration management.

#### Step 1: Choose Your Environment Configuration

```bash
# Development deployment
terraform apply -var-file="environments/dev.tfvars"

# Staging deployment
terraform apply -var-file="environments/staging.tfvars"

# Production deployment
terraform apply -var-file="environments/prod.tfvars"
```

#### Step 2: Customize Environment Files

Edit the appropriate environment file:
- `environments/dev.tfvars` - Development environment
- `environments/staging.tfvars` - Staging environment
- `environments/prod.tfvars` - Production environment

### Method 2: Terraform Workspaces

This method uses Terraform workspaces to manage multiple environments in the same configuration.

#### Step 1: Create Workspaces

```bash
# Create development workspace
terraform workspace new dev
terraform workspace select dev
terraform apply -var-file="environments/dev.tfvars"

# Create staging workspace
terraform workspace new staging
terraform workspace select staging
terraform apply -var-file="environments/staging.tfvars"

# Create production workspace
terraform workspace new prod
terraform workspace select prod
terraform apply -var-file="environments/prod.tfvars"
```

#### Step 2: Switch Between Environments

```bash
# List available workspaces
terraform workspace list

# Switch to specific environment
terraform workspace select dev
terraform plan
terraform apply
```

## Step-by-Step Deployment

### 1. Initial Setup

```bash
# Clone the repository
git clone <your-repository-url>
cd fastscan/terraform

# Initialize Terraform
terraform init

# Copy and customize environment configuration
cp environments/dev.tfvars my-dev.tfvars
```

### 2. Customize Configuration

Edit `my-dev.tfvars`:
```hcl
# Core configuration
project_name = "your-project-name"
environment = "dev"

# Governance
owner = "your-team"
cost_center = "your-cost-center"
business_unit = "your-business-unit"

# Enable discovery features
enable_resource_discovery = true
```

### 3. Plan and Deploy

```bash
# Review the deployment plan
terraform plan -var-file="my-dev.tfvars"

# Deploy the infrastructure
terraform apply -var-file="my-dev.tfvars"

# View outputs
terraform output
```

### 4. Verify Deployment

```bash
# Check resource groups
aws resourcegroups list-groups

# View project metadata
aws ssm get-parameter --name '/your-project-name/dev/metadata' \
  --query 'Parameter.Value' --output text | jq .

# List all project resources
aws resourcegroupstaggingapi get-resources \
  --tag-filters Key=Project,Values=your-project-name Key=Environment,Values=dev
```

## Resource Discovery

### AWS Resource Groups

After deployment, you can find all resources using AWS Resource Groups:

```bash
# List all resource groups
aws resourcegroups list-groups

# Get resources in a specific group
aws resourcegroups get-group-query \
  --group-name "your-project-name-dev-resources"

# Search resources across all groups
aws resourcegroupstaggingapi get-resources \
  --tag-filters Key=Project,Values=your-project-name
```

### Systems Manager Parameter Store

Project metadata is stored in Parameter Store:

```bash
# Get project metadata
aws ssm get-parameter --name '/your-project-name/dev/metadata' \
  --query 'Parameter.Value' --output text | jq .

# Get resource inventory
aws ssm get-parameter --name '/your-project-name/dev/resource-inventory' \
  --query 'Parameter.Value' --output text | jq .
```

### CloudWatch Dashboard

Monitor your infrastructure using the automatically created dashboard:

```bash
# List dashboards
aws cloudwatch list-dashboards

# Get dashboard details
aws cloudwatch get-dashboard \
  --dashboard-name "your-project-name-dev-resource-overview"
```

## Cost Management

### Cost Allocation Tags

View costs by different dimensions:

```bash
# View costs by project
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=TAG,Key=Project

# View costs by environment
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=TAG,Key=Environment

# View costs by cost center
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=TAG,Key=CostCenter
```


## Security Best Practices

### IAM Least Privilege

Each service has its own IAM role with minimal required permissions:

```bash
# List IAM roles created by the project
aws iam list-roles --query 'Roles[?contains(RoleName, `your-project-name`)].RoleName'

# View specific role permissions
aws iam get-role-policy --role-name your-project-name-dev-nuclei-lambda-role --policy-name your-policy-name
```

### Network Security

```bash
# Review security groups
aws ec2 describe-security-groups \
  --filters "Name=tag:Project,Values=your-project-name" \
  --query 'SecurityGroups[*].{GroupId:GroupId,GroupName:GroupName,IpPermissions:IpPermissions}'

# Check VPC configuration
aws ec2 describe-vpcs \
  --filters "Name=tag:Project,Values=your-project-name"
```

## Monitoring and Alerting

### CloudWatch Logs

```bash
# List log groups
aws logs describe-log-groups \
  --log-group-name-prefix /aws/lambda/your-project-name

# View recent logs
aws logs get-log-events \
  --log-group-name /aws/lambda/your-project-name-dev-nuclei-function \
  --log-stream-name latest
```

### CloudWatch Metrics

```bash
# Get Lambda metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Invocations \
  --dimensions Name=FunctionName,Value=your-project-name-dev-nuclei-function \
  --start-time 2024-01-15T00:00:00Z \
  --end-time 2024-01-15T23:59:59Z \
  --period 3600 \
  --statistics Sum

# Get ECS metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=your-project-name-dev-nuclearpond-service \
  --start-time 2024-01-15T00:00:00Z \
  --end-time 2024-01-15T23:59:59Z \
  --period 300 \
  --statistics Average
```

### Common Issues

#### Resource Discovery Not Working

```bash
# Check if resource groups exist
aws resourcegroups list-groups

# Verify tags are applied correctly
aws resourcegroupstaggingapi get-resources \
  --tag-filters Key=Project,Values=your-project-name \
  --query 'ResourceTagMappingList[*].{ResourceArn:ResourceARN,Tags:Tags}'
```

#### Cost Allocation Tags Not Appearing

```bash
# Check if cost allocation tags are activated
aws ce list-cost-category-definitions

# Verify tags are properly formatted
aws resourcegroupstaggingapi get-resources \
  --tag-filters Key=cost-center,Values=your-cost-center
```

#### Deployment Failures

```bash
# Check Terraform state
terraform show

# Validate configuration
terraform validate

# Check AWS limits
aws service-quotas list-service-quotas --service-code lambda
```

