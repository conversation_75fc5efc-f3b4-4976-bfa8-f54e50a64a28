package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/scan"
	"nuclear_pond/pkg/types"

	"github.com/go-chi/render"
	"github.com/google/uuid"
)

// ScanHandler accepts a scan request and kicks off background processing.
func ScanHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan request: %s %s", r.Method, r.URL.Path)

	req, err := decodeScanRequestBody(r)
	if err != nil {
		if _, ok := err.(*badRequestError); !ok {
			log.Printf("Error decoding/validating JSON from %s: %v", r.Remote<PERSON>ddr, err)
		}
		writeAPIError(w, r, err)

		return
	}

	scanID := uuid.New().String()
	go scan.BackgroundScan(req, scanID)
	log.Printf("Started background scan: %s", scanID)

	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, types.Response{
		RequestID: scanID,
		Message:   "Scan request accepted and queued for processing",
	})
}
