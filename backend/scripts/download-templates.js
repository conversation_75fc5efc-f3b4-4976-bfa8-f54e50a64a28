#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const axios = require('axios');

// Paths
const CSV_FILE_PATH = path.join(__dirname, '..', 'ice_templates.csv');
const TEMPLATES_DIR = path.join(__dirname, '..', 'templates');

// Function to clean the templates directory
function cleanTemplatesDir() {
  console.log('Cleaning templates directory...');
  if (fs.existsSync(TEMPLATES_DIR)) {
    const files = fs.readdirSync(TEMPLATES_DIR);
    for (const file of files) {
      fs.unlinkSync(path.join(TEMPLATES_DIR, file));
    }
    console.log('Templates directory cleaned.');
  } else {
    fs.mkdirSync(TEMPLATES_DIR, { recursive: true });
    console.log('Templates directory created.');
  }
}

// Function to convert GitHub URL to raw content URL
function convertToRawUrl(githubUrl) {
  // Example input: https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-55591.yaml
  // Example output: https://raw.githubusercontent.com/projectdiscovery/nuclei-templates/main/http/cves/2024/CVE-2024-55591.yaml
  return githubUrl
    .replace('github.com', 'raw.githubusercontent.com')
    .replace('/blob/', '/');
}

// Debug function to print CSV structure
function debugCSV(records) {
  console.log('CSV Structure:');
  if (records.length > 0) {
    console.log('Column names:', Object.keys(records[0]));
    console.log('First record:', records[0]);
  } else {
    console.log('No records found in CSV');
  }
}

// Function to download a file
async function downloadFile(url, filePath) {
  try {
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
    });

    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Error downloading ${url}: ${error.message}`);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Starting template download process...');

    // Read and parse the CSV file
    const csvContent = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    const records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      delimiter: '\t'  // Use tab as delimiter
    });

    // Debug CSV structure
    debugCSV(records);

    console.log(`Found ${records.length} templates to download.`);

    // Clean the templates directory
    cleanTemplatesDir();

    // Download each template
    let successCount = 0;
    let failCount = 0;

    for (const [index, record] of records.entries()) {
      const { cve_id, nuclei_name, nuclei_template_url } = record;

      if (!nuclei_template_url) {
        console.log(`Skipping record ${index + 1}: No URL provided.`);
        continue;
      }

      const rawUrl = convertToRawUrl(nuclei_template_url);
      const fileName = path.basename(nuclei_template_url);
      const filePath = path.join(TEMPLATES_DIR, fileName);

      try {
        console.log(`Downloading (${index + 1}/${records.length}): ${nuclei_name} (${cve_id})`);
        await downloadFile(rawUrl, filePath);
        console.log(`✅ Successfully downloaded ${fileName}`);
        successCount++;
      } catch (error) {
        console.error(`❌ Failed to download ${fileName}: ${error.message}`);
        failCount++;
      }
    }

    console.log('\nDownload Summary:');
    console.log(`Total templates: ${records.length}`);
    console.log(`Successfully downloaded: ${successCount}`);
    console.log(`Failed to download: ${failCount}`);
    console.log(`Templates saved to: ${TEMPLATES_DIR}`);
    process.exit(0);

  } catch (error) {
    console.error('An error occurred:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();
