#!/usr/bin/env bash

# ==============================================================================
# LocalStack Lambda Deletion Script
# ==============================================================================
# This script deletes the Nuclei Lambda function from LocalStack.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

check_prerequisites() {
    log_info "Checking prerequisites..."
    require_tool "awslocal"
    log_success "All prerequisites met"
}

delete_lambda_function() {
    log_header "Deleting Lambda Function: ${LAMBDA_FUNCTION_NAME}"
    if awslocal lambda get-function --function-name "${LAMBDA_FUNCTION_NAME}" >/dev/null 2>&1; then
        log_info "Function found. Proceeding with deletion..."
        if ! awslocal lambda delete-function --function-name "${LAMBDA_FUNCTION_NAME}"; then
            fail "Failed to delete Lambda function."
        fi
        log_success "Lambda function '${LAMBDA_FUNCTION_NAME}' deleted successfully."
    else
        log_warning "Lambda function '${LAMBDA_FUNCTION_NAME}' does not exist. Nothing to do."
    fi
}

main() {
    check_prerequisites
    delete_lambda_function
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
