# ==============================================================================
# NUCLEI LAMBDA MODULE OUTPUTS
# ==============================================================================

output "nuclei_lambda_function_name" {
  description = "Name of the Nuclei Lambda function"
  value       = module.nuclei_lambda.lambda_function_name
}

output "nuclei_lambda_function_arn" {
  description = "ARN of the Nuclei Lambda function"
  value       = module.nuclei_lambda.lambda_function_arn
}

output "nuclei_s3_bucket_name" {
  description = "Name of the Nuclei artifacts S3 bucket"
  value       = module.nuclei_lambda.s3_bucket_name
}

output "nuclei_findings_s3_path" {
  description = "S3 path where Nuclei scan findings are stored"
  value       = module.nuclei_lambda.findings_s3_path
}

# Legacy outputs for backward compatibility
output "function_name" {
  description = "ARN of the Nuclei Lambda function (legacy - use nuclei_lambda_function_arn)"
  value       = module.nuclei_lambda.lambda_function_arn
}

output "dynamodb_state_table" {
  value = aws_dynamodb_table.scan_state_table.arn
}

# PoW outputs
output "pow_enabled" {
  description = "Whether the PoW infrastructure is enabled"
  value       = var.enable_pow_targets
}

output "pow_domain" {
  description = "The domain name used for PoW targets"
  value       = var.enable_pow_targets ? var.pow_domain_name : null
}

output "pow_zone_id" {
  description = "The Route53 hosted zone ID for the PoW domain"
  value       = var.enable_pow_targets ? module.pow_targets[0].pow_zone_id : null
}

output "pow_target_alb" {
  description = "ALB DNS name for PoW targets"
  value       = var.enable_pow_targets ? module.pow_targets[0].pow_alb_dns_name : null
}

output "pow_summary" {
  description = "Summary of deployed PoW infrastructure"
  value       = var.enable_pow_targets ? module.pow_targets[0].pow_summary : null
}

output "pow_example_urls" {
  description = "Example URLs for Nuclei scanning (after DNS is configured)"
  value       = var.enable_pow_targets ? [
    "http://${var.pow_domain_name}",
    "http://test1.${var.pow_domain_name}",
    "http://test2.${var.pow_domain_name}",
    "http://random-subdomain.${var.pow_domain_name}"
  ] : null
}

# Network module outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.network.vpc_id
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = module.network.public_subnet_ids
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.network.private_subnet_ids
}

# Nuclear Pond Backend module outputs
output "nuclear_pond_backend_ecr_repository_url" {
  description = "URL of the ECR repository for Nuclear Pond"
  value       = module.nuclear_pond_backend.ecr_repository_url
}

output "nuclear_pond_backend_alb_dns_name" {
  description = "DNS name of the Application Load Balancer for Nuclear Pond service"
  value       = module.nuclear_pond_backend.alb_dns_name
}

output "nuclear_pond_backend_service_url" {
  description = "Nuclear Pond Backend service URL"
  value       = module.nuclear_pond_backend.api_url
}

output "nuclear_pond_backend_health_check_url" {
  description = "Health check URL of the Nuclear Pond service"
  value       = module.nuclear_pond_backend.health_check_url
}

output "nuclear_pond_backend_ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = module.nuclear_pond_backend.ecs_cluster_name
}

output "nuclear_pond_backend_ecs_service_name" {
  description = "Name of the ECS service"
  value       = module.nuclear_pond_backend.ecs_service_name
}

output "nuclear_pond_backend_ecs_task_role_arn" {
  description = "ARN of the ECS task role for Nuclear Pond"
  value       = module.nuclear_pond_backend.ecs_task_role_arn
}

output "nuclear_pond_backend_log_group_name" {
  description = "Name of the CloudWatch log group for Nuclear Pond backend"
  value       = module.nuclear_pond_backend.cloudwatch_log_group_name
}

output "nuclear_pond_backend_deployment_info" {
  description = "Deployment information for the Nuclear Pond backend"
  value       = module.nuclear_pond_backend.deployment_info
}

# Legacy outputs for backward compatibility
output "nuclear_pond_ecr_repository_url" {
  description = "The URL of the ECR repository for Nuclear Pond (legacy - use nuclear_pond_backend_ecr_repository_url)"
  value       = module.nuclear_pond_backend.ecr_repository_url
}

output "nuclear_pond_alb_dns_name" {
  description = "The DNS name of the Application Load Balancer for Nuclear Pond service (legacy - use nuclear_pond_backend_alb_dns_name)"
  value       = module.nuclear_pond_backend.alb_dns_name
}

# ==============================================================================
# FRONTEND MODULE OUTPUTS
# ==============================================================================

output "frontend_url" {
  description = "Primary URL to access the frontend application"
  value       = module.frontend.frontend_url
}

output "frontend_s3_bucket_name" {
  description = "Name of the S3 bucket hosting the frontend static files"
  value       = module.frontend.frontend_s3_bucket_name
}

output "frontend_s3_website_url" {
  description = "Full HTTP URL for S3 static website (used when CloudFront is disabled)"
  value       = module.frontend.frontend_s3_website_url
}

output "frontend_cloudfront_domain_name" {
  description = "CloudFront distribution domain name (e.g., d123456789.cloudfront.net)"
  value       = module.frontend.cloudfront_distribution_domain_name
}

output "frontend_cloudfront_distribution_id" {
  description = "CloudFront distribution ID (used for cache invalidation)"
  value       = module.frontend.cloudfront_distribution_id
}

output "frontend_all_urls" {
  description = "All available URLs to access the frontend (for testing and reference)"
  value       = module.frontend.all_frontend_urls
}

output "frontend_deployment_info" {
  description = "Summary of frontend deployment configuration and next steps"
  value       = module.frontend.deployment_info
}
