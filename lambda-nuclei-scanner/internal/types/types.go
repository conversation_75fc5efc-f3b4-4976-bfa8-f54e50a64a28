package types

// ScanRequest is the input payload for a nuclei scan.
type ScanRequest struct {
	Targets []string `json:"targets"`
	Output  string   `json:"output"`

	// Orchestration metadata
	ScanID  string `json:"scan_id,omitempty"`  // dashless scan id provided by orchestrator
	<PERSON><PERSON><PERSON> string `json:"batch_id,omitempty"` // batch id per invocation

	// Template selection (whitelisted names only)
	Templates []string `json:"templates,omitempty"`

	// Execution options
	StreamOutput bool `json:"stream_output,omitempty"` // enable real-time output streaming (shows nuclei stats)
}

// ScanResponse is a legacy output schema (CMD/JSON strings).
type ScanResponse struct {
	Output string `json:"output"`
	Error  string `json:"error"`
}

// StructuredBatchResponse is the new preferred response with inline findings and metadata.
type StructuredBatchResponse struct {
	ScanID   string `json:"scan_id"`
	BatchID  string `json:"batch_id"`
	Error    string `json:"error,omitempty"`
	Findings []any  `json:"findings"`
}

// NucleiResult encapsulates nuclei execution output and metadata.
type NucleiResult struct {
	Error      error
	Output     string
	OutputFile string
	HasResults bool
}

// CLIConfig holds configuration for CLI execution.
type CLIConfig struct {
	Targets    string
	TargetFile string
	Args       string
	OutputFile string
}

// Environment contains runtime configuration for the scanner.
type Environment struct {
	NucleiBinary          string
	FileSystem            string
	TargetsFile           string
	ScanOutput            string
	SelectedTemplatePaths []string
	IsLambda              bool
}

// Constants
const (
	LambdaVersion = "2.1.0"

	OutputModeJSON = "json"
)
