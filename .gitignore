# Node
node_modules
.yarn/*
node_modules/.yarn-integrity
.pnp.loader.mjs
.pnp.cjs

# Terraform
terraform/terraform.tfvars
terraform/.terraform/*
terraform/terraform.tfstate.backup
terraform/terraform.tfstate
terraform/**/*.zip
terraform/terraform.tfstate.*.backup
terraform/nuclei-configs.zip
terraform/backend_backend/deploy-backend-dev.sh
terraform/frontend/DEPLOYMENT-DEV.md

# Backend
backend/bin/LICENSE.md
backend/bin/nuclei
backend/bin/README_CN.md
backend/bin/README_ID.md
backend/bin/README_KR.md
backend/bin/README.md
backend/backend

# Lambda Nuclei Scanner
lambda-nuclei-scanner/bootstrap
lambda-nuclei-scanner/nuclei-lambda

# Nuclei Templates
terraform/nuclei_lambda/temp_templates/

# Frontend
frontend/.env

# General
repomix*.md
nuclei-configs.zip

# AI local/temp files
.kiro/
.cursor/
GEMINI.md

# Devbox
.devbox/
devbox.lock

# Terraform env files
terraform/environments/prod.tfvars
terraform/environments/dev.tfvars
terraform/environments/staging.tfvars

# Localstack data
localstack-data/*
backend/tmp/main
backend/.air.toml

# python cache
__pycache__/
scripts/metadata-generator/metadata-generator
build/*
lambda-nuclei-scanner/lambda

lambda-nuclei-scanner/.env
backend/tmp/build-errors.log
