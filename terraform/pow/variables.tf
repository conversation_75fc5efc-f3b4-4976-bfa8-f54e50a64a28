variable "project_name" {
  description = "Name of the project, used for resource naming"
  type        = string
}

variable "environment" {
  description = "Deployment environment - affects resource naming and configuration"
  type        = string
  default     = "dev"

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "enable_pow_targets" {
  description = "Whether to create PoW demo target infrastructure"
  type        = bool
  default     = false
}

variable "pow_domain_name" {
  description = "The domain name to use for the PoW targets (e.g., fast-scan-pow.com)"
  type        = string
  default     = "fast-scan-demo-target.click"
}

variable "pow_create_hosted_zone" {
  description = "Whether to create the Route53 hosted zone if it doesn't exist"
  type        = bool
  default     = false
}

variable "pow_instance_type" {
  description = "EC2 instance type for the PoW targets"
  type        = string
  default     = "t3.micro"
}

variable "pow_key_name" {
  description = "SSH key pair name to use for the EC2 instances (optional)"
  type        = string
  default     = null
}

# Network configuration variables
variable "vpc_id" {
  description = "ID of the VPC where PoW targets will be deployed"
  type        = string
}

variable "public_subnet_ids" {
  description = "List of public subnet IDs for PoW target deployment"
  type        = list(string)
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs for PoW target deployment"
  type        = list(string)
}

