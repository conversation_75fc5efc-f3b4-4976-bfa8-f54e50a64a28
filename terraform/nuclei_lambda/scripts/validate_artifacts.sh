#!/bin/bash
# Artifact validation script for nuclei lambda deployment
# Usage: validate_artifacts.sh <bucket_name> <module_path>

set -euo pipefail

readonly REQUIRED_S3_OBJECTS=("nuclei-layer.zip" "templates-layer.zip" "templates.json")
readonly REQUIRED_LOCAL_FILES=("lambda-function.zip")
readonly MAX_RETRIES=3

validate_arguments() {
    if [ $# -ne 2 ]; then
        echo "Usage: $0 <bucket_name> <module_path>" >&2
        exit 2
    fi

    if [ -z "$1" ] || [ -z "$2" ]; then
        echo "Error: Bucket name and module path cannot be empty" >&2
        exit 2
    fi

    if [ ! -d "$2" ]; then
        echo "Error: Module path does not exist: $2" >&2
        exit 2
    fi

    if ! command -v aws >/dev/null 2>&1; then
        echo "Error: AWS CLI is not installed" >&2
        exit 2
    fi

    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        echo "Error: AWS credentials not configured" >&2
        exit 2
    fi
}

validate_s3_object() {
    local bucket_name="$1"
    local object_key="$2"
    local retries=0

    while [ $retries -lt $MAX_RETRIES ]; do
        if aws s3api head-object --bucket "$bucket_name" --key "$object_key" >/dev/null 2>&1; then
            local object_size
            object_size=$(aws s3api head-object --bucket "$bucket_name" --key "$object_key" --query 'ContentLength' --output text 2>/dev/null || echo "0")
            
            if [ "$object_size" -gt 0 ]; then
                echo "✓ S3 object $object_key validated ($object_size bytes)"
                return 0
            fi
        fi

        retries=$((retries + 1))
        if [ $retries -lt $MAX_RETRIES ]; then
            echo "Retrying S3 object $object_key (attempt $retries/$MAX_RETRIES)..."
            sleep 2
        fi
    done

    echo "✗ S3 object $object_key validation failed" >&2
    return 1
}

validate_local_file() {
    local file_path="$1"

    if [ ! -f "$file_path" ]; then
        echo "✗ Local file does not exist: $file_path" >&2
        return 1
    fi

    if [ ! -s "$file_path" ]; then
        echo "✗ Local file is empty: $file_path" >&2
        return 1
    fi

    local file_size
    file_size=$(stat -c%s "$file_path" 2>/dev/null || stat -f%z "$file_path" 2>/dev/null || echo "0")
    echo "✓ Local file $(basename "$file_path") validated ($file_size bytes)"
    return 0
}

main() {
    local bucket_name="$1"
    local module_path="$2"
    local validation_failed=false

    echo "Validating artifacts for bucket: $bucket_name"

    # Validate S3 objects
    for object_key in "${REQUIRED_S3_OBJECTS[@]}"; do
        if ! validate_s3_object "$bucket_name" "$object_key"; then
            validation_failed=true
        fi
    done

    # Validate local files
    for file_name in "${REQUIRED_LOCAL_FILES[@]}"; do
        local file_path="$module_path/$file_name"
        if ! validate_local_file "$file_path"; then
            validation_failed=true
        fi
    done

    if [ "$validation_failed" = true ]; then
        echo "✗ Artifact validation failed" >&2
        exit 1
    else
        echo "✓ All artifacts validated successfully"
        exit 0
    fi
}

validate_arguments "$@"
main "$@"
