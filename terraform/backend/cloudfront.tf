# CloudFront distribution for backend API
resource "aws_cloudfront_distribution" "backend_api" {
  origin {
    domain_name = aws_lb.nuclear_pond_alb.dns_name
    origin_id   = "backend-alb"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "http-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  enabled = true
  comment = "Backend API distribution for ${var.project_name} (${var.environment})"

  # API-optimized cache behavior - don't cache API responses
  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "backend-alb"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    # Use managed cache policy for APIs (no caching)
    cache_policy_id = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
    
    # Forward all headers, query strings, and cookies
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-backend-api-${var.environment}"
    Environment = var.environment
    Purpose     = "Backend API CDN"
  })
}

# Data sources for managed CloudFront policies
data "aws_cloudfront_cache_policy" "managed_caching_disabled" {
  name = "Managed-CachingDisabled"
}

data "aws_cloudfront_origin_request_policy" "managed_all_viewer" {
  name = "Managed-AllViewer"
}