import { createContext, useContext, useEffect, useState } from "react";
import { themeChange } from "theme-change";

type Theme = "corporate" | "dark";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const ThemeProviderContext = createContext<ThemeProviderState | undefined>(undefined);

const VALID_THEMES: Theme[] = ["corporate", "dark"];
const DEFAULT_THEME: Theme = "corporate";

function isValidTheme(theme: string): theme is Theme {
  return VALID_THEMES.includes(theme as Theme);
}

export function ThemeProvider({ children, defaultTheme = DEFAULT_THEME, storageKey = "theme", ...props }: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    try {
      const storedTheme = localStorage.getItem(storageKey);
      // Handle legacy theme values and invalid themes by falling back to default
      if (storedTheme && isValidTheme(storedTheme)) {
        return storedTheme;
      }
      return defaultTheme;
    } catch (error) {
      // Handle localStorage access errors (e.g., in SSR or private browsing)
      console.warn("Failed to access localStorage for theme:", error);
      return defaultTheme;
    }
  });

  useEffect(() => {
    // Initialize theme-change
    themeChange(false);

    // Ensure theme is valid before applying
    const validTheme = isValidTheme(theme) ? theme : DEFAULT_THEME;
    document.documentElement.setAttribute("data-theme", validTheme);

    // Update state if theme was invalid
    if (validTheme !== theme) {
      setTheme(validTheme);
    }
  }, [theme]);

  const value = {
    theme,
    setTheme: (newTheme: Theme) => {
      // Validate theme before setting
      const validTheme = isValidTheme(newTheme) ? newTheme : DEFAULT_THEME;

      try {
        localStorage.setItem(storageKey, validTheme);
      } catch (error) {
        // Handle localStorage write errors gracefully
        console.warn("Failed to save theme to localStorage:", error);
      }

      setTheme(validTheme);
      document.documentElement.setAttribute("data-theme", validTheme);
    },
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined) throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};
