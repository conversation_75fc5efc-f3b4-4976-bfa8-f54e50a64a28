package template

import (
	"encoding/json"
	"fmt"
	"log"
	"nuclear_pond/pkg/aws"
	"nuclear_pond/pkg/types"
	"os"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
)

// GetTemplateMetadata retrieves the template metadata from S3.
func GetTemplateMetadata() (*types.TemplateIndex, error) {
	awsConfig := aws.NewAWSConfig()
	sess, err := awsConfig.CreateSession()
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}

	bucket := os.Getenv("NUCLEI_TEMPLATES_BUCKET")
	if bucket == "" {
		return nil, fmt.Errorf("NUCLEI_TEMPLATES_BUCKET environment variable not set")
	}

	downloader := s3.New(sess)
	output, err := downloader.GetObject(&s3.GetObjectInput{
		Bucket: awssdk.String(bucket),
		Key:    awssdk.String("templates.json"),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to download templates.json: %w", err)
	}
	defer output.Body.Close()

	var templateIndex types.TemplateIndex
	if err := json.NewDecoder(output.Body).Decode(&templateIndex); err != nil {
		return nil, fmt.Errorf("failed to parse templates.json: %w", err)
	}

	// Basic validation
	if templateIndex.Templates == nil || templateIndex.Summary.Total == 0 {
		return nil, fmt.Errorf("templates.json is invalid or empty")
	}

	log.Printf("Successfully loaded template metadata version %s (%d templates)",
		templateIndex.Version, templateIndex.Summary.Total)

	return &templateIndex, nil
}
