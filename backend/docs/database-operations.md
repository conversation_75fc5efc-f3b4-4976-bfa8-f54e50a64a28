# Database Operations Reference

This document outlines the standardized database operations for the Nuclear Pond scanning system, providing a clean and consistent interface for managing scan data in DynamoDB.

## Overview

The Nuclear Pond system uses AWS DynamoDB to store and manage scan lifecycle data. All database operations follow a consistent pattern with proper error handling, logging, and data validation.

## Database Schema

### Primary Table: `scan_state_table`
- **Primary Key**: `scan_id` (String) - UUID without dashes for DynamoDB compatibility
- **TTL**: Automatic cleanup after 24 hours
- **Attributes**:
  - `scan_id`: Primary key (UUID without dashes)
  - `request_id`: Original UUID with dashes for API responses
  - `status`: Current scan status (`running`, `completed`, `failed`)
  - `targets`: List of target URLs/domains
  - `args`: Nuclei command-line arguments
  - `mode`: Execution mode (`local` or `cloud`)
  - `batches`: Number of target batches
  - `threads`: Number of concurrent threads
  - `output`: Output destination (`s3`, `local`)
  - `created_at`: ISO 8601 timestamp
  - `updated_at`: ISO 8601 timestamp
  - `completed_at`: ISO 8601 timestamp (optional)
  - `ttl`: Unix timestamp for automatic cleanup

## Error Handling Patterns

### 1. Session Management
```go
sess, err := session.NewSession(&aws.Config{
    Region: aws.String(os.Getenv("AWS_REGION")),
})
if err != nil {
    return fmt.Errorf("failed to create AWS session: %v", err)
}
```

### 2. Conditional Operations
```go
putInput := &dynamodb.PutItemInput{
    TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
    Item:      item,
    ConditionExpression: aws.String("attribute_not_exists(scan_id)"),
}
```

### 3. Graceful Degradation
```go
if err := storeScanRequest(scanRequest); err != nil {
    log.Printf("Failed to store scan request: %v", err)
    // Continue with execution even if storage fails
}
```

## Best Practices

1. **Separation of Concerns**: Each function has a single responsibility
2. **Consistent Logging**: All operations include structured logging
3. **Error Propagation**: Errors are properly wrapped with context
4. **Data Validation**: Input validation before database operations
5. **Resource Cleanup**: Proper session and resource management
6. **Atomic Operations**: Use conditional expressions for data consistency

## Monitoring and Debugging

All database operations include comprehensive logging:
- Operation start/completion
- Error conditions with context
- Performance metrics (implicit via CloudWatch)
- Data validation warnings

For debugging, check CloudWatch logs for:
- `"Storing new scan request in DynamoDB"`
- `"Failed to store scan request"`
- `"Failed to parse scan from DynamoDB"`
- `"Failed to update scan status in DynamoDB"`
