import { Link } from "@tanstack/react-router";
import { ArrowR<PERSON>, Layers, Shield, Zap } from "lucide-react";
import type React from "react";
import { BackgroundPattern } from "@/components/Layout/BackgroundPattern";

const Landing: React.FC = () => {
  return (
    <div className="min-h-screen relative flex flex-col">
      <BackgroundPattern />
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-base-100 via-base-200/50 to-primary/50" />
        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 py-24">
          <div className="text-center space-y-12">
            {/* Logo */}
            <div className="space-y-6">
              <div className="inline-flex items-center justify-center">
                <div className="text-4xl font-bold text-primary">
                  <span className="text-primary">Fast</span>
                  <span className="text-base-content">Scan</span>
                </div>
              </div>

              <div className="space-y-4">
                <p className="text-xl text-base-content/70 font-medium max-w-2xl mx-auto">
                  Next-generation platform built for speed, efficiency, and scale. Empowering teams to achieve more with cutting-edge technology.
                </p>
              </div>
            </div>

            {/* CTA */}
            <div>
              <Link to="/login">
                <button type="button" className="btn btn-primary btn-lg gap-2">
                  <ArrowRight className="h-5 w-5" />
                  Get Started
                </button>
              </Link>
              <p className="text-sm text-base-content/70 mt-2">Enterprise-grade platform launching soon</p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 relative">
        {/* Background gradient from top right angle to bottom left */}
        <div className="absolute inset-0 bg-gradient-to-tr from-base-200/10 via-base-200/50 to-primary/50" />
        <div className="relative z-10 max-w-6xl mx-auto px-4">
          <div className="text-center space-y-16">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-base-content">Built for Performance and Reliability</h2>
              <p className="text-lg text-base-content/70 max-w-2xl mx-auto">
                Modern cloud-native architecture delivers exceptional performance and reliability for today's demanding enterprise environments.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="py-12 gap-6 flex flex-col items-center justify-end flex-1 relative">
        <div className="absolute inset-0 bg-gradient-to-tr from-base-100 via-base-200/50 to-base-200/20" />
        <div className="flex flex-row flex-wrap items-center justify-center gap-2 lg:gap-3">
          <div className="badge badge-primary gap-2">
            <Zap className="h-4 w-4" />
            Lightning Fast
          </div>
          <div className="badge badge-secondary gap-2">
            <Shield className="h-4 w-4" />
            Enterprise Ready
          </div>
          <div className="badge badge-accent gap-2">
            <Layers className="h-4 w-4" />
            Modern Platform
          </div>
        </div>
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-center gap-8 text-sm text-base-content/70">
            <span className="z-10">
              <i>© 2025 evidence</i>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing;
