package server

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"nuclear_pond/pkg/auth"
	"nuclear_pond/pkg/aws"
	"nuclear_pond/pkg/handlers"
	"nuclear_pond/pkg/middleware"
	"nuclear_pond/pkg/types"
	"os"
	"testing"

	"github.com/go-chi/chi/v5"
	chiMiddleware "github.com/go-chi/chi/v5/middleware"
)

func setupTestRouter() *chi.Mux {
	r := chi.NewRouter()
	r.Use(chiMiddleware.Logger)
	r.Use(chiMiddleware.Recoverer)
	r.Use(middleware.CORSMiddleware)

	// Public endpoints (no authentication required)
	r.Get("/health-check", handlers.HealthHandler)
	r.Post("/auth", handlers.AuthHandler)

	// Protected routes (require JWT authentication)
	r.Group(func(r chi.Router) {
		r.Use(middleware.JWTMiddleware)
		r.Get("/", handlers.IndexHandler)
		r.Post("/scan", handlers.ScanHandler)
		r.Get("/scan/{scanId}", handlers.ScanStatusHandler)
		r.Get("/templates", handlers.GetTemplatesHandler)
	})

	return r
}

func TestHealthHandler(t *testing.T) {
	router := setupTestRouter()

	req, err := http.NewRequest("GET", "/health-check", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	var response types.Response
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", response.Status)
	}
}

func TestIndexHandlerWithoutAuthentication(t *testing.T) {
	router := setupTestRouter()

	req, err := http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	// Without authentication, should return 401
	if status := rr.Code; status != http.StatusUnauthorized {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusUnauthorized)
	}
}

func TestIndexHandlerWithValidJWTToken(t *testing.T) {
	// Set up test JWT secret
	os.Setenv("JWT_SECRET", "test-jwt-secret-for-testing")
	defer os.Unsetenv("JWT_SECRET")

	// Generate a real JWT token for testing
	token, err := auth.GenerateJWT("test-user")
	if err != nil {
		t.Fatalf("Failed to generate test JWT token: %v", err)
	}

	router := setupTestRouter()

	req, err := http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Authorization", "Bearer "+token)

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	var response types.Response
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Message != "Welcome to the Nuclear Pond API" {
		t.Errorf("Expected welcome message, got '%s'", response.Message)
	}
}

func TestScanHandlerValidation(t *testing.T) {
	// Set up test JWT secret
	os.Setenv("JWT_SECRET", "test-jwt-secret-for-testing")
	defer os.Unsetenv("JWT_SECRET")

	// Generate a real JWT token for testing
	token, err := auth.GenerateJWT("test-user")
	if err != nil {
		t.Fatalf("Failed to generate test JWT token: %v", err)
	}

	router := setupTestRouter()

	// Test with empty targets
	reqBody := types.Request{
		Targets: []string{},
		Batches: 1,
		Threads: 1,
	}

	jsonBody, _ := json.Marshal(reqBody)
	req, err := http.NewRequest("POST", "/scan", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusBadRequest)
	}

	var response types.Response
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Error != "Bad Request" {
		t.Errorf("Expected 'Bad Request' error, got '%s'", response.Error)
	}
}

func TestAuthHandler(t *testing.T) {
	// Set up test demo password
	os.Setenv("DEMO_PASSWORD", "TestPass123")
	defer os.Unsetenv("DEMO_PASSWORD")

	router := setupTestRouter()

	// Test with correct password
	authReq := map[string]string{"password": "TestPass123"}
	jsonBody, _ := json.Marshal(authReq)
	req, err := http.NewRequest("POST", "/auth", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	var authResponse map[string]string
	if err := json.Unmarshal(rr.Body.Bytes(), &authResponse); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if authResponse["token"] == "" {
		t.Error("Expected token in response")
	}

	// Test with incorrect password
	authReq = map[string]string{"password": "WrongPassword"}
	jsonBody, _ = json.Marshal(authReq)
	req, err = http.NewRequest("POST", "/auth", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	rr = httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusUnauthorized {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusUnauthorized)
	}
}

func TestCORSHeaders(t *testing.T) {
	router := setupTestRouter()

	req, err := http.NewRequest("OPTIONS", "/health-check", nil)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Origin", "http://localhost:5175")

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Check CORS headers
	if origin := rr.Header().Get("Access-Control-Allow-Origin"); origin != "*" {
		t.Errorf("Expected Access-Control-Allow-Origin '*', got '%s'", origin)
	}

	if methods := rr.Header().Get("Access-Control-Allow-Methods"); methods != "GET, POST, OPTIONS" {
		t.Errorf("Expected Access-Control-Allow-Methods 'GET, POST, OPTIONS', got '%s'", methods)
	}
}

func TestLocalStackIntegration(t *testing.T) {
	// Skip this test if not in local development mode
	if os.Getenv("LOCAL_DEVELOPMENT") != "true" {
		t.Skip("Skipping LocalStack integration test - not in local development mode")
	}

	// Set up LocalStack environment
	os.Setenv("LOCAL_DEVELOPMENT", "true")
	os.Setenv("AWS_ENDPOINT_URL", "http://localhost:4566")
	os.Setenv("AWS_REGION", "us-east-1")
	os.Setenv("AWS_ACCESS_KEY_ID", "test")
	os.Setenv("AWS_SECRET_ACCESS_KEY", "test")

	defer func() {
		os.Unsetenv("LOCAL_DEVELOPMENT")
		os.Unsetenv("AWS_ENDPOINT_URL")
		os.Unsetenv("AWS_REGION")
		os.Unsetenv("AWS_ACCESS_KEY_ID")
		os.Unsetenv("AWS_SECRET_ACCESS_KEY")
	}()

	// Create AWS configuration
	awsConfig := aws.NewAWSConfig()

	// Verify LocalStack configuration
	if !awsConfig.IsLocal {
		t.Error("Expected IsLocal to be true for LocalStack integration test")
	}

	if awsConfig.Endpoint != "http://localhost:4566" {
		t.Errorf("Expected endpoint http://localhost:4566, got %s", awsConfig.Endpoint)
	}

	// Create AWS session
	sess, err := awsConfig.CreateSession()
	if err != nil {
		t.Fatalf("Failed to create AWS session: %v", err)
	}

	// Verify session configuration
	if *sess.Config.Region != "us-east-1" {
		t.Errorf("Expected session region us-east-1, got %s", *sess.Config.Region)
	}

	if *sess.Config.Endpoint != "http://localhost:4566" {
		t.Errorf("Expected endpoint http://localhost:4566, got %s", *sess.Config.Endpoint)
	}

	if !*sess.Config.S3ForcePathStyle {
		t.Error("Expected S3ForcePathStyle to be true for LocalStack")
	}

	// Verify credentials
	creds, err := sess.Config.Credentials.Get()
	if err != nil {
		t.Fatalf("Failed to get credentials: %v", err)
	}

	if creds.AccessKeyID != "test" {
		t.Errorf("Expected AccessKeyID 'test', got %s", creds.AccessKeyID)
	}

	if creds.SecretAccessKey != "test" {
		t.Errorf("Expected SecretAccessKey 'test', got %s", creds.SecretAccessKey)
	}
}
