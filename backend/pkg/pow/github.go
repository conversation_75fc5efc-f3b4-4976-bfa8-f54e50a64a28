package pow

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"nuclear_pond/pkg/types"
	"os"
	"time"
)

// TriggerGitHubWorkflow triggers a GitHub Actions workflow.
func TriggerGitHubWorkflow(action, requestID string) error {
	// Get required environment variables
	githubToken := os.Getenv("GITHUB_TOKEN")
	githubRepo := os.Getenv("GITHUB_REPOSITORY") // format: "owner/repo"

	if githubToken == "" {
		return fmt.Errorf("GITHUB_TOKEN environment variable is required")
	}

	if githubRepo == "" {
		return fmt.Errorf("GITHUB_REPOSITORY environment variable is required (format: owner/repo)")
	}

	// Convert action to workflow event type
	var eventType string
	switch action {
	case "start":
		eventType = "pow-start"
	case "stop":
		eventType = "pow-stop"
	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	// Prepare payload
	payload := types.GitHubDispatchPayload{
		EventType: eventType,
		ClientPayload: map[string]interface{}{
			"request_id": requestID,
			"action":     action,
			"timestamp":  time.Now().Unix(),
			"source":     "nuclear-pond-api",
		},
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request to GitHub API
	url := fmt.Sprintf("https://api.github.com/repos/%s/dispatches", githubRepo)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Accept", "application/vnd.github.v3+json")
	req.Header.Set("Authorization", "token "+githubToken)
	req.Header.Set("Content-Type", "application/json")

	// Send request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Check response
	if resp.StatusCode != http.StatusNoContent {
		return fmt.Errorf("GitHub API returned status %d", resp.StatusCode)
	}

	log.Printf("Successfully triggered GitHub workflow: event_type=%s, request_id=%s", eventType, requestID)

	return nil
}
