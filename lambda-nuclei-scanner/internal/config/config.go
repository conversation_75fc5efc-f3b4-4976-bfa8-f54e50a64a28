package config

import (
	"log"
	"main/internal/types"
	"os"
	"path/filepath"
)

// findFirstExisting returns the first existing path from candidates.
func findFirstExisting(candidates []string) string {
	for _, p := range candidates {
		if stat, err := os.Stat(p); err == nil && !stat.IsDir() {
			return p
		}
	}

	return ""
}

// SetupLambdaEnvironment configures paths and HOME for Lambda execution.
func SetupLambdaEnvironment() (*types.Environment, error) {
	nucleiBinaryPath := findFirstExisting([]string{
		"/opt/nuclei",
		"/opt/bin/nuclei",
		"/var/task/opt/nuclei",
		"./opt/nuclei",
	})
	if nucleiBinaryPath == "" {
		log.Printf("Nuclei binary not found in expected locations")
		nucleiBinaryPath = "/opt/nuclei" // default for error messaging later
	}

	env := &types.Environment{
		NucleiBinary: nucleiBinaryPath,
		FileSystem:   "/tmp/",
		IsLambda:     true,
	}

	env.TargetsFile = env.FileSystem + "targets.txt"
	env.ScanOutput = env.FileSystem + "output.json"

	homeDir := "/tmp"
	if err := os.Setenv("HOME", homeDir); err != nil {
		return nil, err
	}

	nucleiConfigDirPath := filepath.Join(homeDir, ".config", "nuclei")
	if err := os.MkdirAll(nucleiConfigDirPath, 0o755); err != nil {
		return nil, err
	}

	nucleiIgnoreFilePath := filepath.Join(nucleiConfigDirPath, ".nuclei-ignore")
	if _, err := os.Stat(nucleiIgnoreFilePath); os.IsNotExist(err) {
		if file, err := os.Create(nucleiIgnoreFilePath); err != nil {
			return nil, err
		} else {
			file.Close()
		}
	}

	return env, nil
}
