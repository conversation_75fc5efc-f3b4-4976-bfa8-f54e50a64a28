{"name": "fastscan-monorepo", "private": true, "version": "1.0.0", "packageManager": "yarn@4.9.2", "type": "module", "author": "max <<EMAIL>>", "license": "MIT", "workspaces": ["frontend", "backend", "terraform", "lambda-nuclei-scanner"], "scripts": {"dev:frontend": "yarn workspace fastscan-frontend dev", "build:frontend": "yarn workspace frontend build", "build:frontend:dev": "yarn workspace frontend build:dev", "build:frontend:staging": "yarn workspace frontend build:staging", "build:frontend:prod": "yarn workspace frontend build:prod", "deploy:frontend:dev": "yarn workspace frontend deploy:dev", "deploy:frontend:staging": "yarn workspace frontend deploy:staging", "deploy:frontend:prod": "yarn workspace frontend deploy:prod", "tf:destroy": "yarn workspace terraform destroy", "tf:plan": "yarn workspace terraform plan", "tf:apply": "yarn workspace terraform apply && ./terraform/scripts/post-apply-hooks/generate-metadata.sh", "tf:frontend:plan": "cd terraform && terraform plan -target=module.frontend", "tf:frontend:apply": "cd terraform && terraform apply -target=module.frontend", "lambda:build": "cd lambda-nuclei-scanner && GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap", "lambda:test": "cd lambda-nuclei-scanner && go test ./...", "lambda:clean": "cd lambda-nuclei-scanner && rm -f bootstrap nuclei-lambda", "metadata:generate": "devbox run local:metadata", "metadata:verify": "devbox run local:verify-metadata", "metadata:update": "scripts/tasks/aws/update-template-metadata.sh", "pack:terraform": "yarn dlx repomix -o repomix_terraform.md terraform/", "pack:backend": "yarn dlx repomix -o repomix_backend.md backend/", "pack:lambda": "yarn dlx repomix -o repomix_lambda.md lambda-nuclei-scanner/", "cc:usage": "yarn dlx ccusage@latest blocks --live"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@testing-library/dom": "^10.4.0", "jsdom": "^26.1.0"}}