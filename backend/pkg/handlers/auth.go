package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"nuclear_pond/pkg/auth"
	"nuclear_pond/pkg/types"
	"os"
	"strings"

	"github.com/go-chi/render"
)

// AuthRequest represents a simple authentication request
type AuthRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// AuthResponse represents the authentication response with JWT token
type AuthResponse struct {
	Token   string `json:"token"`
	Message string `json:"message"`
}

// allowing only @rootevidence.com emails for demo purposes

// AuthHandler handles simple password-based authentication and returns a JWT token
func AuthHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Auth request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	var authReq AuthRequest
	if err := json.NewDecoder(r.Body).Decode(&authReq); err != nil {
		log.Printf("Failed to decode auth request: %v", err)
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, types.Response{
			Error:   "Bad Request",
			Message: "Invalid JSON in request body",
		})

		return
	}

	// Check if email is @rootevidence.com
	if !strings.Contains(authReq.Email, "@rootevidence.com") {
		log.Printf("Invalid email attempt from %s", r.RemoteAddr)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, types.Response{
			Error:   "Unauthorized",
			Message: "Invalid email",
		})

		return
	}

	// Simple password check (for demo purposes)
	demoPassword := os.Getenv("DEMO_PASSWORD")
	if authReq.Password != demoPassword {
		log.Printf("Invalid password attempt from %s", r.RemoteAddr)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, types.Response{
			Error:   "Unauthorized",
			Message: "Invalid password",
		})

		return
	}

	// Generate real JWT token for demo user
	token, err := auth.GenerateJWT(authReq.Email)
	if err != nil {
		log.Printf("Failed to generate JWT token: %v", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, types.Response{
			Error:   "Internal Server Error",
			Message: "Failed to generate authentication token",
		})

		return
	}

	log.Printf("Successful authentication from %s", r.RemoteAddr)
	render.JSON(w, r, AuthResponse{
		Token:   token,
		Message: "Authentication successful",
	})
}
