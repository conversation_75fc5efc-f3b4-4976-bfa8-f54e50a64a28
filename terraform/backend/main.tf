# ==============================================================================
# NUCLEAR POND BACKEND MODULE
# ==============================================================================
# This module deploys the Nuclear Pond backend service on AWS ECS Fargate
# with Application Load Balancer, ECR repository, and all required resources.

# Data sources for AWS information
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Generate deployment configuration for the backend
resource "local_file" "backend_env" {
  content = <<-EOT
# Nuclear Pond Backend Environment Configuration for ${var.environment}
# Generated automatically by Terraform - do not edit manually

# Authentication Configuration
JWT_SECRET=${var.jwt_secret != null ? var.jwt_secret : "demo-jwt-secret-key"}
DEMO_PASSWORD=${var.demo_password != null ? var.demo_password : "DemoPass1837466"}

# AWS Configuration
AWS_REGION=${data.aws_region.current.name}
AWS_LAMBDA_FUNCTION_NAME=${var.lambda_function_name}
AWS_DYNAMODB_TABLE=${var.dynamodb_table_name}
AWS_S3_BUCKET=${aws_s3_bucket.nuclear_pond_targets.bucket}
NUCLEI_TEMPLATES_BUCKET=${var.nuclei_templates_bucket}

# ECS Configuration
ECS_CLUSTER=${aws_ecs_cluster.nuclear_pond_cluster.name}
ECS_SERVICE=${aws_ecs_service.nuclear_pond_service.name}

# Load Balancer Configuration
ALB_DNS_NAME=${aws_lb.nuclear_pond_alb.dns_name}
ALB_ZONE_ID=${aws_lb.nuclear_pond_alb.zone_id}

# ECR Configuration
ECR_REPOSITORY_URL=${aws_ecr_repository.nuclear_pond_repo.repository_url}

# GitHub Configuration
GITHUB_TOKEN=${var.github_token}
GITHUB_REPOSITORY=${var.github_repository}

# Deployment Information (for reference)
# Environment: ${var.environment}
# CPU: ${var.task_cpu}
# Memory: ${var.task_memory}
# Desired Count: ${var.desired_count}
EOT

  filename = "${path.module}/../../nuclear_pond/.env.${var.environment}"

  # Set appropriate permissions for environment files
  file_permission = "0644"
}

# Create deployment script for the backend
resource "local_file" "deploy_script" {
  content = <<-EOT
#!/bin/bash
# Nuclear Pond Backend Deployment Script for ${var.environment} Environment
# Generated automatically by Terraform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="${var.environment}"
ECR_REPOSITORY_URL="${aws_ecr_repository.nuclear_pond_repo.repository_url}"
IMAGE_TAG="$${1:-latest}"
ECS_CLUSTER="${aws_ecs_cluster.nuclear_pond_cluster.name}"
ECS_SERVICE="${aws_ecs_service.nuclear_pond_service.name}"

echo -e "$${BLUE}=== Nuclear Pond Backend Deployment Script for $${ENVIRONMENT} ===$${NC}"
echo -e "$${BLUE}ECR Repository: $${ECR_REPOSITORY_URL}$${NC}"
echo -e "$${BLUE}Image Tag: $${IMAGE_TAG}$${NC}"
echo -e "$${BLUE}ECS Cluster: $${ECS_CLUSTER}$${NC}"
echo -e "$${BLUE}ECS Service: $${ECS_SERVICE}$${NC}"
echo ""

# Check dependencies
echo -e "$${YELLOW}Checking dependencies...$${NC}"
if ! command -v aws &> /dev/null; then
    echo -e "$${RED}Error: AWS CLI is not installed or not in PATH$${NC}"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo -e "$${RED}Error: Docker is not installed or not in PATH$${NC}"
    exit 1
fi

# Navigate to nuclear_pond directory
echo -e "$${YELLOW}Navigating to nuclear_pond directory...$${NC}"
cd ../../nuclear_pond

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    echo -e "$${RED}Error: Dockerfile not found in nuclear_pond directory$${NC}"
    exit 1
fi

# Login to ECR
echo -e "$${YELLOW}Logging in to ECR...$${NC}"
aws ecr get-login-password --region ${data.aws_region.current.name} | docker login --username AWS --password-stdin $${ECR_REPOSITORY_URL%/*}

# Build Docker image
echo -e "$${YELLOW}Building Docker image...$${NC}"
docker build -t $${ECR_REPOSITORY_URL}:$${IMAGE_TAG} .

# Push image to ECR
echo -e "$${YELLOW}Pushing image to ECR...$${NC}"
docker push $${ECR_REPOSITORY_URL}:$${IMAGE_TAG}

# Force new deployment of ECS service
echo -e "$${YELLOW}Forcing new deployment of ECS service...$${NC}"
aws ecs update-service --cluster $${ECS_CLUSTER} --service $${ECS_SERVICE} --force-new-deployment > /dev/null

echo ""
echo -e "$${GREEN}=== Deployment Complete! ===$${NC}"
echo -e "$${GREEN}Backend URL: https://${aws_cloudfront_distribution.backend_api.domain_name}$${NC}"
echo -e "$${GREEN}Health Check: https://${aws_cloudfront_distribution.backend_api.domain_name}/health-check$${NC}"
echo -e "$${BLUE}ALB Direct (HTTP): http://${aws_lb.nuclear_pond_alb.dns_name}$${NC}"

# Show deployment status
echo ""
echo -e "$${BLUE}=== Deployment Status ===$${NC}"
echo -e "$${BLUE}Checking ECS service status...$${NC}"
aws ecs describe-services --cluster $${ECS_CLUSTER} --services $${ECS_SERVICE} --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount}' --output table
EOT

  filename        = "${path.module}/deploy-backend-${var.environment}.sh"
  file_permission = "0755"
}
