import { toHumanString } from "./human-readable";

export interface ProgressEstimationConfig {
  minDuration: number; // 30 seconds minimum
  maxDuration: number; // 300 seconds (5 minutes) maximum for very large scans
  baselineTargets: number; // 1,000 targets
  baselineDuration: number; // 30 seconds
}

export interface ProgressEstimate {
  estimatedDuration: number;
  completionPercentage: number;
  currentPhase: string;
  statusMessage: string;
}

const DEFAULT_CONFIG: ProgressEstimationConfig = {
  minDuration: 30,
  maxDuration: 120,
  baselineTargets: 1000,
  baselineDuration: 30,
};

export const calculateProgressEstimate = (
  targetCount: number,
  templateCount: number,
  startTime: string,
  status: string,
  config: ProgressEstimationConfig = DEFAULT_CONFIG,
): ProgressEstimate => {
  const now = Date.now();
  const start = new Date(startTime).getTime();
  const elapsed = Math.max(0, (now - start) / 1000);

  // Calculate realistic estimated duration
  // Base: 30s for 1k targets, scaling logarithmically for larger scans
  const targetRatio = targetCount / config.baselineTargets;
  const scalingFactor = Math.log10(Math.max(1, targetRatio)) + 1;
  const templateMultiplier = Math.max(1, templateCount / 10);

  let estimatedDuration = config.baselineDuration * scalingFactor * templateMultiplier;

  // Ensure minimum duration and cap maximum
  estimatedDuration = Math.max(config.minDuration, Math.min(config.maxDuration, estimatedDuration));
  console.log("🚀 ~ calculateProgressEstimate ~ estimatedDuration:", estimatedDuration);

  // Calculate mocked progress that advances smoothly through phases
  let currentPhase = "Initializing scanning swarm...";
  let completionPercentage = 0;
  let statusMessage = "Preparing distributed scanning environment";

  if (status === "queued") {
    currentPhase = "Waiting for scanner resources...";
    statusMessage = "Your scan is queued and will start shortly";
    completionPercentage = 0;
  } else if (status === "running") {
    // Create smooth, realistic progress through phases
    const progressRatio = Math.min(0.95, elapsed / estimatedDuration);
    console.log("🚀 ~ calculateProgressEstimate ~ progressRatio:", progressRatio);
    completionPercentage = progressRatio * 100;
    console.log("🚀 ~ calculateProgressEstimate ~ completionPercentage:", completionPercentage);

    // Phase transitions with realistic timing
    if (progressRatio < 0.15) {
      currentPhase = "Initializing scanning swarm...";
      statusMessage = "Deploying distributed scanners across infrastructure";
      // Progress smoothly from 0% to 15%
      completionPercentage = (elapsed / (estimatedDuration * 0.15)) * 15;
    } else if (progressRatio < 0.25) {
      currentPhase = "Starting vulnerability scanners...";
      statusMessage = `Activating security detection modules`;
      // Progress from 15% to 25%
      const phaseProgress = (progressRatio - 0.15) / 0.1;
      completionPercentage = 15 + phaseProgress * 10;
    } else if (progressRatio < 0.35) {
      currentPhase = "Performing initial reconnaissance...";
      statusMessage = `Discovering services across ${toHumanString(targetCount)} targets`;
      // Progress from 25% to 35%
      const phaseProgress = (progressRatio - 0.25) / 0.1;
      completionPercentage = 25 + phaseProgress * 10;
    } else if (progressRatio < 0.8) {
      currentPhase = "Deep vulnerability scanning...";
      statusMessage = `Analyzing security posture and potential exposures`;
      // Progress from 35% to 80% (main scanning phase)
      const phaseProgress = (progressRatio - 0.35) / 0.45;
      completionPercentage = 35 + phaseProgress * 45;
    } else {
      currentPhase = "Gathering results from scanners...";
      statusMessage = "Consolidating findings and generating security report";
      // Progress from 80% to 95%
      const phaseProgress = (progressRatio - 0.8) / 0.15;
      completionPercentage = 80 + phaseProgress * 15;
    }

    // Ensure we never exceed 95% until actually completed
    completionPercentage = Math.min(95, completionPercentage);
  }

  return {
    estimatedDuration,
    completionPercentage,
    currentPhase,
    statusMessage,
  };
};
