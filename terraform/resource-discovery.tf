# ==============================================================================
# RESOURCE DISCOVERY AND TRACKING
# ==============================================================================
# This file contains resources for tracking and discovering all AWS resources
# created by this Terraform project across different environments.

# ==============================================================================
# AWS RESOURCE GROUPS
# ==============================================================================

# Primary resource group for all project resources
resource "aws_resourcegroups_group" "project_resources" {
  count = var.enable_resource_discovery ? 1 : 0

  name        = local.naming.resource_group
  description = "All resources for ${var.project_name} project in ${var.environment} environment"

  resource_query {
    query = jsonencode({
      ResourceTypeFilters = ["AWS::AllSupported"]
      TagFilters = [
        {
          Key    = "Project"
          Values = [var.project_name]
        },
        {
          Key    = "Environment"
          Values = [var.environment]
        }
      ]
    })
  }

  tags = merge(local.common_tags, {
    Name        = local.naming.resource_group
    Purpose     = "resource-discovery"
    ResourceType = "management"
  })
}

# Environment-specific resource group
resource "aws_resourcegroups_group" "environment_resources" {
  count = var.enable_resource_discovery ? 1 : 0

  name        = "${local.naming.resource_group}-by-environment"
  description = "Resources grouped by environment for ${var.project_name}"

  resource_query {
    query = jsonencode({
      ResourceTypeFilters = ["AWS::AllSupported"]
      TagFilters = [
        {
          Key    = "Project"
          Values = [var.project_name]
        }
      ]
    })
  }

  tags = merge(local.common_tags, {
    Name        = "${local.naming.resource_group}-by-environment"
    Purpose     = "environment-grouping"
    ResourceType = "management"
  })
}

# Cost allocation resource group
resource "aws_resourcegroups_group" "cost_allocation" {
  count = var.enable_resource_discovery ? 1 : 0

  name        = "${local.naming.resource_group}-cost-allocation"
  description = "Resources for cost allocation tracking - ${var.project_name}"

  resource_query {
    query = jsonencode({
      ResourceTypeFilters = ["AWS::AllSupported"]
      TagFilters = [
        {
          Key    = "CostCenter"
          Values = [var.cost_center]
        },
        {
          Key    = "Project"
          Values = [var.project_name]
        }
      ]
    })
  }

  tags = merge(local.common_tags, {
    Name        = "${local.naming.resource_group}-cost-allocation"
    Purpose     = "cost-tracking"
    ResourceType = "management"
  })
}


# ==============================================================================
# SYSTEMS MANAGER PARAMETER STORE - PROJECT METADATA
# ==============================================================================

# Store project metadata in Parameter Store for easy discovery
resource "aws_ssm_parameter" "project_metadata" {
  count = var.enable_resource_discovery ? 1 : 0

  name  = "/${var.project_name}/${var.environment}/metadata"
  type  = "String"
  value = jsonencode({
    project_name     = var.project_name
    environment      = var.environment
    deployment_id    = local.deployment_timestamp
    account_id       = local.account_id
    region           = local.region
    owner            = var.owner
    cost_center      = var.cost_center
    business_unit    = var.business_unit
    created_date     = formatdate("YYYY-MM-DD", plantimestamp())
    
    # Resource endpoints and identifiers
    lambda_function_name = local.naming.lambda_function
    ecs_cluster_name     = local.naming.ecs_cluster
    ecs_service_name     = local.naming.ecs_service
    vpc_name             = local.naming.vpc
    
    # Resource group information
    resource_group_name = local.naming.resource_group
    
    # Component status
    components = {
      nuclei_lambda        = true
      network              = true
      nuclear_pond_backend = true
      frontend             = true
      pow_targets          = var.enable_pow_targets
    }
  })

  description = "Project metadata for ${var.project_name} in ${var.environment} environment"

  tags = merge(local.common_tags, {
    Name        = "${var.project_name}-${var.environment}-metadata"
    Purpose     = "project-metadata"
    ResourceType = "configuration"
  })
}

# Store resource inventory in Parameter Store
resource "aws_ssm_parameter" "resource_inventory" {
  count = var.enable_resource_discovery ? 1 : 0

  name  = "/${var.project_name}/${var.environment}/resource-inventory"
  type  = "String"
  value = jsonencode({
    last_updated = formatdate("YYYY-MM-DD hh:mm:ss", plantimestamp())
    
    # Expected resources by category
    compute_resources = {
      lambda_functions = [local.naming.lambda_function]
      ecs_clusters    = [local.naming.ecs_cluster]
      ecs_services    = [local.naming.ecs_service]
    }
    
    storage_resources = {
      s3_buckets = [
        "${local.naming.s3_bucket_prefix}-nuclei-artifacts-${local.region}",
        "${local.naming.s3_bucket_prefix}-nuclear-pond-targets-*"
      ]
      dynamodb_tables = [local.naming.dynamodb_table]
      ecr_repositories = [local.naming.ecr_repository]
    }
    
    network_resources = {
      vpc_name = local.naming.vpc
      subnets = [
        "${local.naming.subnet_public_prefix}-az1",
        "${local.naming.subnet_public_prefix}-az2",
        "${local.naming.subnet_private_prefix}-az1"
      ]
      load_balancers = [local.naming.alb]
    }
    
    # Resource discovery queries
    discovery_queries = {
      all_resources = {
        tag_filters = [
          { key = "Project", values = [var.project_name] },
          { key = "Environment", values = [var.environment] }
        ]
      }
      cost_allocation = {
        tag_filters = [
          { key = "CostCenter", values = [var.cost_center] },
          { key = "Project", values = [var.project_name] }
        ]
      }
    }
  })

  description = "Resource inventory for ${var.project_name} in ${var.environment} environment"

  tags = merge(local.common_tags, {
    Name        = "${var.project_name}-${var.environment}-inventory"
    Purpose     = "resource-inventory"
    ResourceType = "configuration"
  })
}

# ==============================================================================
# OUTPUTS FOR RESOURCE DISCOVERY
# ==============================================================================

# Output resource group ARNs for external tools
output "resource_discovery" {
  description = "Resource discovery information"
  value = var.enable_resource_discovery ? {
    resource_groups = {
      project_resources    = aws_resourcegroups_group.project_resources[0].arn
      environment_resources = aws_resourcegroups_group.environment_resources[0].arn
      cost_allocation      = aws_resourcegroups_group.cost_allocation[0].arn
    }
    
    parameter_store = {
      project_metadata     = aws_ssm_parameter.project_metadata[0].name
      resource_inventory   = aws_ssm_parameter.resource_inventory[0].name
    }
    
    # Useful CLI commands for resource discovery
    cli_commands = {
      list_all_resources = "aws resourcegroupstaggingapi get-resources --tag-filters Key=Project,Values=${var.project_name} Key=Environment,Values=${var.environment}"
      get_project_metadata = "aws ssm get-parameter --name '/${var.project_name}/${var.environment}/metadata' --query 'Parameter.Value' --output text | jq ."
      get_resource_inventory = "aws ssm get-parameter --name '/${var.project_name}/${var.environment}/resource-inventory' --query 'Parameter.Value' --output text | jq ."
    }
  } : {}
}

# Output standardized tags for use in other modules
output "standardized_tags" {
  description = "Standardized tags for use across all modules"
  value = {
    common_tags          = local.common_tags
    compute_tags         = local.compute_tags
    storage_tags         = local.storage_tags
    network_tags         = local.network_tags
    monitoring_tags      = local.monitoring_tags
  }
}

# Output naming conventions for consistency
output "naming_conventions" {
  description = "Standardized naming conventions for all resources"
  value = local.naming
} 