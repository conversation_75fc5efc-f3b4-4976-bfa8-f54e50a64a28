package main

import (
	"bytes"
	"encoding/json"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"gopkg.in/yaml.v3"

	awsgo "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
)

// StringOrSlice handles both string and []string formats in YAML
type StringOrSlice []string

func (s *StringOrSlice) UnmarshalYAML(value *yaml.Node) error {
	if value.Kind == yaml.SequenceNode {
		// Handle array format
		var slice []string
		if err := value.Decode(&slice); err != nil {
			return err
		}
		*s = StringOrSlice(slice)
	} else {
		// Handle string format (comma-separated)
		var str string
		if err := value.Decode(&str); err != nil {
			return err
		}
		if str == "" {
			*s = StringOrSlice{}
		} else {
			parts := strings.Split(str, ",")
			for i, part := range parts {
				parts[i] = strings.TrimSpace(part)
			}
			*s = StringOrSlice(parts)
		}
	}

	return nil
}

// Simplified metadata structure from Nuclei templates
type NucleiTemplateInfo struct {
	Metadata    map[string]interface{} `yaml:"metadata"`
	ID          string                 `yaml:"id"`
	Name        string                 `yaml:"name"`
	Severity    string                 `yaml:"severity"`
	Description string                 `yaml:"description"`
	Author      StringOrSlice          `yaml:"author"`
	Tags        StringOrSlice          `yaml:"tags"`
	Reference   StringOrSlice          `yaml:"reference"`
}

type NucleiTemplate struct {
	Info NucleiTemplateInfo `yaml:"info"`
}

// TemplateMetadata represents a processed template with metadata
type TemplateMetadata struct {
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Path        string                 `json:"path"`
	Description string                 `json:"description"`
	Severity    string                 `json:"severity"`
	Author      []string               `json:"author"`
	Tags        []string               `json:"tags"`
	Reference   []string               `json:"reference,omitempty"`
	Size        int64                  `json:"size"`
}

// Structures for the final JSON output - matching nuclear_pond types
type Template struct {
	Metadata    map[string]interface{} `json:"metadata"`
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Severity    string                 `json:"severity"`
	Author      []string               `json:"author"`
	Tags        []string               `json:"tags"`
	Reference   []string               `json:"reference"`
}

type TemplateSummary struct {
	BySeverity map[string]int `json:"by_severity"`
	Total      int            `json:"total"`
}

type TemplateIndex struct {
	Summary     TemplateSummary              `json:"summary"`
	Templates   map[string]*TemplateMetadata `json:"templates"`
	Version     string                       `json:"version"`
	GeneratedAt string                       `json:"generated_at"`
}

// Creates a new AWS session, supporting LocalStack for local development.
func createAWSSession() *session.Session {
	region := os.Getenv("AWS_REGION")
	if region == "" {
		region = "us-east-1"
	}
	awsConfig := awsgo.Config{
		Region: awsgo.String(region),
	}

	if os.Getenv("LOCAL_DEVELOPMENT") == "true" {
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		if endpoint == "" {
			endpoint = "http://localhost:4566"
		}
		awsConfig.Endpoint = awsgo.String(endpoint)
		awsConfig.S3ForcePathStyle = awsgo.Bool(true)
		log.Printf("Using LocalStack endpoint: %s", endpoint)
	}

	sess, err := session.NewSession(&awsConfig)
	if err != nil {
		log.Fatalf("Failed to create AWS session: %v", err)
	}

	return sess
}

func main() {
	log.Println("Starting template metadata generation...")

	sess := createAWSSession()
	uploader := s3manager.NewUploader(sess)

	bucket := os.Getenv("NUCLEI_TEMPLATES_BUCKET")
	if bucket == "" {
		log.Fatal("NUCLEI_TEMPLATES_BUCKET environment variable not set")
	}

	// Use local templates directory instead of S3
	templatesDir := os.Getenv("TEMPLATES_SOURCE_DIR")
	if templatesDir == "" {
		templatesDir = "../../backend/templates"
	}

	// Make path absolute
	if !filepath.IsAbs(templatesDir) {
		wd, _ := os.Getwd()
		templatesDir = filepath.Join(wd, templatesDir)
	}

	log.Printf("Reading templates from local directory: %s", templatesDir)

	// 1. Find all template files locally
	var templateFiles []string
	err := filepath.Walk(templatesDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if strings.HasSuffix(path, ".yaml") || strings.HasSuffix(path, ".yml") {
			templateFiles = append(templateFiles, path)
		}

		return nil
	})
	if err != nil {
		log.Fatalf("Failed to scan templates directory: %v", err)
	}
	log.Printf("Found %d templates to process.", len(templateFiles))

	// 2. Parse templates in parallel
	var wg sync.WaitGroup
	var mu sync.Mutex
	templates := make(map[string]*TemplateMetadata)
	severityCounts := make(map[string]int)

	concurrency := 20
	sem := make(chan struct{}, concurrency)
	for _, templateFile := range templateFiles {
		wg.Add(1)
		sem <- struct{}{}
		go func(filePath string) {
			defer wg.Done()
			defer func() { <-sem }()

			log.Printf("Processing %s", filePath)

			data, err := os.ReadFile(filePath)
			if err != nil {
				log.Printf("ERROR: Failed to read %s: %v", filePath, err)

				return
			}

			var template NucleiTemplate
			if err := yaml.Unmarshal(data, &template); err != nil {
				log.Printf("ERROR: Failed to parse YAML for %s: %v", filePath, err)

				return
			}

			// Generate relative path for template
			relPath, err := filepath.Rel(templatesDir, filePath)
			if err != nil {
				log.Printf("ERROR: Failed to get relative path for %s: %v", filePath, err)

				return
			}

			// Use the filename (without extension) as the ID
			id := strings.TrimSuffix(filepath.Base(filePath), filepath.Ext(filePath))

			// Fallback for info fields
			if template.Info.ID == "" {
				template.Info.ID = id
			}
			if template.Info.Name == "" {
				template.Info.Name = id
			}
			if template.Info.Severity == "" {
				template.Info.Severity = "info"
			}

			// Get file size
			fileInfo, _ := os.Stat(filePath)
			fileSize := int64(0)
			if fileInfo != nil {
				fileSize = fileInfo.Size()
			}

			mu.Lock()
			templates[id] = &TemplateMetadata{
				ID:          template.Info.ID,
				Name:        template.Info.Name,
				Path:        "templates/" + relPath, // S3-style path for consistency
				Author:      []string(template.Info.Author),
				Severity:    template.Info.Severity,
				Description: template.Info.Description,
				Tags:        []string(template.Info.Tags),
				Reference:   []string(template.Info.Reference),
				Metadata:    template.Info.Metadata,
				Size:        fileSize,
			}
			severityCounts[template.Info.Severity]++
			mu.Unlock()
		}(templateFile)
	}
	wg.Wait()

	// 3. Assemble the final index
	templateIndex := &TemplateIndex{
		Version:     "1.0.0",
		GeneratedAt: time.Now().UTC().Format(time.RFC3339),
		Templates:   templates,
	}
	templateIndex.Summary.Total = len(templates)
	templateIndex.Summary.BySeverity = severityCounts

	// 4. Marshal to JSON
	jsonData, err := json.MarshalIndent(templateIndex, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal template index to JSON: %v", err)
	}

	// 5. Upload to S3
	outputKey := "templates.json"
	log.Printf("Uploading metadata to s3://%s/%s", bucket, outputKey)
	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: awsgo.String(bucket),
		Key:    awsgo.String(outputKey),
		Body:   bytes.NewReader(jsonData),
	})
	if err != nil {
		log.Fatalf("Failed to upload templates.json: %v", err)
	}

	log.Println("✅ Template metadata generation complete.")
	log.Printf("Summary: %d templates, %v", templateIndex.Summary.Total, templateIndex.Summary.BySeverity)
}
