package configs

import (
	"os"
	"testing"
)

func TestLoadConfig(t *testing.T) {
	// Test with valid environment variables
	os.Setenv("API_ENDPOINT", "http://test-api.com")
	os.Setenv("WORKER_COUNT", "15")
	os.Setenv("TASK_CHANNEL_SIZE", "200")
	os.Setenv("RESULTS_DIR", "test-results")

	config := LoadConfig()

	if config.APIEndpoint != "http://test-api.com" {
		t.Errorf("Expected API_ENDPOINT to be 'http://test-api.com', got '%s'", config.APIEndpoint)
	}

	if config.WorkerCount != 15 {
		t.<PERSON><PERSON>("Expected WORKER_COUNT to be 15, got %d", config.WorkerCount)
	}

	if config.TaskChannelSize != 200 {
		t.Errorf("Expected TASK_CHANNEL_SIZE to be 200, got %d", config.TaskChannelSize)
	}

	if config.ResultsDir != "test-results" {
		t.<PERSON><PERSON><PERSON>("Expected RESULTS_DIR to be 'test-results', got '%s'", config.ResultsDir)
	}

	// Clean up
	os.Unsetenv("API_ENDPOINT")
	os.Unsetenv("WORKER_COUNT")
	os.Unsetenv("TASK_CHANNEL_SIZE")
	os.Unsetenv("RESULTS_DIR")
}

func TestLoadConfigWithDefaults(t *testing.T) {
	// Test with only required environment variable
	os.Setenv("API_ENDPOINT", "http://default-api.com")

	config := LoadConfig()

	if config.APIEndpoint != "http://default-api.com" {
		t.Errorf("Expected API_ENDPOINT to be 'http://default-api.com', got '%s'", config.APIEndpoint)
	}

	if config.WorkerCount != 10 {
		t.Errorf("Expected default WORKER_COUNT to be 10, got %d", config.WorkerCount)
	}

	if config.TaskChannelSize != 100 {
		t.Errorf("Expected default TASK_CHANNEL_SIZE to be 100, got %d", config.TaskChannelSize)
	}

	if config.ResultsDir != "results" {
		t.Errorf("Expected default RESULTS_DIR to be 'results', got '%s'", config.ResultsDir)
	}

	// Clean up
	os.Unsetenv("API_ENDPOINT")
}

func TestLoadConfigWithInvalidValues(t *testing.T) {
	// Test with invalid integer values
	os.Setenv("API_ENDPOINT", "http://test-api.com")
	os.Setenv("WORKER_COUNT", "invalid")
	os.Setenv("TASK_CHANNEL_SIZE", "not-a-number")

	config := LoadConfig()

	if config.WorkerCount != 10 {
		t.Errorf("Expected default WORKER_COUNT to be 10 when invalid, got %d", config.WorkerCount)
	}

	if config.TaskChannelSize != 100 {
		t.Errorf("Expected default TASK_CHANNEL_SIZE to be 100 when invalid, got %d", config.TaskChannelSize)
	}

	// Clean up
	os.Unsetenv("API_ENDPOINT")
	os.Unsetenv("WORKER_COUNT")
	os.Unsetenv("TASK_CHANNEL_SIZE")
}

func TestGetEnvOrDefault(t *testing.T) {
	tests := []struct {
		name       string
		key        string
		defaultVal string
		envValue   string
		expected   string
		shouldSet  bool
	}{
		{
			name:       "Environment variable set",
			key:        "TEST_KEY",
			defaultVal: "default",
			envValue:   "custom",
			expected:   "custom",
			shouldSet:  true,
		},
		{
			name:       "Environment variable not set",
			key:        "TEST_KEY",
			defaultVal: "default",
			envValue:   "",
			expected:   "default",
			shouldSet:  false,
		},
		{
			name:       "Empty environment variable",
			key:        "TEST_KEY",
			defaultVal: "default",
			envValue:   "",
			expected:   "default",
			shouldSet:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldSet {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			result := getEnvOrDefault(tt.key, tt.defaultVal)
			if result != tt.expected {
				t.Errorf("getEnvOrDefault(%s, %s) = %s, want %s", tt.key, tt.defaultVal, result, tt.expected)
			}
		})
	}
}

func TestGetEnvAsInt(t *testing.T) {
	tests := []struct {
		name       string
		key        string
		defaultVal int
		envValue   string
		expected   int
		shouldSet  bool
	}{
		{
			name:       "Valid integer",
			key:        "TEST_INT",
			defaultVal: 10,
			envValue:   "25",
			expected:   25,
			shouldSet:  true,
		},
		{
			name:       "Invalid integer",
			key:        "TEST_INT",
			defaultVal: 10,
			envValue:   "not-a-number",
			expected:   10,
			shouldSet:  true,
		},
		{
			name:       "Environment variable not set",
			key:        "TEST_INT",
			defaultVal: 10,
			envValue:   "",
			expected:   10,
			shouldSet:  false,
		},
		{
			name:       "Zero value",
			key:        "TEST_INT",
			defaultVal: 10,
			envValue:   "0",
			expected:   0,
			shouldSet:  true,
		},
		{
			name:       "Negative value",
			key:        "TEST_INT",
			defaultVal: 10,
			envValue:   "-5",
			expected:   -5,
			shouldSet:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldSet {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			result := getEnvAsInt(tt.key, tt.defaultVal)
			if result != tt.expected {
				t.Errorf("getEnvAsInt(%s, %d) = %d, want %d", tt.key, tt.defaultVal, result, tt.expected)
			}
		})
	}
}

func TestLoadConfigMissingAPIEndpoint(t *testing.T) {
	// This test will cause a fatal error, so we need to handle it
	// We'll use a defer to recover from the fatal
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected LoadConfig to panic when API_ENDPOINT is not set")
		}
	}()

	// Ensure API_ENDPOINT is not set
	os.Unsetenv("API_ENDPOINT")

	LoadConfig()
}
