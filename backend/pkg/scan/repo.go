package scan

import (
	"fmt"
	"log"
	"sort"
	"strconv"
	"sync"
	"time"

	"nuclear_pond/pkg/aws"
	"nuclear_pond/pkg/types"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/dynamodb"
)

var (
	ddbSvcCached    *dynamodb.DynamoDB
	awsConfigCached *aws.AWSConfig
	ddbInitOnce     sync.Once
)

func getDDB() (*dynamodb.DynamoDB, *aws.AWSConfig, error) {
	var initErr error
	ddbInitOnce.Do(func() {
		awsConfigCached = aws.NewAWSConfig()
		sess, err := awsConfigCached.CreateSession()
		if err != nil {
			initErr = err

			return
		}
		ddbSvcCached = dynamodb.New(sess)
	})
	if initErr != nil {
		return nil, nil, initErr
	}

	return ddbSvcCached, awsConfigCached, nil
}

// updateScanStatus updates the status and timestamps for a scan.
func updateScanStatus(requestId, status string) error {
	svc, awsConfig, err := getDDB()
	if err != nil {
		return err
	}

	updateInput := &dynamodb.UpdateItemInput{
		TableName: awssdk.String(awsConfig.DynamoDBTable),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {S: awssdk.String(requestId)},
		},
		UpdateExpression: awssdk.String("SET #status = :status, updated_at = :updated_at"),
		ExpressionAttributeNames: map[string]*string{
			"#status": awssdk.String("status"),
		},
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":status":     {S: awssdk.String(status)},
			":updated_at": {S: awssdk.String(time.Now().Format(time.RFC3339))},
		},
		ConditionExpression: awssdk.String("attribute_exists(scan_id)"),
	}
	if status == "completed" || status == "failed" {
		updateInput.UpdateExpression = awssdk.String("SET #status = :status, updated_at = :updated_at, completed_at = :completed_at")
		updateInput.ExpressionAttributeValues[":completed_at"] = &dynamodb.AttributeValue{S: awssdk.String(time.Now().Format(time.RFC3339))}
	}

	if _, err := svc.UpdateItem(updateInput); err != nil {
		if awsConfig.IsLocal {
			// Retry without condition for local dev convenience
			updateInput.ConditionExpression = nil
			if _, err2 := svc.UpdateItem(updateInput); err2 != nil {
				return err2
			}

			return nil
		}

		return err
	}

	return nil
}

// GetAllScans retrieves scans with optional status filter and limit.
func GetAllScans(limit int, exclusiveStartKey map[string]*dynamodb.AttributeValue, statusFilter string) (*types.ScanListResponse, error) {
	svc, awsConfig, err := getDDB()
	if err != nil {
		return nil, err
	}

	input := &dynamodb.ScanInput{
		TableName: awssdk.String(awsConfig.DynamoDBTable),
		Limit:     awssdk.Int64(int64(limit)),
	}
	if exclusiveStartKey != nil {
		input.ExclusiveStartKey = exclusiveStartKey
	}
	if statusFilter != "" && statusFilter != "all" {
		input.FilterExpression = awssdk.String("#status = :status")
		input.ExpressionAttributeNames = map[string]*string{"#status": awssdk.String("status")}
		input.ExpressionAttributeValues = map[string]*dynamodb.AttributeValue{":status": {S: awssdk.String(statusFilter)}}
	}

	result, err := svc.Scan(input)
	if err != nil {
		if awsConfig.IsLocal {
			log.Printf("Local DynamoDB scan failed: %v", err)

			return &types.ScanListResponse{Scans: []types.ScanRequest{}, Total: 0, Limit: limit, HasMore: false}, nil
		}

		return nil, err
	}

	scans := make([]types.ScanRequest, 0, len(result.Items))
	for _, item := range result.Items {
		scan, parseErr := parseScanFromDynamoDB(item)
		if parseErr != nil {
			log.Printf("Failed to parse scan record: %v", parseErr)

			continue
		}
		_ = validateScanRecord(scan) // validation only logs if something odd
		scans = append(scans, *scan)
	}

	sort.Slice(scans, func(i, j int) bool { return scans[i].CreatedAt.After(scans[j].CreatedAt) })

	return &types.ScanListResponse{
		Scans:   scans,
		Total:   len(scans),
		Limit:   limit,
		HasMore: result.LastEvaluatedKey != nil,
	}, nil
}

// GetScanByID retrieves a single scan by ID (scan_id is dashless request ID).
func GetScanByID(scanId string) (*types.ScanRequest, error) {
	svc, awsConfig, err := getDDB()
	if err != nil {
		return nil, err
	}

	result, err := svc.GetItem(&dynamodb.GetItemInput{
		TableName: awssdk.String(awsConfig.DynamoDBTable),
		Key:       map[string]*dynamodb.AttributeValue{"scan_id": {S: awssdk.String(scanId)}},
	})
	if err != nil {
		return nil, err
	}
	if result.Item == nil {
		return nil, fmt.Errorf("scan not found")
	}

	return parseScanFromDynamoDB(result.Item)
}

// parseScanFromDynamoDB parses a DynamoDB item into a ScanRequest struct.
func parseScanFromDynamoDB(item map[string]*dynamodb.AttributeValue) (*types.ScanRequest, error) {
	scan := &types.ScanRequest{Status: "unknown", CreatedAt: time.Now(), UpdatedAt: time.Now()}

	if val, ok := item["scan_id"]; ok && val.S != nil {
		scan.ScanID = *val.S
	}
	if val, ok := item["request_id"]; ok && val.S != nil {
		scan.RequestID = *val.S
	}
	if val, ok := item["status"]; ok && val.S != nil {
		scan.Status = *val.S
	}
	if val, ok := item["target_count"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.TargetCount = n
		}
	}
	if val, ok := item["targets_s3_url"]; ok && val.S != nil {
		scan.TargetsS3URL = *val.S
	}
	if val, ok := item["mode"]; ok && val.S != nil {
		scan.Mode = *val.S
	}
	if val, ok := item["batches"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.Batches = n
		}
	}
	if val, ok := item["threads"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.Threads = n
		}
	}
	if val, ok := item["created_at"]; ok && val.S != nil {
		if t, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.CreatedAt = t
		}
	}
	if val, ok := item["updated_at"]; ok && val.S != nil {
		if t, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.UpdatedAt = t
		}
	}
	if val, ok := item["completed_at"]; ok && val.S != nil {
		if t, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.CompletedAt = &t
		}
	}
	if val, ok := item["total_vulnerable_targets"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.TotalVulnerableTargets = n
		}
	}
	if val, ok := item["ttl"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.TTL = int64(n)
		}
	}

	if val, ok := item["templates"]; ok && val.L != nil {
		templates := make([]string, 0, len(val.L))
		for _, t := range val.L {
			if t.S != nil {
				templates = append(templates, *t.S)
			}
		}
		scan.Templates = templates
	}

	if val, ok := item["expected_batches"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.ExpectedBatches = n
		}
	}
	if val, ok := item["results_batches_received"]; ok && val.N != nil {
		if n, err := strconv.Atoi(*val.N); err == nil {
			scan.ResultsBatchesReceived = n
		}
	}
	if val, ok := item["results_status"]; ok && val.S != nil {
		scan.ResultsStatus = *val.S
	}
	if val, ok := item["results_updated_at"]; ok && val.S != nil {
		if t, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.ResultsUpdatedAt = t
		}
	}
	if val, ok := item["aggregated_findings_key"]; ok && val.S != nil {
		scan.AggregatedFindingsKey = *val.S
	}
	if val, ok := item["findings_keys"]; ok && val.L != nil {
		keys := make([]string, 0, len(val.L))
		for _, av := range val.L {
			if av.S != nil {
				keys = append(keys, *av.S)
			}
		}
		scan.FindingsKeys = keys
	}

	if val, ok := item["vulnerable_targets"]; ok && val.M != nil {
		vt := make(map[string][]string)
		for target, list := range val.M {
			if list.L != nil {
				arr := make([]string, 0, len(list.L))
				for _, x := range list.L {
					if x.S != nil {
						arr = append(arr, *x.S)
					}
				}
				vt[target] = arr
			}
		}
		scan.VulnerableTargets = vt
	}

	// results_summary
	if val, ok := item["results_summary"]; ok && val.M != nil {
		rs := &types.ResultsSummary{}
		if v, ok := val.M["total_findings"]; ok && v.N != nil {
			if n, err := strconv.Atoi(*v.N); err == nil {
				rs.TotalFindings = n
			}
		}
		if v, ok := val.M["targets_scanned"]; ok && v.N != nil {
			if n, err := strconv.Atoi(*v.N); err == nil {
				rs.TargetsScanned = n
			}
		}
		if v, ok := val.M["vulnerable_targets"]; ok && v.N != nil {
			if n, err := strconv.Atoi(*v.N); err == nil {
				rs.VulnerableTargets = n
			}
		}
		if v, ok := val.M["duration_seconds"]; ok && v.N != nil {
			if n, err := strconv.Atoi(*v.N); err == nil {
				rs.DurationSeconds = int64(n)
			}
		}
		scan.ResultsSummary = rs
	}

	return scan, nil
}

// storeScanRequest inserts a new scan record in DynamoDB.
func storeScanRequest(sr *types.ScanRequest) error {
	svc, awsConfig, err := getDDB()
	if err != nil {
		return fmt.Errorf("aws session: %w", err)
	}

	item := map[string]*dynamodb.AttributeValue{
		"scan_id":        {S: awssdk.String(sr.ScanID)},
		"request_id":     {S: awssdk.String(sr.RequestID)},
		"status":         {S: awssdk.String(sr.Status)},
		"target_count":   {N: awssdk.String(fmt.Sprintf("%d", sr.TargetCount))},
		"targets_s3_url": {S: awssdk.String(sr.TargetsS3URL)},
		"mode":           {S: awssdk.String(sr.Mode)},
		"batches":        {N: awssdk.String(fmt.Sprintf("%d", sr.Batches))},
		"threads":        {N: awssdk.String(fmt.Sprintf("%d", sr.Threads))},
		"created_at":     {S: awssdk.String(sr.CreatedAt.Format(time.RFC3339))},
		"updated_at":     {S: awssdk.String(sr.UpdatedAt.Format(time.RFC3339))},
		"ttl":            {N: awssdk.String(fmt.Sprintf("%d", sr.TTL))},
	}
	if sr.CompletedAt != nil {
		item["completed_at"] = &dynamodb.AttributeValue{S: awssdk.String(sr.CompletedAt.Format(time.RFC3339))}
	}
	// templates list
	if len(sr.Templates) > 0 {
		list := make([]*dynamodb.AttributeValue, 0, len(sr.Templates))
		for _, t := range sr.Templates {
			list = append(list, &dynamodb.AttributeValue{S: awssdk.String(t)})
		}
		item["templates"] = &dynamodb.AttributeValue{L: list}
	} else {
		item["templates"] = &dynamodb.AttributeValue{L: []*dynamodb.AttributeValue{}}
	}

	put := &dynamodb.PutItemInput{
		TableName:           awssdk.String(awsConfig.DynamoDBTable),
		Item:                item,
		ConditionExpression: awssdk.String("attribute_not_exists(scan_id)"),
	}
	if _, err := svc.PutItem(put); err != nil {
		if awsConfig.IsLocal {
			put.ConditionExpression = nil
			if _, err2 := svc.PutItem(put); err2 != nil {
				return fmt.Errorf("local put: %w", err2)
			}

			return nil
		}

		return fmt.Errorf("ddb put: %w", err)
	}

	return nil
}

// validateScanRecord does sanity checks and logs warnings; returns validity.
func validateScanRecord(scan *types.ScanRequest) bool {
	isValid := true
	if scan.ScanID == "" || scan.RequestID == "" {
		isValid = false
	}
	if scan.TargetCount == 0 && scan.TargetsS3URL == "" {
		isValid = false
	}
	if scan.Batches == 0 || scan.Threads == 0 {
		isValid = false
	}
	if !isValid {
		log.Printf("Incomplete scan record %s (status=%s targets=%d batches=%d threads=%d)", scan.ScanID, scan.Status, scan.TargetCount, scan.Batches, scan.Threads)
	}

	return isValid
}
