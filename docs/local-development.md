# FastScan Local Development Guide


## Quick Start

```bash
# Start everything with a single command
devbox run local:start-full

# Access services at:
# - Frontend: http://localhost:5175
# - Backend API: http://localhost:8082
# - LocalStack: http://localhost:4566
```

## Prerequisites

- <PERSON>er and Docker Compose
- Go 1.22+
- Node.js 22+
- Yarn
- Devbox (https://www.jetify.com/docs/devbox/installing_devbox/)

## Components

The local development environment consists of:

1. **LocalStack** - Emulates AWS services (S3, DynamoDB, Lambda)
2. **Nuclear Pond** - Go backend service
3. **Lambda Nuclei Scanner** - Serverless function for vulnerability scanning
4. **Frontend** - React web application

## Setup Options

### Option 1: Full Environment (Recommended)

```bash
# Start everything (LocalStack, backend, frontend)
devbox run local:start-full
```

### Option 2: Individual Components

```bash
# 1. Start LocalStack and set up resources
devbox run local:start

# 2. Start the Nuclear Pond backend
devbox run dev:backend

# 3. Start the frontend
devbox run dev:frontend
```

### Option 3: Reset Environment

```bash
# Reset LocalStack data and restart
devbox run local:reset
```

## Development Workflows

### Workflow 1: Frontend Development

```bash
# Start backend services
devbox run local:start
devbox run dev:backend

# Start frontend with hot reloading
devbox run dev:frontend

# Make changes to frontend code and see immediate updates
```

### Workflow 2: Backend Development

```bash
# Start LocalStack
devbox run local:start

# Run backend with hot reloading
cd backend
devbox run -- go run main.go server

# Make API requests to http://localhost:8082
```

### Workflow 3: Lambda Function Development

```bash
# Start LocalStack
devbox run local:start

# Make changes to Lambda code
# Deploy changes to LocalStack
devbox run local:deploy-lambda

# Or use watch mode for automatic redeployment
devbox run local:watch-lambda
```

### Workflow 4: End-to-End Testing

```bash
# Start full environment
devbox run local:start-full

# Verify scan workflow functionality
devbox run local:verify
```

## Environment Configuration

Local development uses `.env.local` files in each component directory:

- `backend/.env.local` - Backend configuration
- `lambda-nuclei-scanner/.env.local` - Lambda function configuration
- `frontend/.env.local` - Frontend configuration

To load these environment variables in your shell:

```bash
devbox run local:env
```

## Debugging Tools

### LocalStack Health Check

```bash
# Check LocalStack services status
devbox run local:status
```

### S3 Bucket Inspection

```bash
# Install awslocal CLI tool (first time only)
devbox run debug:install-awslocal

# List all buckets
devbox run debug:s3 list

# List contents of a bucket
devbox run debug:s3 ls fastscan-nuclei-artifacts-local-us-east-1

# Download an object
devbox run debug:s3 download fastscan-nuclei-artifacts-local-us-east-1 path/to/object output-file.json
```

### DynamoDB Table Inspection

```bash
# List all tables
devbox run debug:dynamodb list

# Scan a table (with optional limit)
devbox run debug:dynamodb scan fastscan-local-server-state 20

# Get an item by primary key
devbox run debug:dynamodb get fastscan-local-server-state scan_id test-scan-123
```

## Common Troubleshooting

### Issue: LocalStack not starting

**Symptoms:**
- Docker container fails to start
- "Connection refused" errors when accessing LocalStack

**Solutions:**
1. Check Docker service is running:
   ```bash
   docker ps
   ```

2. Fix Docker socket permissions:
   ```bash
   sudo chmod 666 /var/run/docker.sock
   ```

3. Check for port conflicts:
   ```bash
   lsof -i :4566
   ```

### Issue: Nuclear Pond can't connect to LocalStack

**Symptoms:**
- Backend logs show connection errors
- API calls fail with AWS service errors

**Solutions:**
1. Ensure environment variables are set correctly:
   ```bash
   devbox run local:env
   ```

2. Verify LocalStack is healthy:
   ```bash
   devbox run local:status
   ```

3. Check AWS endpoint configuration in `.env.local` files

### Issue: Lambda function not working

**Symptoms:**
- Scans get stuck in QUEUED state
- Lambda invocation errors in logs

**Solutions:**
1. Redeploy the Lambda function:
   ```bash
   devbox run local:deploy-lambda
   ```

2. Check Lambda logs:
   ```bash
   awslocal logs describe-log-groups
   awslocal logs get-log-events --log-group-name /aws/lambda/fastscan-local-nuclei-function
   ```

3. Verify Lambda configuration in `.env.local` files

### Issue: Frontend can't connect to backend

**Symptoms:**
- Network errors in browser console
- API calls fail with connection refused

**Solutions:**
1. Ensure backend is running:
   ```bash
   curl http://localhost:8082/health
   ```

2. Check CORS configuration in backend
3. Verify API URL in frontend environment variables

## Available Commands

| Command | Description |
|---------|-------------|
| `devbox run local:start-full` | Start complete environment |
| `devbox run local:start` | Start LocalStack only |
| `devbox run local:stop` | Stop all services |
| `devbox run local:reset` | Reset and restart environment |
| `devbox run local:status` | Check LocalStack health |
| `devbox run local:verify` | Verify scan workflow functionality |
| `devbox run local:deploy-lambda` | Deploy Lambda function to LocalStack |
| `devbox run local:watch-lambda` | Watch and auto-deploy Lambda changes |
| `devbox run local:env` | Load local environment variables |
| `devbox run dev:backend` | Start Nuclear Pond backend |
| `devbox run dev:frontend` | Start frontend development server |
| `devbox run debug:s3` | S3 bucket inspection tool |
| `devbox run debug:dynamodb` | DynamoDB table inspection tool |