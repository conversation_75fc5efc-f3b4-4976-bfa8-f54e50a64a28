{"name": "fastscan-frontend", "private": true, "version": "0.0.1", "packageManager": "yarn@4.9.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode dev", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode prod", "lint": "biome check .", "lint:fix": "biome format --write . && biome lint --apply-unsafe .", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "typecheck": "tsc --noEmit", "deploy:dev": "cd ../terraform && ./frontend/deploy-frontend-dev.sh", "deploy:prod": "cd ../terraform && ./frontend/deploy-frontend-prod.sh"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-router": "^1.121.27", "@tanstack/router-plugin": "^1.130.15", "@types/react-window": "^1.8.8", "ahooks": "^3.9.0", "alova": "^3.3.4", "arktype": "^2.1.20", "clsx": "^2.1.1", "daisyui": "^5.0.50", "date-fns": "^4.1.0", "jotai": "^2.12.5", "lucide-react": "^0.522.0", "motion": "^12.23.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-window": "^1.8.11", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "theme-change": "^2.5.0"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "@tanstack/router-devtools": "^1.121.27", "@tanstack/router-vite-plugin": "^1.121.29", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "babel-plugin-react-compiler": "^19.1.0-rc.2", "jsdom": "^26.0.0", "postcss": "^8.4.47", "tailwindcss": "^4.1.10", "typescript": "^5.9.2", "vite": "^6.3.5", "vitest": "^3.2.4"}}