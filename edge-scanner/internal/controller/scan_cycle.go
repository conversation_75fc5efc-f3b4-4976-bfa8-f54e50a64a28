package controller

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/RootEvidence/edge-scanner/internal/apiclient"
	"github.com/RootEvidence/edge-scanner/internal/configs"
	"github.com/RootEvidence/edge-scanner/internal/models"
	"github.com/RootEvidence/edge-scanner/internal/scanner"
)

// fetchAndProcessTargets orchestrates the fetch → scan → save flow.
func FetchAndProcessTargets(cfg *configs.Config, apiURL string) {
	log.Println("[INFO] Fetching targets from API...")

	if err := os.MkdirAll(cfg.ResultsDir, 0755); err != nil {
		log.Fatalf("Failed to create results directory: %v", err)
	}

	job, err := apiclient.FetchTargets(apiURL)
	if err != nil {
		log.Printf("[ERROR] Failed to fetch targets: %v", err)
		return
	}

	taskChan := make(chan scanner.ScanTask, cfg.TaskChannelSize)
	saveJobs := make(chan string, cfg.TaskChannelSize)

	var scanWG sync.WaitGroup
	var saveWG sync.WaitGroup

	runScanWorkers(cfg, taskChan, saveJobs, &scanWG)
	StartSaveWorkers(cfg, saveJobs, &saveWG)

	DispatchScanTasks(job, taskChan)
	close(taskChan) // No more scan tasks

	scanWG.Wait()   // Wait for scanning to finish
	close(saveJobs) // No more save jobs
	saveWG.Wait()   // Wait for saving to finish

	log.Println("[INFO] All scan and save tasks completed.")
}

// runScanWorkers launches concurrent scanning goroutines.
func runScanWorkers(cfg *configs.Config, taskChan <-chan scanner.ScanTask, saveJobs chan<- string, wg *sync.WaitGroup) {
	for i := 0; i < cfg.WorkerCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			scanner.Worker(id, cfg.ResultsDir, taskChan, wg, saveJobs)
		}(i)
	}
}

// startSaveWorkers launches concurrent saving goroutines.
func StartSaveWorkers(cfg *configs.Config, saveJobs <-chan string, wg *sync.WaitGroup) {
	for i := 0; i < cfg.WorkerCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			SaveWorker(id, cfg, saveJobs)
		}(i)
	}
}

// saveWorker processes files and uploads them to the API.
func SaveWorker(id int, cfg *configs.Config, saveJobs <-chan string) {
	for fileName := range saveJobs {
		filePath := filepath.Join(cfg.ResultsDir, fileName)
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			log.Printf("[ERROR] Failed to stat file %s: %v", filePath, err)
			continue
		}

		if fileInfo.Size() == 0 {
			if err := os.Remove(filePath); err != nil {
				log.Printf("[ERROR] Failed to delete empty file %s: %v", filePath, err)
			} else {
				log.Printf("[INFO] Deleted empty file: %s", fileName)
			}
			continue
		}

		data, err := os.ReadFile(filePath)
		if err != nil {
			log.Printf("[ERROR] Failed to read file %s: %v", filePath, err)
			continue
		}

		var result models.IPJobResponse
		if err := json.Unmarshal(data, &result); err != nil {
			log.Printf("[ERROR] Failed to parse JSON from file %s: %v", filePath, err)
			continue
		}

		start := time.Now()
		jobId := result.JobId
		batchId := result.BatchId
		productId := "edge_scan"
		saveAPIurl := fmt.Sprintf("%s/job/%d/batch/%d/product/%s/result",
			cfg.APIEndpoint,
			jobId,
			batchId,
			productId,
		)
		resp, err := apiclient.SaveTargets(saveAPIurl, data, fileName)
		if err != nil {
			log.Printf("[ERROR] Failed to save file %s: %v", fileName, err)
			log.Printf("SAVE API URL: %s", saveAPIurl)
			continue
		}
		log.Printf("[INFO] File %s saved in %v | Response: %s", fileName, time.Since(start), resp)

		if err := os.Remove(filePath); err != nil {
			log.Printf("[ERROR] Failed to delete file %s: %v", filePath, err)
		} else {
			log.Printf("[INFO] File %s deleted after save.", fileName)
		}
	}
}

// dispatchScanTasks sends all scan tasks into the channel.
func DispatchScanTasks(job *models.IPJobResponse, taskChan chan<- scanner.ScanTask) {
	ipCount := 0
	for _, target := range job.Targets {
		ip, err := apiclient.HexToIPv4(target.Addr)
		if err != nil {
			log.Printf("[WARN] Skipping invalid IP hex %s: %v", target.Addr, err)
			continue
		}
		ipCount++
		// for _, port := range target.Ports {
		for _, probe := range job.Probes {
			taskChan <- scanner.ScanTask{
				IP: ip,
				// Port:       int(port),
				TemplateID: probe.TemplateId,
			}
		}
		// }
	}
	log.Printf("[INFO] Total valid IPs processed: %d", ipCount)
}
