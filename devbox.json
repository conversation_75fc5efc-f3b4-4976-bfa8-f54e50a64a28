{"packages": {"go": "1.22", "gopls": "latest", "nodejs": "22", "terraform": "latest", "awscli2": "latest", "python": "latest", "uv": "latest", "git": "latest", "gh": "latest", "jq": "latest", "fzf": "latest", "delta": "latest", "zsh": "latest", "yarn": "latest", "curl": "latest", "wget": "latest", "unzip": "latest", "zip": "latest", "gnupg": "latest", "openssh": "latest", "docker": "latest", "docker-compose": "latest", "bash-language-server": "latest", "air": "latest", "golangci-lint": "latest", "goimports": "latest", "gofumpt": "latest"}, "env": {"NODE_OPTIONS": "--max-old-space-size=4096", "SHELL": "/bin/zsh", "DEVBOX_COREPACK_ENABLED": "true", "PATH": "$HOME/.local/bin:$HOME/go/bin:$PATH"}, "shell": {"init_hook": ["# FastScan Development Environment", "echo '🚀 FastScan Devbox Environment Active'", "echo '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'", "echo '📦 Package Versions:'", "echo '   Go:        $(go version | cut -d' ' -f3)'", "echo '   Node.js:   $(node --version)'", "echo '   Yarn:      $(yarn --version)'", "echo '   Terraform: $(terraform --version | head -n1 | cut -d' ' -f2)'", "echo '   AWS CLI:   $(aws --version | cut -d' ' -f1 | cut -d'/' -f2)'", "echo '   Python:    $(python --version | cut -d' ' -f2)'", "echo '   golangci-lint: '$(golangci-lint --version 2>/dev/null | awk '{print $4}')", "echo '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'", "echo '🛠️  Available Commands:'", "echo '   devbox run dev:*           - Start development servers'", "echo '   devbox run local:*         - Local development commands'", "echo '   devbox run local:verify    - Verify scan workflow functionality'", "echo '   devbox run test-targets:*  - Manage test target containers'", "echo '   devbox run debug:*         - Debugging and inspection tools'", "echo '   devbox run go:lint         - Run golangci-lint across all Go modules'", "echo '   devbox run go:lint:fix     - Auto-fix simple issues using golangci-lint'", "echo '   devbox run edge:*          - Edge Scanner development commands'", "echo '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'", "# Install awslocal if not present", "if ! command -v awslocal &> /dev/null; then", "  echo '🔧 Installing awslocal CLI tool...'", "  uv tool install awscli-local >/dev/null 2>&1 || true", "fi", "# Fix Docker socket permissions if needed", "if [ -S /var/run/docker.sock ] && ! docker info >/dev/null 2>&1; then", "  echo '🐳 Fixing Docker socket permissions...'", "  sudo chmod 666 /var/run/docker.sock 2>/dev/null || true", "fi", "echo ''"], "scripts": {"frontend": "yarn dev:frontend", "backend": "./scripts/tasks/lambda/watch-nuclear-pond.sh", "localstack": "./scripts/bin/start-full-environment.sh", "test-targets": "./scripts/tasks/test-targets/start.sh", "local:deploy-lambda": "./scripts/tasks/lambda/deploy.sh --create-layers", "local:stop": "docker-compose down --remove-orphans", "local:stop-full": "./scripts/bin/stop-full-environment.sh", "local:reset": "./scripts/bin/start-full-environment.sh --reset", "local:status": "./scripts/tasks/verification/check-health.sh", "local:watch-lambda": "./scripts/tasks/lambda/watch.sh", "local:test-lambda": "scripts/tasks/lambda/test.sh", "local:delete-lambda": "scripts/tasks/lambda/delete.sh", "local:env": "source ./scripts/bin/load-local-env.sh", "local:verify": "./scripts/tasks/verification/verify-scan-workflow.sh", "local:verify-metadata": "./scripts/tasks/verification/verify-metadata-generator.sh", "local:metadata": "./scripts/tasks/metadata/generate-template-metadata.sh --local --bucket fastscan-nuclei-artifacts-local-us-east-1", "aws:metadata": "./scripts/tasks/aws/update-template-metadata.sh", "test-targets:stop": "./scripts/tasks/test-targets/stop.sh", "test-targets:status": "./scripts/tasks/test-targets/start.sh --status", "debug:install-awslocal": "./scripts/tasks/aws/install-awslocal.sh", "debug:s3": "./scripts/tasks/aws/inspect-s3.sh", "debug:dynamodb": "./scripts/tasks/aws/inspect-dynamodb.sh", "debug:status": "./scripts/tasks/verification/check-health.sh", "go:lint": "./scripts/tasks/go/lint.sh", "go:lint:fix": "./scripts/tasks/go/lint.sh --fix", "edge:setup": "cd edge-scanner && ./scripts/dev-setup.sh", "edge:dev": "cd edge-scanner && ./scripts/run-dev.sh", "edge:mock-api": "cd edge-scanner && ./scripts/run-mock-api.sh", "edge:single": "cd edge-scanner && ./scripts/run-single.sh", "edge:test": "cd edge-scanner && ./scripts/test-integration.sh", "edge:stop": "cd edge-scanner && docker-compose -f mock-api/docker-compose.mock.yml down"}}}