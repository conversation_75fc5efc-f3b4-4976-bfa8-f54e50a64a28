package main

import (
	"context"
	"main/internal/types"
	"main/pkg/scanner"

	"github.com/aws/aws-lambda-go/lambda"
)

// handler is the AWS Lambda function handler
func handler(ctx context.Context, event types.ScanRequest) (any, error) {
	// Create scanner instance
	sc, err := scanner.NewScanner()
	if err != nil {
		// Return structured error even on init failure
		return types.StructuredBatchResponse{Error: err.Error()}, err
	}

	// Execute scan and return structured response
	structured, _ := sc.Scan(&event)

	return *structured, nil
}

func main() {
	lambda.Start(handler)
}
