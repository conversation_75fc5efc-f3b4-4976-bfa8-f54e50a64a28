# Nuclear Pond Architecture

This document provides an overview of the Nuclear Pond system architecture, its components, and how different execution modes function.

## Core Concepts

Nuclear Pond is designed as a pure orchestrator for [Nuclei](https://github.com/projectdiscovery/nuclei) scans across various environments. The primary goals of its architecture are scalability, flexibility, and cost-effectiveness through a unified API-based approach.


## Component Overview

### 1. Nuclear Pond Server (Pure Orchestrator)

-   **Purpose**: Acts as a pure orchestration layer that delegates all actual scanning work to execution engines.
-   **Commands**:
    -   `nuclearpond run`: Manages cloud-based scans (AWS Lambda).
    -   `nuclearpond service`: Starts the HTTP API server.
-   **Responsibilities**: Parses user input, prepares targets and Nuclei arguments, makes API calls to execution engines, and handles output aggregation.
-   **Activation**: Started by the `nuclearpond service` command.
-   **Functionality**:
    -   Provides HTTP endpoints for scan management (start scan, get status).
    -   Can initiate scans via AWS lambda functions.
    -   **State Management (DynamoDB + S3)**: When the API server is used, it leverages an AWS DynamoDB table (`AWS_DYNAMODB_TABLE`) to store scan metadata and an S3 bucket (`AWS_S3_BUCKET`) for target storage. The database operations follow a clean separation of concerns:
        -   **INSERT**: `storeScanRequest()` creates new scan records when scans are initiated
        -   **UPDATE**: `updateScanStatus()` updates scan status and timestamps during execution
        -   **GET**: `getScanByID()` retrieves individual scan details by ID
        -   **LIST**: `getAllScans()` retrieves all scans with optional filtering and pagination
        -   **PARSE**: `parseScanFromDynamoDB()` handles consistent data transformation from DynamoDB format
-   **Dependencies**:
    -   For `cloud` mode scans via API: Requires AWS credentials and configuration for Lambda invocation, S3 access, and DynamoDB access.
-   **Use Case**: Programmatic integration, automated workflows, CI/CD pipelines, providing a persistent interface to the scanning capabilities.

## Parameter Flow Architecture

### Concurrency Model

Nuclear Pond now uses a single user-facing parameter:

- **Scanners (Concurrency)**: Number of scanner workers (Lambda invocations in parallel)

From this, the server derives internal execution values:

- **Group Size**: `ceil(totalTargets / scanners)` — number of targets assigned to each scanner
- **Threads**: equals `scanners`

### Execution Logic

1. **Target Grouping**: `helpers.SplitSlice()` divides targets into groups of size `group size`
2. **Concurrency Control**: `core.ExecuteScans()` creates a goroutine pool of size `scanners`
3. **Lambda Distribution**: Each batch is sent to a separate Lambda function
4. **Parallel Execution**: Up to `threads` Lambda functions run concurrently

### Example Calculation

For 100 targets with scanners=5:
- Group size per scanner: `ceil(100/5) = 20`
- Total Lambda invocations: `ceil(100/20) = 5` (one per scanner)
- Concurrent Lambdas: `5`
- Execution waves: `1`
