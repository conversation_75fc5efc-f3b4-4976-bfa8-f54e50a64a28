# ==============================================================================
# NUCLEI ARTIFACTS MANAGEMENT
# ==============================================================================
# This file manages the download, packaging, and upload of Nuclei-related
# artifacts including the binary, templates.

# Local variables for this module
locals {
  # S3 object tags (limited to 10 tags per AWS constraint)
  s3_object_tags = {
    Project     = var.project_name
    Environment = var.environment
    Terraform   = "true"
    # Leave 7 slots for resource-specific tags
  }
}

# ==============================================================================
# BUILD ARTIFACTS USING UNIFIED BUILD SCRIPTS
# ==============================================================================

# Build all Lambda artifacts using the unified build system
resource "null_resource" "build_artifacts" {
  triggers = {
    # Rebuild when build scripts change
    build_script_hash = filemd5("${path.module}/../../scripts/tasks/build/build-lambda-artifacts.sh")
    nuclei_version    = var.nuclei_version
    nuclei_arch       = var.nuclei_arch
    # Rebuild when templates change
    templates_hash = join(",", [for f in fileset("${path.module}/../../nuclear_pond/templates", "*.yaml") : filemd5("${path.module}/../../nuclear_pond/templates/${f}")])
    # Rebuild when Go source changes
    go_mod_hash = fileexists("${path.module}/../../lambda-nuclei-scanner/go.mod") ? filemd5("${path.module}/../../lambda-nuclei-scanner/go.mod") : "none"
     # Add metadata generator files to triggers
    metadata_generator_hash = join(",", [
      filemd5("${path.module}/../../scripts/metadata-generator/main.go"),
      filemd5("${path.module}/../../scripts/metadata-generator/go.mod"),
      filemd5("${path.module}/../../scripts/tasks/metadata/generate-template-metadata.sh")
    ])
  }

  provisioner "local-exec" {
    working_dir = "${path.module}/../.."
    command = <<-EOT
      set -e
      echo "Building Lambda artifacts..."
      
      # Build all artifacts
      bash scripts/tasks/build/build-lambda-artifacts.sh
      
      # Generate template metadata
      echo "Generating template metadata..."
      export NUCLEI_TEMPLATES_BUCKET="${aws_s3_bucket.artifacts_bucket.id}"
      export TEMPLATES_SOURCE_DIR="$(pwd)/nuclear_pond/templates"
      cd scripts/metadata-generator
      go build -o metadata-generator .
      ./metadata-generator
      cd ../..
      
      echo "✓ All Lambda artifacts built successfully"
      echo "✓ Template metadata generated and uploaded to S3"
    EOT
  }
}

# Data sources for artifact files (calculated after build)
data "local_file" "nuclei_layer" {
  depends_on = [null_resource.build_artifacts]
  filename   = "${path.module}/../../build/artifacts/nuclei-layer.zip"
}

data "local_file" "templates_layer" {
  depends_on = [null_resource.build_artifacts]
  filename   = "${path.module}/../../build/artifacts/templates-layer.zip"
}

data "local_file" "lambda_function" {
  depends_on = [null_resource.build_artifacts]
  filename   = "${path.module}/../../build/artifacts/lambda-function.zip"
}

# Upload Nuclei layer to S3
resource "aws_s3_object" "upload_nuclei" {
  depends_on = [null_resource.build_artifacts, data.local_file.nuclei_layer]

  bucket      = aws_s3_bucket.artifacts_bucket.id
  key         = "nuclei-layer.zip"
  source      = data.local_file.nuclei_layer.filename
  source_hash = data.local_file.nuclei_layer.content_md5

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.s3_object_tags, {
    Name      = "nuclei-layer"
    Component = "nuclei-lambda"
    Version   = var.nuclei_version
  })
}

# Upload templates layer to S3 (built by unified build system)
resource "aws_s3_object" "upload_templates" {
  depends_on = [null_resource.build_artifacts, data.local_file.templates_layer]

  bucket      = aws_s3_bucket.artifacts_bucket.id
  key         = "templates-layer.zip"
  source      = data.local_file.templates_layer.filename
  source_hash = data.local_file.templates_layer.content_md5

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.s3_object_tags, {
    Name      = "templates-layer"
    Component = "nuclei-lambda"
  })
}

# Lambda function package (built by unified build system)
# The build script already creates a zip file, so we just reference it directly
locals {
  lambda_zip_path = "${path.module}/../../build/artifacts/lambda-function.zip"
}

# ==============================================================================
# ARTIFACT VALIDATION
# ==============================================================================

# Simple validation to ensure artifacts are uploaded
resource "null_resource" "validate_artifacts" {
  depends_on = [
    aws_s3_object.upload_nuclei,
    aws_s3_object.upload_templates,
    null_resource.build_artifacts,
    data.local_file.nuclei_layer,
    data.local_file.templates_layer,
    data.local_file.lambda_function
  ]

  triggers = {
    bucket_name    = aws_s3_bucket.artifacts_bucket.id
    lambda_hash    = data.local_file.lambda_function.content_md5
    nuclei_hash    = data.local_file.nuclei_layer.content_md5
    templates_hash = data.local_file.templates_layer.content_md5
  }

  provisioner "local-exec" {
    interpreter = ["/bin/bash", "-c"]
    command = "${path.module}/scripts/validate_artifacts.sh ${aws_s3_bucket.artifacts_bucket.id} ${path.module}/../../build/artifacts"
  }
}