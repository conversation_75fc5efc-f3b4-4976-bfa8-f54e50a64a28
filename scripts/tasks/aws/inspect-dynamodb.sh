#!/usr/bin/env bash

# ==============================================================================
# LocalStack DynamoDB Table Inspector
# ==============================================================================
# This script provides utilities for inspecting and managing DynamoDB tables in LocalStack

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

log_header() {
    log_info "--- DynamoDB: $1 ---"
}

# Function to list all tables
list_tables() {
    log_header "Listing all DynamoDB tables"
    
    local tables
    tables=$(awslocal dynamodb list-tables 2>&1) || fail "Failed to list tables: $tables"
    
    local table_names
    table_names=$(echo "$tables" | jq -r '.TableNames[] // empty')
    
    if [ -z "$table_names" ]; then
        log_info "No tables found"
    else
        echo "$table_names" | while read -r table; do
            echo "- $table"
        done
    fi
}

# Function to describe table
describe_table() {
    local table="$1"
    log_header "Describing table: $table"
    
    local description
    description=$(awslocal dynamodb describe-table --table-name "$table" 2>&1) || fail "Failed to describe table: $description"
    
    local table_status
    table_status=$(echo "$description" | jq -r '.Table.TableStatus')
    local item_count
    item_count=$(echo "$description" | jq -r '.Table.ItemCount')
    local creation_date
    creation_date=$(echo "$description" | jq -r '.Table.CreationDateTime')
    local size_bytes
    size_bytes=$(echo "$description" | jq -r '.Table.TableSizeBytes')
    
    local formatted_date
    formatted_date=$(date -d "@$creation_date" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "$creation_date")
    
    echo "Table: $table"
    echo "Status: $table_status"
    echo "Items: $item_count"
    echo "Size: $size_bytes bytes"
    echo "Created: $formatted_date"
    echo ""
    echo "Key Schema:"
    echo "$description" | jq -r '.Table.KeySchema[] | "  \(.KeyType): \(.AttributeName)"'
    echo ""
    echo "Attribute Definitions:"
    echo "$description" | jq -r '.Table.AttributeDefinitions[] | "  \(.AttributeName): \(.AttributeType)"'
    
    if echo "$description" | jq -e '.Table.ProvisionedThroughput' >/dev/null; then
        echo ""
        echo "Provisioned Throughput:"
        echo "  Read Capacity Units: $(echo "$description" | jq -r '.Table.ProvisionedThroughput.ReadCapacityUnits')"
        echo "  Write Capacity Units: $(echo "$description" | jq -r '.Table.ProvisionedThroughput.WriteCapacityUnits')"
    fi
    
    echo ""
    echo "Billing Mode: $(echo "$description" | jq -r '.Table.BillingModeSummary.BillingMode // "PROVISIONED"')"
}

# Function to scan table
scan_table() {
    local table="$1"
    local limit="${2:-10}"
    log_header "Scanning table: $table (limit: $limit)"
    
    local scan_result
    scan_result=$(awslocal dynamodb scan --table-name "$table" --limit "$limit" 2>&1) || fail "Failed to scan table: $scan_result"
    
    local item_count
    item_count=$(echo "$scan_result" | jq -r '.Count')
    log_info "Found $item_count items"
    
    if [ "$item_count" -gt 0 ]; then
        echo "$scan_result" | jq '.Items'
    else
        log_info "Table is empty"
    fi
}

# Function to display help
display_help() {
    echo "LocalStack DynamoDB Table Inspector"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  list                     List all tables"
    echo "  describe <table>         Describe a table's structure"
    echo "  scan <table> [limit]     Scan a table (default limit: 10)"
    echo "  help                     Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 describe ${DYNAMODB_TABLE}"
    echo "  $0 scan ${DYNAMODB_TABLE} 20"
}

# Main function
main() {
    require_tool "awslocal"
    require_tool "jq"

    local command="${1:-help}"
    
    case "$command" in
        list)
            list_tables
            ;;
        describe)
            [ $# -lt 2 ] && fail "Table name required"
            describe_table "$2"
            ;;
        scan)
            [ $# -lt 2 ] && fail "Table name required"
            scan_table "$2" "${3:-10}"
            ;;
        help|--help|-h)
            display_help
            ;;
        *)
            fail "Unknown command: $command"
            ;;
    esac
}

# Run main function with all arguments
main "$@"