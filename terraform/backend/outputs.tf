# ==============================================================================
# NUCLEAR POND BACKEND MODULE OUTPUTS
# ==============================================================================

# ECR Repository Outputs
output "ecr_repository_url" {
  description = "URL of the ECR repository for Nuclear Pond"
  value       = aws_ecr_repository.nuclear_pond_repo.repository_url
}

output "ecr_repository_arn" {
  description = "ARN of the ECR repository for Nuclear Pond"
  value       = aws_ecr_repository.nuclear_pond_repo.arn
}

output "ecr_repository_name" {
  description = "Name of the ECR repository for Nuclear Pond"
  value       = aws_ecr_repository.nuclear_pond_repo.name
}

# ECS Cluster Outputs
output "ecs_cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.nuclear_pond_cluster.id
}

output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.nuclear_pond_cluster.name
}

output "ecs_cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.nuclear_pond_cluster.arn
}

# ECS Service Outputs
output "ecs_service_id" {
  description = "ID of the ECS service"
  value       = aws_ecs_service.nuclear_pond_service.id
}

output "ecs_service_name" {
  description = "Name of the ECS service"
  value       = aws_ecs_service.nuclear_pond_service.name
}

output "ecs_service_arn" {
  description = "ARN of the ECS service"
  value       = aws_ecs_service.nuclear_pond_service.id
}

# ECS Task Definition Outputs
output "ecs_task_definition_arn" {
  description = "ARN of the ECS task definition"
  value       = aws_ecs_task_definition.nuclear_pond_task_def.arn
}

output "ecs_task_definition_family" {
  description = "Family of the ECS task definition"
  value       = aws_ecs_task_definition.nuclear_pond_task_def.family
}

output "ecs_task_definition_revision" {
  description = "Revision of the ECS task definition"
  value       = aws_ecs_task_definition.nuclear_pond_task_def.revision
}

# Load Balancer Outputs
output "alb_dns_name" {
  description = "ALB DNS name (direct HTTP access)"
  value       = aws_lb.nuclear_pond_alb.dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the Application Load Balancer"
  value       = aws_lb.nuclear_pond_alb.zone_id
}

output "alb_arn" {
  description = "ARN of the Application Load Balancer"
  value       = aws_lb.nuclear_pond_alb.arn
}

output "alb_arn_suffix" {
  description = "ARN suffix of the Application Load Balancer"
  value       = aws_lb.nuclear_pond_alb.arn_suffix
}

# Target Group Outputs
output "target_group_arn" {
  description = "ARN of the target group"
  value       = aws_lb_target_group.nuclear_pond_tg.arn
}

output "target_group_name" {
  description = "Name of the target group"
  value       = aws_lb_target_group.nuclear_pond_tg.name
}

# Security Group Outputs
output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = aws_security_group.alb_sg.id
}

output "ecs_security_group_id" {
  description = "ID of the ECS service security group"
  value       = aws_security_group.ecs_service_sg.id
}

# CloudWatch Outputs
output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.ecs_nuclear_pond_lg.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.ecs_nuclear_pond_lg.arn
}

# IAM Role Outputs
output "ecs_task_execution_role_arn" {
  description = "ARN of the ECS task execution role"
  value       = aws_iam_role.ecs_task_execution_role.arn
}

output "ecs_task_role_arn" {
  description = "ARN of the ECS task role"
  value       = aws_iam_role.nuclear_pond_task_role.arn
}

# S3 Bucket Outputs
output "s3_bucket_name" {
  description = "Name of the S3 bucket for scan targets"
  value       = aws_s3_bucket.nuclear_pond_targets.bucket
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket for scan targets"
  value       = aws_s3_bucket.nuclear_pond_targets.arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.nuclear_pond_targets.bucket_domain_name
}

# Service URLs
output "api_url" {
  description = "Backend API URL (via CloudFront HTTPS)"
  value       = "https://${aws_cloudfront_distribution.backend_api.domain_name}"
}

output "health_check_url" {
  description = "Health check URL of the Nuclear Pond service"
  value       = "http://${aws_lb.nuclear_pond_alb.dns_name}${var.health_check_path}"
}

# CloudFront outputs
output "cloudfront_domain_name" {
  description = "CloudFront distribution domain name for backend API"
  value       = aws_cloudfront_distribution.backend_api.domain_name
}

output "cloudfront_distribution_id" {
  description = "CloudFront distribution ID for backend API"
  value       = aws_cloudfront_distribution.backend_api.id
}

# Deployment Information
output "deployment_info" {
  description = "Deployment information for the Nuclear Pond backend"
  value = {
    environment      = var.environment
    cluster_name     = aws_ecs_cluster.nuclear_pond_cluster.name
    service_name     = aws_ecs_service.nuclear_pond_service.name
    task_definition  = aws_ecs_task_definition.nuclear_pond_task_def.family
    ecr_repository   = aws_ecr_repository.nuclear_pond_repo.repository_url
    alb_dns_name     = aws_lb.nuclear_pond_alb.dns_name
    service_url      = "https://${aws_cloudfront_distribution.backend_api.domain_name}"
    health_check_url = "http://${aws_cloudfront_distribution.backend_api.domain_name}${var.health_check_path}"
    log_group        = aws_cloudwatch_log_group.ecs_nuclear_pond_lg.name
    s3_bucket_name   = aws_s3_bucket.nuclear_pond_targets.bucket
    s3_bucket_arn    = aws_s3_bucket.nuclear_pond_targets.arn
  }
}
