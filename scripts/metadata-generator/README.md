# Nuclei Template Metadata Generator

This tool generates a metadata index file (`templates.json`) for Nuclei templates stored in an S3 bucket. The metadata index is used by the FastScan application to provide template information without needing to download and parse all templates.

## Purpose

- Creates a single JSON file containing metadata for all Nuclei templates
- Extracts key information like ID, name, severity, tags, etc.
- Provides a summary of templates by severity
- Enables efficient template filtering and selection in the UI

## Usage

### Local Development

The metadata generator is automatically integrated into the local development setup process. When you run:

```bash
devbox run localstack
# or
devbox run local:reset
```

The metadata generator will run as part of the setup process after Lambda deployment.

### Manual Execution

To manually generate metadata for templates in LocalStack:

```bash
devbox run local:metadata
```

### Production Deployment

To update template metadata in a production AWS environment:

```bash
devbox run aws:metadata --bucket your-templates-bucket-name [--profile aws-profile] [--region aws-region]
```

## Integration Points

1. **LocalStack Development**: Automatically runs during `setup-local-development.sh`
2. **AWS Deployment**: Can be run manually or integrated into CI/CD pipelines
3. **Template Updates**: Should be run whenever templates are added or updated

## Environment Variables

- `NUCLEI_TEMPLATES_BUCKET`: S3 bucket containing Nuclei templates
- `LOCAL_DEVELOPMENT`: Set to "true" to use LocalStack endpoints
- `AWS_ENDPOINT_URL`: Custom AWS endpoint (for LocalStack)
- `AWS_REGION`: AWS region to use

## Output

The tool generates a `templates.json` file with the following structure:

```json
{
  "version": "1.0.0",
  "generated_at": "2025-07-21T12:34:56Z",
  "templates": {
    "template-id": {
      "id": "template-id",
      "name": "Template Name",
      "path": "templates/path/to/template.yaml",
      "author": ["Author Name"],
      "severity": "medium",
      "description": "Template description",
      "tags": ["tag1", "tag2"],
      "size": 1234
    },
    // More templates...
  },
  "summary": {
    "total": 123,
    "by_severity": {
      "info": 10,
      "low": 20,
      "medium": 30,
      "high": 40,
      "critical": 23
    }
  }
}
```