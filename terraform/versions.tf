terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"  # Use latest 5.x version for better features and security
    }
    null = {
      source  = "hashicorp/null"
      version = "3.2.1"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "2.2.0"
    }
    github = {
      source  = "integrations/github"
      version = "5.14.0"
    }
  }
}