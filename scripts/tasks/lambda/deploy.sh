#!/usr/bin/env bash

# ==============================================================================
# LocalStack Lambda Deployment Script
# ==============================================================================
# This script deploys the pre-built Nuclei Lambda function and its associated
# layers to LocalStack for local development and testing.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

# --- Paths & Names ---
readonly ARTIFACTS_DIR="${PROJECT_ROOT}/build/artifacts"
readonly LAMBDA_ZIP_FILE="${ARTIFACTS_DIR}/lambda-function.zip"
readonly LAMBDA_ROLE_ARN="arn:aws:iam::000000000000:role/${LAMBDA_ROLE_NAME}"
readonly BUCKET_NAME="fastscan-nuclei-artifacts-local-us-east-1"

check_prerequisites() {
    log_info "Checking prerequisites..."
    require_tool "awslocal"
    require_tool "curl"

    if ! curl -s "${LOCALSTACK_ENDPOINT}/_localstack/health" | grep -q "available"; then
        fail "LocalStack is not running. Please start it with: devbox run localstack"
    fi

    log_info "Ensuring S3 bucket for results exists..."
    if ! awslocal s3api head-bucket --bucket "$BUCKET_NAME" >/dev/null 2>&1; then
        log_info "Bucket $BUCKET_NAME not found. Creating..."
        if ! awslocal s3 mb "s3://$BUCKET_NAME"; then
            fail "Failed to create S3 bucket $BUCKET_NAME"
        fi
        log_success "S3 bucket $BUCKET_NAME created."
    else
        log_success "S3 bucket $BUCKET_NAME already exists."
    fi

    log_success "All prerequisites met"
}

check_lambda_artifacts() {
    log_info "Checking for required Lambda artifacts..."
    local all_found=true

    if [ ! -f "${LAMBDA_ZIP_FILE}" ]; then
        log_error "Lambda function zip not found: ${LAMBDA_ZIP_FILE}"
        all_found=false
    fi

    if [ ! -f "${ARTIFACTS_DIR}/nuclei-layer-arn.txt" ]; then
        log_error "Nuclei layer ARN file not found."
        all_found=false
    fi

    if [ ! -f "${ARTIFACTS_DIR}/templates-layer-arn.txt" ]; then
        log_error "Templates layer ARN file not found."
        all_found=false
    fi

    if [ "$all_found" = "false" ]; then
        fail "One or more required artifacts are missing. Please run the create-layers script first: devbox run local:create-layers"
    fi

    log_success "All required Lambda artifacts found."
}

deploy_lambda() {
    log_header "Deploying Lambda Function to LocalStack"

    local layer_arns=()
    layer_arns+=("$(cat "${ARTIFACTS_DIR}/nuclei-layer-arn.txt")")
    layer_arns+=("$(cat "${ARTIFACTS_DIR}/templates-layer-arn.txt")")

    log_info "Using Lambda layers: ${layer_arns[*]}"

    local env_vars="Variables={BUCKET_NAME=${BUCKET_NAME}}"

    if awslocal lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" >/dev/null 2>&1; then
        log_info "Lambda function $LAMBDA_FUNCTION_NAME already exists, updating..."

        awslocal lambda update-function-code \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --zip-file "fileb://${LAMBDA_ZIP_FILE}" >/dev/null || fail "Failed to update Lambda function code"
        log_success "Lambda function code updated successfully"

        log_info "Waiting for function code update to complete..."
        sleep 5 # Add a delay to prevent ResourceConflictException

        awslocal lambda update-function-configuration \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --layers "${layer_arns[@]}" \
            --environment "$env_vars" \
            --timeout 300 \
            --memory-size 512 >/dev/null || fail "Failed to update Lambda function configuration"
        log_success "Lambda function configuration updated successfully"

    else
        log_info "Creating new Lambda function..."
        awslocal lambda create-function \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --runtime "provided.al2" \
            --role "$LAMBDA_ROLE_ARN" \
            --handler "bootstrap" \
            --zip-file "fileb://${LAMBDA_ZIP_FILE}" \
            --layers "${layer_arns[@]}" \
            --environment "$env_vars" \
            --timeout 300 \
            --memory-size 512 >/dev/null || fail "Failed to create Lambda function"
        log_success "Lambda function created successfully"
    fi
}

display_deployment_info() {
    log_info "Lambda Deployment Information:"
    echo "  Function Name:    $LAMBDA_FUNCTION_NAME"
    echo "  Runtime:          provided.al2 (Go)"
    echo "  Handler:          bootstrap"
    echo "  Timeout:          300 seconds"
    echo "  Memory:           512 MB"
    echo "  Role:             $LAMBDA_ROLE_ARN"
    echo "  Results Bucket:   $BUCKET_NAME"
}

main() {
    log_header "Lambda Deployment to LocalStack"

    local TEST_LAMBDA=false
    local CREATE_LAYERS=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --test)
                TEST_LAMBDA=true
                shift
                ;;
            --create-layers)
                CREATE_LAYERS=true
                shift
                ;;
            *)
                fail "Unknown option: $1"
                ;;
        esac
    done

    check_prerequisites

    if [ "${CREATE_LAYERS}" = "true" ]; then
        log_info "Ensuring layers are created and up-to-date..."
        if ! bash "${SCRIPT_DIR}/create-layers.sh"; then
            fail "Failed to create or update Lambda layers. Aborting deployment."
        fi
    fi

    check_lambda_artifacts
    
    # Prepare Lambda function for LocalStack by embedding nuclei binary
    log_info "Preparing Lambda function for LocalStack..."
    if ! bash "${SCRIPT_DIR}/prepare-localstack.sh"; then
        fail "Failed to prepare Lambda function for LocalStack. Aborting deployment."
    fi
    
    deploy_lambda

    if [ "${TEST_LAMBDA}" = "true" ]; then
        log_info "Testing lambda..."
        bash "${SCRIPT_DIR}/test.sh"
    fi

    display_deployment_info
    log_success "Lambda deployment completed successfully!"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
