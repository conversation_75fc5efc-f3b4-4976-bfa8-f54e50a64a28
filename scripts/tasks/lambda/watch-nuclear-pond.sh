#!/usr/bin/env bash

# ==============================================================================
# Nuclear Pond Hot Reloading Watcher Script
# ==============================================================================
# This script provides hot reloading for the Go backend service
# using either 'air' (if available) or a custom file watcher implementation.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

readonly NUCLEAR_POND_DIR="$PROJECT_ROOT/backend"

usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --port N         Set server port (default: 8082)"
    echo "  --force-polling  Force use of polling instead of 'air'"
    echo "  --help           Show this help message"
}

check_prerequisites() {
    log_header "Checking Prerequisites"
    require_tool "go"
    require_tool "curl"
    
    if [ ! -d "$NUCLEAR_POND_DIR" ]; then
        fail "Nuclear Pond directory not found: $NUCLEAR_POND_DIR"
    fi
    
    if ! command -v air &> /dev/null; then
        log_warn "Air hot reloading tool not found. Will use polling fallback."
        log_warn "Install with: $0 --install-air"
        export USE_POLLING=true
    fi
    
    if ! curl -s "http://localhost:4566/health" | grep -q "running" 2>/dev/null; then
        log_warn "LocalStack is not running. Backend will use production AWS endpoints."
    fi
    
    log_success "All prerequisites met"
}

create_air_config() {
    local config_file="$NUCLEAR_POND_DIR/.air.toml"
    
    if [ -f "$config_file" ]; then
        log_info "Air configuration already exists, cleaning..."
        rm "$config_file"
    fi
    
    log_info "Creating air configuration..."
    
    cat > "$config_file" << 'EOF'
root = "."
tmp_dir = "tmp"

[build]
  args_bin = ["service"]
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main ."
  include_ext = ["go", "tpl", "tmpl", "html", "yaml", "yml", "json"]
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "docs"]
  exclude_regex = ["_test.go"]
  log = "build-errors.log"

[color]
  main = "magenta"
  watcher = "cyan"
  build = "yellow"
  runner = "green"

[log]
  time = true
EOF
    
    log_success "Air configuration created at $config_file"
}

setup_environment() {
    log_header "Setting Up Environment"
    
    source "${PROJECT_ROOT}/scripts/bin/load-local-env.sh"
    export PORT=${SERVER_PORT:-8082}
    
    log_success "Environment configured"
}

watch_with_air() {
    log_header "Starting Nuclear Pond with Air Hot Reloading"
    (
        cd "$NUCLEAR_POND_DIR"
        create_air_config
        mkdir -p tmp
        
        log_info "Starting air hot reloading..."
        log_info "Server will be available at: http://localhost:${PORT:-8082}"
        log_info "Press Ctrl+C to stop"
        
        air
    )
}

main() {
    local USE_POLLING=${USE_POLLING:-false}
    local SERVER_PORT=8082
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --port) SERVER_PORT=$2; shift 2 ;;
            --force-polling) USE_POLLING=true; shift ;;
            --help) usage; exit 0 ;;
            *) fail "Unknown option: $1" ;;
        esac
    done
    
    export SERVER_PORT
    export USE_POLLING
    
    trap 'echo -e "\n${YELLOW}[INFO]${NC} Nuclear Pond watcher stopped"; exit 0' INT
    
    check_prerequisites
    setup_environment
    
    if [ "$USE_POLLING" = "true" ] || ! command -v air &> /dev/null; then
        # The polling logic was complex and can be replaced by a simple go run for now
        log_warn "Polling not implemented in this refactor, please install 'air'."
        fail "Aborting."
    else
        watch_with_air
    fi
}

main "$@"