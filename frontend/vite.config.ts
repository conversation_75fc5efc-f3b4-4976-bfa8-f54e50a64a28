import path from "node:path";
import tailwindcss from "@tailwindcss/vite";
import { tanstackRouter } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import { defineConfig, loadEnv } from "vite";
/// <reference types="vitest" />

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables from .env file based on mode
  loadEnv(mode, process.cwd(), "");

  return {
    server: {
      host: "::",
      port: 5175,
    },
    plugins: [
      tanstackRouter({
        target: "react",
        autoCodeSplitting: true,
      }),
      react({
        babel: {
          plugins: [["babel-plugin-react-compiler", {}]],
        },
      }),
      tailwindcss(),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // Define environment variables
    define: {
      __APP_ENV__: JSON.stringify(mode),
    },
    // Build configuration
    build: {
      outDir: "dist",
      sourcemap: mode !== "production",
      // Minify for production
      minify: mode === "production" ? "esbuild" : false,
      // Generate manifest file for asset handling
      manifest: true,
    },
    // Test configuration
    test: {
      globals: true,
      environment: "jsdom",
      setupFiles: ["./src/test/setup.ts"],
    },
  };
});
