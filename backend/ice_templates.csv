cve_id	nuclei_name	nuclei_template_url
CVE-2024-55591	Fortinet Authentication Bypass	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-55591.yaml
CVE-2024-55956	Cleo Harmony < 5.8.0.24 - File Upload Vulnerability	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-55956.yaml
CVE-2024-51378	CyberPanel - Command Injection	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-51378.yaml
CVE-2024-4577	PHP CGI - Argument Injection	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-4577.yaml
CVE-2024-3400	GlobalProtect - OS Command Injection	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-3400.yaml
CVE-2023-48788	Fortinet Forticlient Endpoint Management Server - SQL Injection	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-48788.yaml
CVE-2024-27198	TeamCity < 2023.11.4 - Authentication Bypass	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-27198.yaml
CVE-2024-1709	ConnectWise ScreenConnect 23.9.7 - Authentication Bypass	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2024/CVE-2024-1709.yaml
CVE-2023-22527	Atlassian Confluence - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-22527.yaml
CVE-2023-47246	SysAid Server - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-47246.yaml
CVE-2023-22518	Atlassian Confluence Server - Improper Authorization	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-22518.yaml
CVE-2023-46604	Apache ActiveMQ - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-46604.yaml
CVE-2023-4966	Citrix Bleed - Leaking Session Tokens	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-4966.yaml
CVE-2023-22515	Atlassian Confluence - Privilege Escalation	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-22515.yaml
CVE-2023-42793	JetBrains TeamCity < 2023.05.4 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-42793.yaml
CVE-2023-41265	Qlik Sense Enterprise - HTTP Request Smuggling	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-41265.yaml
CVE-2023-35082	MobileIron Core - Remote Unauthenticated API Access	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-35082.yaml
CVE-2023-35078	Ivanti Endpoint Manager Mobile (EPMM) - Authentication Bypass	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-35078.yaml
CVE-2023-38203	Adobe ColdFusion - Deserialization of Untrusted Data	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-38203.yaml
CVE-2023-29300	Adobe ColdFusion - Pre-Auth Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-29300.yaml
CVE-2023-29357	Microsoft SharePoint - Authentication Bypass	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-29357.yaml
CVE-2023-34362	MOVEit Transfer - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-34362.yaml
CVE-2023-27350	PaperCut - Unauthenticated Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-27350.yaml
CVE-2022-47986	IBM Aspera Faspex <=4.4.2 PL1 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-47986.yaml
CVE-2022-24990	TerraMaster TOS < 4.2.30 Server Information Disclosure	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-24990.yaml
CVE-2023-0669	Fortra GoAnywhere MFT - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2023/CVE-2023-0669.yaml
CVE-2022-47966	ManageEngine - Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-47966.yaml
CVE-2022-37042	Zimbra Collaboration Suite 8.8.15/9.0 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-37042.yaml
CVE-2022-26138	Atlassian Questions For Confluence - Hardcoded Credentials	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-26138.yaml
CVE-2022-26134	Confluence - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-26134.yaml
CVE-2022-29464	WSO2 Management - Arbitrary File Upload & Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-29464.yaml
CVE-2022-22954	VMware Workspace ONE Access - Server-Side Template Injection	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-22954.yaml
CVE-2022-1040	Sophos Firewall <=18.5 MR3 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2022/CVE-2022-1040.yaml
CVE-2021-44228	Apache Log4j2 Remote Code Injection	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-44228.yaml
CVE-2021-20038	SonicWall SMA100 Stack - Buffer Overflow/Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-20038.yaml
CVE-2021-42237	Sitecore Experience Platform Pre-Auth RCE	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-42237.yaml
CVE-2021-42013	Apache 2.4.49/2.4.50 - Path Traversal and Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-42013.yaml
CVE-2021-41773	Apache 2.4.49 - Path Traversal and Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-41773.yaml
CVE-2021-22005	VMware vCenter Server - Arbitrary File Upload	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-22005.yaml
CVE-2021-38647	Microsoft Open Management Infrastructure - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-38647.yaml
CVE-2021-40539	Zoho ManageEngine ADSelfService Plus v6113 - Unauthenticated Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-40539.yaml
CVE-2021-26084	Confluence Server - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-26084.yaml
CVE-2021-35464	ForgeRock OpenAM <7.0 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-35464.yaml
CVE-2021-34473	Exchange Server - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-34473.yaml
CVE-2021-21985	VMware vSphere Client (HTML5) - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-21985.yaml
CVE-2021-22205	GitLab CE/EE - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-22205.yaml
CVE-2021-22986	F5 iControl REST -  Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-22986.yaml
CVE-2021-26855	Microsoft Exchange Server SSRF Vulnerability	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-26855.yaml
CVE-2021-21972	VMware vSphere Client (HTML5) - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2021/CVE-2021-21972.yaml
CVE-2020-28188	TerraMaster TOS - Unauthenticated Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2020/CVE-2020-28188.yaml
CVE-2020-5902	F5 BIG-IP TMUI - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2020/CVE-2020-5902.yaml
CVE-2019-19781	Citrix ADC and Gateway - Directory Traversal	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-19781.yaml
CVE-2019-16920	D-Link Routers - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-16920.yaml
CVE-2019-16057	D-Link DNS-320 -  Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-16057.yaml
CVE-2019-2729	Oracle WebLogic Server Administration Console - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-2729.yaml
CVE-2018-13379	Fortinet FortiOS - Credentials Disclosure	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2018/CVE-2018-13379.yaml
CVE-2019-11510	Pulse Connect Secure SSL VPN Arbitrary File Read	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-11510.yaml
CVE-2019-2725	Oracle WebLogic Server - Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-2725.yaml
CVE-2019-3396	Atlassian Confluence Server - Path Traversal	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2019/CVE-2019-3396.yaml
CVE-2018-11776	Apache Struts2 S2-057 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2018/CVE-2018-11776.yaml
CVE-2018-2894	Oracle WebLogic Server - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2018/CVE-2018-2894.yaml
CVE-2018-1273	Spring Data Commons - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2018/CVE-2018-1273.yaml
CVE-2018-7600	Drupal - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2018/CVE-2018-7600.yaml
CVE-2018-6530	D-Link - Unauthenticated Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2018/CVE-2018-6530.yaml
CVE-2017-10271	Oracle WebLogic Server - Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2017/CVE-2017-10271.yaml
CVE-2017-9805	Apache Struts2 S2-052 - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2017/CVE-2017-9805.yaml
CVE-2017-5638	Apache Struts 2 - Remote Command Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2017/CVE-2017-5638.yaml
CVE-2015-1427	ElasticSearch - Remote Code Execution	https://github.com/projectdiscovery/nuclei-templates/blob/main/http/cves/2015/CVE-2015-1427.yaml
