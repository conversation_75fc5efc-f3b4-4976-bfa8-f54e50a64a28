// Vulnerable targets display types

/**
 * Vulnerable target data structure for display purposes
 */
export interface VulnerableTargetData {
  target: string;
  templateIds: string[];
  vulnerabilityCount: number;
}

/**
 * Props for the VulnerableTargetsList component
 */
export interface VulnerableTargetsListProps {
  vulnerableTargets: Record<string, string[]>; // target -> template IDs
  totalVulnerableTargets: number;
  loading?: boolean;
  error?: string | null;
}
