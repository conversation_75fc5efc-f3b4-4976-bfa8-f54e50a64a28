package outputs

import (
	"fmt"
)

// NormalizeFinding ensures a consistent map[string]any structure and extracts common fields.
// Returns normalized map and a boolean indicating if the item is a valid finding object.
func NormalizeFinding(item any) (map[string]any, bool) {
	m, ok := item.(map[string]any)
	if !ok {
		return nil, false
	}
	// Surface severity and template_id from nested info when absent
	if _, exists := m["severity"]; !exists {
		if info, ok := m["info"].(map[string]any); ok {
			if sev, ok := info["severity"].(string); ok {
				m["severity"] = sev
			}
			if tid, ok := info["id"].(string); ok {
				m["template_id"] = tid
			}
		}
	}
	if _, exists := m["template_id"]; !exists {
		if id, ok := m["templateID"].(string); ok {
			m["template_id"] = id
		} else if id, ok := m["template-id"].(string); ok {
			m["template_id"] = id
		}
	}

	return m, true
}

// ValidateAndNormalizeFindings validates raw findings, normalizes structure, and returns integrity stats.
type IntegrityReport struct {
	Checksums      map[string]string `json:"checksums,omitempty"`
	Issues         []string          `json:"issues"`
	FindingsCount  int               `json:"findings_count"`
	MalformedCount int               `json:"malformed_count"`
	Validated      bool              `json:"validated"`
}

func ValidateAndNormalizeFindings(raw []any, maxFindings int) ([]map[string]any, IntegrityReport) {
	var normalized []map[string]any
	report := IntegrityReport{Validated: false, Issues: []string{}, Checksums: map[string]string{}}

	for i, it := range raw {
		n, ok := NormalizeFinding(it)
		if !ok {
			report.MalformedCount++
			if report.MalformedCount <= 5 {
				report.Issues = append(report.Issues, fmt.Sprintf("malformed item at index %d", i))
			}

			continue
		}
		normalized = append(normalized, n)
		if maxFindings > 0 && len(normalized) >= maxFindings {
			report.Issues = append(report.Issues, fmt.Sprintf("findings truncated at %d (max %d)", len(normalized), maxFindings))

			break
		}
	}
	report.FindingsCount = len(normalized)
	report.Validated = true

	return normalized, report
}
