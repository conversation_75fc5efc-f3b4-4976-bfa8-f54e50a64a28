package configs

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	APIEndpoint       string
	WorkerCount       int
	TaskChannelSize   int
	ResultsDir        string
	NucleiTemplateID  string
	PRODUCT           string
	DebugMode         bool
	ScanIntervalSecs  int
}

func LoadConfig() *Config {
	if err := godotenv.Load(); err != nil {
		log.Printf("[WARN] No .env file found or failed to load: %v", err)
	}

	workerCount := getEnvAsInt("WORKER_COUNT", 10)
	taskChannelSize := getEnvAsInt("TASK_CHANNEL_SIZE", 100)
	scanIntervalSecs := getEnvAsInt("SCAN_INTERVAL_SECONDS", 120)
	resultsDir := getEnvOrDefault("RESULTS_DIR", "results")
	nucleiTemplateID := getEnvOrDefault("NUCLEI_TEMPLATE_ID", "CVE-2023-42793")
	debugMode := getEnvAsBool("DEBUG_MODE", false)
	apiEndpoint := os.Getenv("API_ENDPOINT")
	product := os.Getenv("PRODUCT")
	if apiEndpoint == "" {
		log.Fatal("API_ENDPOINT not set in environment")
	}
	return &Config{
		APIEndpoint:       apiEndpoint,
		WorkerCount:       workerCount,
		TaskChannelSize:   taskChannelSize,
		ResultsDir:        resultsDir,
		NucleiTemplateID:  nucleiTemplateID,
		PRODUCT:           product,
		DebugMode:         debugMode,
		ScanIntervalSecs:  scanIntervalSecs,
	}
}

func getEnvOrDefault(key, defaultVal string) string {
	val := os.Getenv(key)
	if val == "" {
		return defaultVal
	}
	return val
}

func getEnvAsInt(key string, defaultVal int) int {
	valStr := os.Getenv(key)
	if valStr == "" {
		return defaultVal
	}
	val, err := strconv.Atoi(valStr)
	if err != nil {
		log.Printf("[WARN] Invalid value for %s: %s, using default %d", key, valStr, defaultVal)
		return defaultVal
	}
	return val
}

func getEnvAsBool(key string, defaultVal bool) bool {
	valStr := os.Getenv(key)
	if valStr == "" {
		return defaultVal
	}
	val, err := strconv.ParseBool(valStr)
	if err != nil {
		log.Printf("[WARN] Invalid boolean value for %s: %s, using default %t", key, valStr, defaultVal)
		return defaultVal
	}
	return val
}
