package main

import (
	"log"
	"net/url"
	"time"

	"github.com/RootEvidence/edge-scanner/internal/configs"
	"github.com/RootEvidence/edge-scanner/internal/controller"
)

func main() {
	cfg := configs.LoadConfig()
	product := cfg.PRODUCT
	baseURL := cfg.APIEndpoint + "/job/batch/next"

	u, err := url.Parse(baseURL)
	if err != nil {
		// handle error (e.g., log or return)
		log.Fatal(err)
	}
	q := u.Query()          // get existing query params (if any)
	q.Set("queue", product) // add or update the "queue" param
	u.RawQuery = q.Encode() // encode and attach to the URL

	apiURL := u.String()
	scanInterval := time.Duration(cfg.ScanIntervalSecs) * time.Second
	ticker := time.NewTicker(scanInterval)
	defer ticker.Stop()

	if cfg.DebugMode {
		log.Printf("[DEBUG] Scan interval set to %v", scanInterval)
		log.Printf("[DEBUG] API URL: %s", apiURL)
		log.Printf("[DEBUG] Worker count: %d", cfg.WorkerCount)
	}

	for {
		controller.FetchAndProcessTargets(cfg, apiURL)
		log.Printf("[INFO] Waiting %v before next fetch...", scanInterval)
		<-ticker.C
	}
}
