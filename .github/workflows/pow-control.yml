name: PoW Infrastructure Control

on:
  repository_dispatch:
    types: [pow-start, pow-stop]

jobs:
  pow-control:
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      id-token: write
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Log workflow trigger
        run: |
          echo "Workflow triggered by: ${{ github.event.action }}"
          echo "Request ID: ${{ github.event.client_payload.request_id }}"
          echo "Action: ${{ github.event.client_payload.action }}"
          echo "Timestamp: ${{ github.event.client_payload.timestamp }}"
          echo "Source: ${{ github.event.client_payload.source }}"
          
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.5.0
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION || 'us-east-1' }}
          
      - name: Initialize Terraform
        working-directory: ./terraform
        run: terraform init
        
      - name: Start PoW Infrastructure
        if: github.event.action == 'pow-start'
        working-directory: ./terraform
        run: |
          echo "🚀 Starting PoW infrastructure deployment for DEV environment..."
          echo "This will create the pow_targets module with all AWS resources:"
          echo "  - 2x EC2 instances (t3.micro)"
          echo "  - Application Load Balancer"
          echo "  - Route53 DNS records"
          echo "  - Security groups"
          echo "  - Uses existing VPC and subnets from network module"
          
          # Use dev environment configuration with PoW targets enabled. Example vars, since we dont need secrets at this point
          terraform plan -var-file="environments/dev.example.tfvars" -var="enable_pow_targets=true" -target=module.pow_targets -out=pow-start.plan
          
          # Apply the plan
          terraform apply -auto-approve pow-start.plan
          
          echo "✅ PoW infrastructure deployment completed!"
          echo "📊 Terraform outputs:"
          terraform output pow_summary || echo "No PoW summary available"
          
      - name: Stop PoW Infrastructure
        if: github.event.action == 'pow-stop'
        working-directory: ./terraform
        run: |
          echo "🛑 Stopping PoW infrastructure for DEV environment..."
          echo "This will destroy the pow_targets module (count = 0) and all its AWS resources:"
          echo "  - EC2 instances will be terminated"
          echo "  - Load balancer will be deleted"
          echo "  - Route53 records will be removed"
          echo "  - Security groups will be cleaned up"
          echo "  - Network infrastructure will remain (VPC, subnets, etc.)"
          
          # Use dev environment configuration with PoW targets disabled. Example vars, since we dont need secrets at this point
          terraform plan -var-file="environments/dev.example.tfvars" -var="enable_pow_targets=false" -target=module.pow_targets -out=pow-stop.plan
          
          # Apply the plan (this will destroy PoW resources because count = 0)
          terraform apply -auto-approve pow-stop.plan
          
          echo "✅ PoW infrastructure destruction completed!"
          echo "💰 Cost savings: PoW infrastructure is now offline"
          
      - name: Handle deployment errors
        if: failure()
        run: |
          echo "❌ Deployment failed for request ID: ${{ github.event.client_payload.request_id }}"
          echo "Action: ${{ github.event.client_payload.action }}"
          echo "Check the logs above for detailed error information"
          
      - name: Post-deployment summary
        if: success()
        run: |
          echo "✅ PoW infrastructure operation completed successfully!"
          echo "Request ID: ${{ github.event.client_payload.request_id }}"
          echo "Action: ${{ github.event.client_payload.action }}"
          
          if [ "${{ github.event.action }}" == "pow-start" ]; then
            echo "🎯 PoW targets are now available for demo scanning"
            echo "💡 Remember to stop the infrastructure when done to save costs"
          else
            echo "💰 PoW infrastructure is now offline"
          fi 