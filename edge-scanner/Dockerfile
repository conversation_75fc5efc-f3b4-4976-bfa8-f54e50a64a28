# Build stage
FROM golang:1.24-alpine AS builder

# Install git and ca-certificates (needed for go mod download)
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o edge-scanner ./cmd/scanner

# Runtime stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests and other utilities
RUN apk --no-cache add ca-certificates curl

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Create results directory
RUN mkdir -p /app/results && \
    chown -R appuser:appgroup /app

# Set working directory
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/edge-scanner .

# Change ownership to non-root user
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ps aux | grep edge-scanner || exit 1

# Run the application
CMD ["./edge-scanner"]

