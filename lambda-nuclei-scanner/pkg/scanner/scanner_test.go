package scanner

import (
	"main/internal/types"
	"path/filepath"
	"testing"
)

func TestScanRequest_TemplateSelection(t *testing.T) {
	// Test that ScanRequest properly handles template selections
	request := &types.ScanRequest{
		Targets:   []string{"example.com"},
		Output:    "json",
		Templates: []string{"CVE-2024-3400", "CVE-2023-22518"},
	}

	// Verify templates are properly set
	if len(request.Templates) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 templates, got %d", len(request.Templates))
	}

	if request.Templates[0] != "CVE-2024-3400" {
		t.<PERSON><PERSON><PERSON>("Expected first template to be CVE-2024-3400, got %s", request.Templates[0])
	}

	if request.Templates[1] != "CVE-2023-22518" {
		t.<PERSON><PERSON><PERSON>("Expected second template to be CVE-2023-22518, got %s", request.Templates[1])
	}
}

func TestTemplatePathConstruction(t *testing.T) {
	// Test that template paths are constructed correctly with .yaml extension
	testCases := []struct {
		input    string
		expected string
	}{
		{"CVE-2023-22527", "CVE-2023-22527.yaml"},
		{"CVE-2024-51378", "CVE-2024-51378.yaml"},
		{"template.yaml", "template.yaml"}, // Already has extension
		{"template.yml", "template.yml"},   // Already has extension
	}

	for _, tc := range testCases {
		// Simulate the logic from HandleTemplateSelection
		templateFile := tc.input
		ext := filepath.Ext(templateFile)
		if ext != ".yaml" && ext != ".yml" {
			templateFile = tc.input + ".yaml"
		}

		if templateFile != tc.expected {
			t.Errorf("Template path construction failed for %s: expected %s, got %s",
				tc.input, tc.expected, templateFile)
		}
	}
}
