import { Link } from "@tanstack/react-router";
import { formatRelative } from "date-fns";
import { AlertCircleIcon, CheckCircleIcon, ClockIcon, FileTextIcon, PlayIcon, SearchIcon, XCircleIcon } from "lucide-react";
import type React from "react";
import { useState } from "react";
import { useScans } from "@/hooks/use-api";
import type { ScanRequest } from "@/types";

const Results: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Get scan data using our custom hook with filter
  const { data, loading, error } = useScans(activeTab === "all" ? { limit: 50 } : { status: activeTab, limit: 50 });

  const scans = data?.scans || [];
  console.log("scans", scans);

  // Filter scans based on search term only (status filtering is done on backend)
  const filteredScans = scans.filter((scan) => {
    const matchesSearch =
      searchTerm === "" ||
      scan.requestId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scan.config?.targets?.some((target: string) => target.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesSearch;
  });

  // Render status badge with semantic colors
  const renderScanStatusBadge = (status: string) => {
    switch (status) {
      case "running":
        return (
          <div className="badge badge-info gap-1">
            <PlayIcon className="h-3 w-3" />
            Running
          </div>
        );
      case "completed":
        return (
          <div className="badge badge-success gap-1">
            <CheckCircleIcon className="h-3 w-3" />
            Completed
          </div>
        );
      case "failed":
        return (
          <div className="badge badge-error gap-1">
            <XCircleIcon className="h-3 w-3" />
            Failed
          </div>
        );
      case "queued":
        return (
          <div className="badge badge-warning gap-1">
            <ClockIcon className="h-3 w-3" />
            Queued
          </div>
        );
      default:
        return (
          <div className="badge badge-neutral gap-1">
            <AlertCircleIcon className="h-3 w-3" />
            {status}
          </div>
        );
    }
  };

  // Get tab counts for better UX
  const getTabCount = (tabStatus: string) => {
    if (tabStatus === "all") return scans.length;
    return scans.filter((scan) => scan.status === tabStatus).length;
  };

  // Format duration in a human-readable way
  const formatDuration = (seconds: number | undefined) => {
    if (!seconds) return "N/A";

    if (seconds < 60) {
      return `${seconds}s`;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes < 60) {
      return `${minutes}m ${remainingSeconds}s`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
  };

  // Render performance metrics for completed scans
  const renderPerformanceMetrics = (scan: ScanRequest) => {
    if (scan.status !== "completed") {
      return <span className="text-xs text-base-content/60">-</span>;
    }

    const targetCount = scan.config?.target_count || 0;
    const templates = scan.config?.templates?.length || 0;

    return (
      <div className="text-xs flex gap-2 flex-row flex-wrap">
        <span className="badge badge-sm badge-accent">{targetCount} targets</span>
        <span className="badge badge-sm badge-accent">{templates} templates</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <div className="p-3 bg-secondary/10 rounded-lg">
          <FileTextIcon className="h-8 w-8 text-secondary" />
        </div>
        <div>
          <h1 className="text-4xl font-bold text-primary mb-2">Scan Results</h1>
          <p className="text-base-content/70 text-lg">Monitor and analyze your security assessment results</p>
        </div>
      </div>

      {/* Search */}
      <div className="bg-base-100 p-4 rounded-lg border border-base-300">
        <label className="input input-bordered input-primary flex items-center gap-2 w-full">
          <SearchIcon className="h-4 w-4 opacity-70" />
          <input type="search" placeholder="Search by ID or domain..." className="grow" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
        </label>
      </div>

      {/* Tabs */}
      <div className="tabs tabs-bordered w-full">
        <button type="button" className={`tab tab-lg ${activeTab === "all" ? "tab-active" : ""}`} onClick={() => setActiveTab("all")}>
          All Scans
          <div className="badge badge-neutral badge-sm ml-2">{getTabCount("all")}</div>
        </button>
        <button type="button" className={`tab tab-lg ${activeTab === "running" ? "tab-active" : ""}`} onClick={() => setActiveTab("running")}>
          Running
          <div className="badge badge-info badge-sm ml-2">{getTabCount("running")}</div>
        </button>
        <button type="button" className={`tab tab-lg ${activeTab === "completed" ? "tab-active" : ""}`} onClick={() => setActiveTab("completed")}>
          Completed
          <div className="badge badge-success badge-sm ml-2">{getTabCount("completed")}</div>
        </button>
        <button type="button" className={`tab tab-lg ${activeTab === "failed" ? "tab-active" : ""}`} onClick={() => setActiveTab("failed")}>
          Failed
          <div className="badge badge-error badge-sm ml-2">{getTabCount("failed")}</div>
        </button>
      </div>

      {/* Results Content */}
      <div className="bg-base-100 rounded-lg border border-base-300">
        {error ? (
          <div className="text-center py-12">
            <div className="p-4 bg-error/10 rounded-full w-fit mx-auto mb-4">
              <AlertCircleIcon className="h-12 w-12 text-error" />
            </div>
            <h3 className="text-xl font-semibold text-base-content/80 mb-2">Error Loading Scans</h3>
            <p className="text-base-content/60 mb-4">{error instanceof Error ? error.message : "An unexpected error occurred while loading scan results"}</p>
            <button type="button" className="btn btn-primary" onClick={() => window.location.reload()}>
              Retry Loading
            </button>
          </div>
        ) : loading ? (
          <div className="p-4">
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>Request ID</th>
                    <th>Status</th>
                    <th>Timestamp</th>
                    <th>Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {[1, 2, 3, 4, 5].map((skeletonId) => (
                    <tr key={`skeleton-row-${skeletonId}`}>
                      <td>
                        <div className="skeleton h-4 w-32"></div>
                      </td>
                      <td>
                        <div className="skeleton h-6 w-20"></div>
                      </td>
                      <td>
                        <div className="skeleton h-4 w-36"></div>
                      </td>
                      <td>
                        <div className="skeleton h-4 w-48"></div>
                      </td>
                      <td>
                        <div className="skeleton h-4 w-36"></div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : filteredScans.length === 0 ? (
          <div className="text-center py-12">
            <div className="p-4 bg-base-200 rounded-full w-fit mx-auto mb-4">
              <SearchIcon className="h-12 w-12 text-base-content/40" />
            </div>
            <h3 className="text-xl font-semibold text-base-content/80 mb-2">No Scans Found</h3>
            <p className="text-base-content/60 mb-6">
              {searchTerm ? `No scan results match your search term "${searchTerm}"` : `No ${activeTab !== "all" ? activeTab : ""} scan results found.`}
            </p>
            <Link to="/scan" className="btn btn-primary">
              Start New Scan
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="table table-zebra">
              <thead>
                <tr>
                  <th>Request ID</th>
                  <th>Status</th>
                  <th>Timestamp</th>
                  <th>Performance</th>
                </tr>
              </thead>
              <tbody>
                {filteredScans.map((scan) => (
                  <tr key={scan.requestId} className="hover">
                    <td>
                      <Link to="/scans/$id" params={{ id: scan.requestId }} className="flex items-center gap-2 hover:underline">
                        <div className="p-1 bg-info/10 rounded">
                          <FileTextIcon className="h-3 w-3 text-info" />
                        </div>
                        <span className="font-mono text-sm font-medium">{scan.requestId}</span>
                      </Link>
                    </td>
                    <td>{renderScanStatusBadge(scan.status)}</td>
                    <td>
                      <div className="text-sm">{formatRelative(new Date(scan.timestamp), new Date())}</div>
                    </td>

                    <td>{renderPerformanceMetrics(scan)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default Results;
