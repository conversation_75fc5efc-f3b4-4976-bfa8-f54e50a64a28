#!/bin/bash

set -e

echo "🧪 Running Edge Scanner Integration Tests"

# Check if we're in the edge-scanner directory
if [ ! -f "go.mod" ] || [ ! -d "cmd/scanner" ]; then
    echo "❌ Please run this script from the edge-scanner directory"
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0

test_passed() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

test_failed() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
}

test_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🔧 Environment Setup Tests"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Test 1: Check if .env file exists
if [ -f ".env" ]; then
    test_passed ".env file exists"
else
    test_failed ".env file missing (run ./scripts/dev-setup.sh)"
fi

# Test 2: Check Go dependencies
echo "📦 Testing Go dependencies..."
if go mod download &>/dev/null; then
    test_passed "Go dependencies resolved"
else
    test_failed "Go dependencies failed to resolve"
fi

# Test 3: Check if Nuclei is installed
if command -v nuclei &> /dev/null; then
    NUCLEI_VERSION=$(nuclei -version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
    test_passed "Nuclei installed ($NUCLEI_VERSION)"
else
    test_warning "Nuclei not installed (run: go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@3.1.7)"
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🔄 Unit Tests"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Test 4: Run unit tests
echo "🧪 Running unit tests..."
if go test ./... -v; then
    test_passed "All unit tests passed"
else
    test_failed "Some unit tests failed"
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🐳 Docker Integration Tests"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Test 5: Check Docker availability
if docker info &>/dev/null; then
    test_passed "Docker is available"
    
    # Test 6: Test Docker build
    echo "🏗️  Testing Docker build..."
    if docker build -t edge-scanner-test:latest . &>/dev/null; then
        test_passed "Docker build successful"
        
        # Clean up test image
        docker rmi edge-scanner-test:latest &>/dev/null || true
    else
        test_failed "Docker build failed"
    fi
    
    # Test 7: Test mock API startup
    echo "🎭 Testing mock API..."
    
    # Stop any existing mock API
    docker-compose -f mock-api/docker-compose.mock.yml down &>/dev/null || true
    
    # Start mock API
    if docker-compose -f mock-api/docker-compose.mock.yml up -d &>/dev/null; then
        # Wait for API to be ready
        sleep 5
        
        # Test API endpoint
        if curl -s http://localhost:8080/job/batch/next?queue=edge_scan_dev &>/dev/null; then
            test_passed "Mock API is responding"
        else
            test_failed "Mock API not responding"
        fi
        
        # Stop mock API
        docker-compose -f mock-api/docker-compose.mock.yml down &>/dev/null || true
    else
        test_failed "Failed to start mock API"
    fi
else
    test_warning "Docker not available - skipping Docker tests"
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🔍 Configuration Tests"  
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Test 8: Test configuration loading
echo "⚙️  Testing configuration loading..."
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs) 2>/dev/null || true
    
    # Test required environment variables
    if [ -n "$API_ENDPOINT" ]; then
        test_passed "API_ENDPOINT configured ($API_ENDPOINT)"
    else
        test_failed "API_ENDPOINT not configured"
    fi
    
    if [ -n "$WORKER_COUNT" ]; then
        test_passed "WORKER_COUNT configured ($WORKER_COUNT)"
    else
        test_warning "WORKER_COUNT not configured (will use default)"
    fi
else
    test_failed "Cannot test configuration - .env file missing"
fi

# Test 9: Test single run compilation
echo "🔨 Testing single run compilation..."
if go build -o /tmp/edge-scanner-test ./cmd/scanner/main_dev.go &>/dev/null; then
    test_passed "Single run mode compiles successfully"
    rm -f /tmp/edge-scanner-test
else
    test_failed "Single run mode compilation failed"
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 Test Results Summary"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))

echo "📈 Tests Passed: $TESTS_PASSED"
echo "📉 Tests Failed: $TESTS_FAILED"
echo "📊 Total Tests:  $TOTAL_TESTS"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! Edge Scanner is ready for development.${NC}"
    echo ""
    echo "🚀 Quick start commands:"
    echo "  ./scripts/run-mock-api.sh  # Start mock API"
    echo "  ./scripts/run-dev.sh       # Run scanner in development mode"
    echo "  ./scripts/run-single.sh    # Run single scan cycle"
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some tests failed. Please fix the issues above before proceeding.${NC}"
    
    if [ $TESTS_PASSED -gt 0 ]; then
        echo ""
        echo "💡 Partially working setup - you may be able to run some components."
    fi
    
    exit 1
fi