package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/auth"
	"nuclear_pond/pkg/types"
	"strings"

	"github.com/go-chi/render"
)

// RefreshResponse represents the token refresh response
type RefreshResponse struct {
	Token   string `json:"token"`
	Message string `json:"message"`
}

// RefreshHandler handles JWT token refresh for authenticated users
func RefreshHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Token refresh request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	// Extract current token from Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
		log.Printf("Missing or invalid Authorization header from %s", r.RemoteAddr)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, types.Response{
			Error:   "Unauthorized",
			Message: "Missing or invalid Authorization header",
		})

		return
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")

	// Validate current token
	claims, err := auth.ValidateJWT(token)
	if err != nil {
		log.Printf("Invalid token for refresh from %s: %v", r.RemoteAddr, err)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, types.Response{
			Error:   "Unauthorized",
			Message: "Invalid token",
		})

		return
	}

	// Generate new token with same user ID
	newToken, err := auth.GenerateJWT(claims.UserID)
	if err != nil {
		log.Printf("Failed to generate refresh token: %v", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, types.Response{
			Error:   "Internal Server Error",
			Message: "Failed to generate refresh token",
		})

		return
	}

	log.Printf("Successful token refresh for user %s from %s", claims.UserID, r.RemoteAddr)
	render.JSON(w, r, RefreshResponse{
		Token:   newToken,
		Message: "Token refreshed successfully",
	})
}
