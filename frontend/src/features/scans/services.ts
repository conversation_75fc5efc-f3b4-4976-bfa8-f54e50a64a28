import { alovaClient } from "@/lib/api-client";
import type { Scan<PERSON>onfig, ScanRequest } from "@/types";

// Enhanced API response types to match backend
/*
{
    "scan_id": "44bdb218087f409c9b809416bc1b7242",
    "request_id": "44bdb218-087f-409c-9b80-9416bc1b7242",
    "status": "completed",
    "target_count": 1,
    "targets_s3_url": "s3://fastscan-nuclei-artifacts-local-us-east-1/scan-targets/44bdb218087f409c9b809416bc1b7242/targets.json",
    "batches": 1,
    "threads": 1,
    "output": "s3",
    "mode": "cloud",
    "args": "",
    "created_at": "2025-07-21T14:35:58+02:00",
    "updated_at": "2025-07-21T14:35:59+02:00",
    "completed_at": "2025-07-21T14:35:59+02:00",
    "ttl": 1753187758
}
*/
export interface BackendScanRequest {
  scan_id: string;
  request_id: string;
  status: "queued" | "running" | "completed" | "failed";
  target_count: number;
  templates: string[];
  targets_s3_url: string;
  targets?: string[]; // Optional field that might be populated
  args: string;
  mode: string;
  batches: number;
  threads: number;
  output: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  ttl: number;
  results_summary?: {
    total_findings: number;
    targets_scanned: number;
    duration_seconds: number;
  };
}

export interface BackendScanListResponse {
  scans: BackendScanRequest[];
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

export interface BackendScanStatusResponse {
  scan_id: string;
  request_id: string;
  status: string;
  config: {
    target_count: number;
    targets: string[];
    batches: number;
    threads: number;
    output: string;
    mode: string;
    args: string;
    templates?: string[];
  };
  created_at: string;
  updated_at: string;
  completed_at?: string;
  vulnerable_targets?: Record<string, string[]>; // target -> template IDs
  total_vulnerable_targets: number;
}

// API client for scans
export const scanApi = {
  // Initiate a new scan
  initiateScan: (config: ScanConfig) => {
    const requestBody: Record<string, unknown> = {
      Targets: config.targets,
      Templates: config.templates,
      Scanners: Math.max(1, Number(config.scanners || 1)),
    };

    return alovaClient.Post<{ RequestId: string }>("/scan", requestBody);
  },

  // Get scan status with comprehensive information
  getScanStatus: (scanId: string) => alovaClient.Get<BackendScanStatusResponse>(`/scan/${scanId}`),

  // Get all scans with optional filtering
  getAllScans: (options?: { limit?: number; status?: string }) => {
    const params = new URLSearchParams();
    if (options?.limit) params.append("limit", options.limit.toString());
    if (options?.status && options.status !== "all") params.append("status", options.status);

    const queryString = params.toString();
    return alovaClient.Get(`/scans${queryString ? `?${queryString}` : ""}`, {
      transform: (rawData: BackendScanListResponse) => {
        // Transform backend response to frontend format
        const transformedScans = rawData.scans.map((backendScan: BackendScanRequest) => {
          // Base scan request object
          const scanRequest: ScanRequest = {
            requestId: backendScan.request_id || "unknown",
            timestamp: backendScan.created_at || new Date().toISOString(),
            status: backendScan.status || "queued",
            config: {
              target_count: backendScan.target_count || 0,
              templates: backendScan.templates || [],
              targets: backendScan.targets || [], // Use targets if available
              batches: backendScan.batches || 0,
              threads: backendScan.threads || 0,
            },
          };

          // Add completedAt timestamp
          if (backendScan.completed_at) {
            scanRequest.completedAt = backendScan.completed_at;
          }

          // Add performance metrics if available (for completed scans)
          if (backendScan.status === "completed" && backendScan.results_summary) {
            scanRequest.performance = {
              durationSeconds: backendScan.results_summary.duration_seconds,
            };
          }

          return scanRequest;
        });

        return {
          scans: transformedScans,
          total: rawData.total,
          limit: rawData.limit,
          offset: rawData.offset,
          has_more: rawData.has_more,
        };
      },
    });
  },

  // Get targets for a specific scan from S3
  getScanTargets: (scanId: string) => alovaClient.Get<{ targets: string[] }>(`/scan/${scanId}/targets`),

  // Get dashboard metrics
  getDashboardMetrics: () => alovaClient.Get<DashboardMetricsResponse>("/dashboard/metrics"),
};

// Dashboard metrics response type
export interface DashboardMetricsResponse {
  total_scans: number;
  active_scans: number;
  targets_scanned: number;
  recent_scans: {
    scan_id: string;
    request_id: string;
    status: string;
    target_count: number;
    created_at: string;
    completed_at?: string;
  }[];
  recent_findings: unknown[];
  system_status: {
    nuclei: string;
    queue: string;
    api: string;
  };
}
