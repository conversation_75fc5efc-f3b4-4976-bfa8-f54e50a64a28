#!/bin/bash

# Build Lambda Artifacts - Main Orchestrator Script
# This script builds all Lambda artifacts consistently for the unified nuclei delivery system

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT
BUILD_DIR="${PROJECT_ROOT}/build"
ARTIFACTS_DIR="${BUILD_DIR}/artifacts"

source "${PROJECT_ROOT}/scripts/lib/logging.sh"

# Error handling
cleanup() {
    if [[ $? -ne 0 ]]; then
        log_error "Build failed. Cleaning up partial artifacts..."
        rm -rf "${BUILD_DIR}"
    fi
}

trap cleanup EXIT

# Validation functions
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check if required directories exist
    if [[ ! -d "${PROJECT_ROOT}/backend/templates" ]]; then
        log_error "Templates directory not found: ${PROJECT_ROOT}/backend/templates"
        return 1
    fi
    
    if [[ ! -d "${PROJECT_ROOT}/lambda-nuclei-scanner" ]]; then
        log_error "Lambda scanner directory not found: ${PROJECT_ROOT}/lambda-nuclei-scanner"
        return 1
    fi
    
    # Check if individual build scripts exist
    local scripts=(
        "build-nuclei-layer.sh"
        "build-templates-layer.sh"
        "build-lambda-function.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ ! -f "${SCRIPT_DIR}/${script}" ]]; then
            log_error "Required build script not found: ${SCRIPT_DIR}/${script}"
            return 1
        fi
        
        if [[ ! -x "${SCRIPT_DIR}/${script}" ]]; then
            log_error "Build script is not executable: ${SCRIPT_DIR}/${script}"
            return 1
        fi
    done
    
    log_success "Prerequisites validated"
}

validate_artifacts() {
    log_info "Validating created artifacts..."
    
    local artifacts=(
        "nuclei-layer.zip"
        "templates-layer.zip"
        "lambda-function.zip"
    )
    
    local all_valid=true
    
    for artifact in "${artifacts[@]}"; do
        local artifact_path="${ARTIFACTS_DIR}/${artifact}"
        
        if [[ ! -f "${artifact_path}" ]]; then
            log_error "Missing artifact: ${artifact_path}"
            all_valid=false
            continue
        fi
        
        # Check if zip file is valid
        if ! unzip -t "${artifact_path}" >/dev/null 2>&1; then
            log_error "Invalid zip file: ${artifact_path}"
            all_valid=false
            continue
        fi
        
        # Check file size (should be > 0)
        local size=$(stat -f%z "${artifact_path}" 2>/dev/null || stat -c%s "${artifact_path}" 2>/dev/null || echo "0")
        if [[ "${size}" -eq 0 ]]; then
            log_error "Empty artifact: ${artifact_path}"
            all_valid=false
            continue
        fi
        
        log_success "Validated artifact: ${artifact} (${size} bytes)"
    done
    
    if [[ "${all_valid}" != "true" ]]; then
        log_error "Artifact validation failed"
        return 1
    fi
    
    log_success "All artifacts validated successfully"
}

# Main build function
build_artifacts() {
    log_info "Starting Lambda artifacts build process..."
    
    # Create build directories
    mkdir -p "${BUILD_DIR}"
    mkdir -p "${ARTIFACTS_DIR}"
    
    # Build nuclei layer
    log_info "Building Nuclei binary layer..."
    if ! "${SCRIPT_DIR}/build-nuclei-layer.sh" "${ARTIFACTS_DIR}"; then
        log_error "Failed to build Nuclei layer"
        return 1
    fi
    log_success "Nuclei layer built successfully"
    
    # Build templates layer
    log_info "Building templates layer..."
    if ! "${SCRIPT_DIR}/build-templates-layer.sh" "${ARTIFACTS_DIR}"; then
        log_error "Failed to build templates layer"
        return 1
    fi
    log_success "Templates layer built successfully"
    
    # Build lambda function
    log_info "Building Lambda function..."
    if ! "${SCRIPT_DIR}/build-lambda-function.sh" "${ARTIFACTS_DIR}"; then
        log_error "Failed to build Lambda function"
        return 1
    fi
    log_success "Lambda function built successfully"
}

# Print build summary
print_summary() {
    log_info "Build Summary:"
    echo "=================="
    
    if [[ -d "${ARTIFACTS_DIR}" ]]; then
        for artifact in "${ARTIFACTS_DIR}"/*.zip; do
            if [[ -f "${artifact}" ]]; then
                local name=$(basename "${artifact}")
                local size=$(stat -f%z "${artifact}" 2>/dev/null || stat -c%s "${artifact}" 2>/dev/null || echo "0")
                local size_mb=$((size / 1024 / 1024))
                echo "  ${name}: ${size_mb}MB"
            fi
        done
    fi
    
    echo "=================="
    echo "Artifacts location: ${ARTIFACTS_DIR}"
}

# Main execution
main() {
    log_info "Lambda Artifacts Build Orchestrator"
    log_info "Project root: ${PROJECT_ROOT}"
    log_info "Build directory: ${BUILD_DIR}"
    
    # Validate prerequisites
    if ! validate_prerequisites; then
        log_error "Prerequisites validation failed"
        exit 1
    fi
    
    # Clean previous build
    if [[ -d "${BUILD_DIR}" ]]; then
        log_info "Cleaning previous build artifacts..."
        rm -rf "${BUILD_DIR}"
    fi
    
    # Build all artifacts
    if ! build_artifacts; then
        log_error "Build process failed"
        exit 1
    fi
    
    # Validate created artifacts
    if ! validate_artifacts; then
        log_error "Artifact validation failed"
        exit 1
    fi
    
    # Print summary
    print_summary
    
    log_success "All Lambda artifacts built successfully!"
}

# Execute main function
main "$@"