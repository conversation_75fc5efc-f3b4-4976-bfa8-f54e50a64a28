package outputs

import (
	"net/url"
	"strings"
)

// SummarizeFindings computes a results summary.
type ResultsSummary struct {
	TotalFindings     int   `json:"total_findings"`
	TargetsScanned    int   `json:"targets_scanned"`
	VulnerableTargets int   `json:"vulnerable_targets"`
	DurationSeconds   int64 `json:"duration_seconds"`
}

// SummarizeFindings computes summary using the exact target list; a finding
// contributes to a target if its URL begins with the target string.
func SummarizeFindings(findings []map[string]any, targets []string, durationSeconds int64) *ResultsSummary {
	s := &ResultsSummary{TotalFindings: len(findings), TargetsScanned: len(targets), DurationSeconds: durationSeconds}
	if len(targets) == 0 || len(findings) == 0 {
		return s
	}

	// Normalize targets for prefix comparison
	normalizedTargets := make([]string, len(targets))
	for i, t := range targets {
		normalizedTargets[i] = normalizeTarget(t)
	}

	vulnerableByTarget := make(map[string]bool)
	for _, f := range findings {
		fu := extractBestURL(f)
		if fu == "" {
			continue
		}
		fuNorm := normalizeURLForMatch(fu)
		for idx, nt := range normalizedTargets {
			if strings.HasPrefix(fuNorm, nt) {
				vulnerableByTarget[targets[idx]] = true

				break
			}
		}
	}

	s.VulnerableTargets = len(vulnerableByTarget)

	return s
}

// SummarizeFindingsFromCount retains legacy behavior based on a target count only.
func SummarizeFindingsFromCount(findings []map[string]any, targetCount int, durationSeconds int64) *ResultsSummary {
	s := &ResultsSummary{TotalFindings: len(findings), TargetsScanned: targetCount, DurationSeconds: durationSeconds}
	if len(findings) == 0 {
		return s
	}
	vt := make(map[string]bool)
	for _, f := range findings {
		if target := extractTargetFromFinding(f); target != "" {
			vt[target] = true
		}
	}
	s.VulnerableTargets = len(vt)

	return s
}

func extractBestURL(finding map[string]any) string {
	if s, ok := finding["matched-at"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["url"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["target"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["host"].(string); ok && s != "" {
		return s
	}
	if s, ok := finding["ip"].(string); ok && s != "" {
		return s
	}

	return ""
}

func normalizeTarget(t string) string {
	u, err := url.Parse(t)
	if err != nil || u.Scheme == "" {
		// Fallback simple normalization
		if !strings.HasSuffix(t, "/") {
			return t + "/"
		}

		return t
	}
	// Ensure path ends with slash for prefix comparisons
	if !strings.HasSuffix(u.Path, "/") {
		u.Path = u.Path + "/"
	}

	return u.Scheme + "://" + u.Host + u.Path
}

func normalizeURLForMatch(s string) string {
	u, err := url.Parse(s)
	if err != nil || u.Scheme == "" {
		if !strings.HasSuffix(s, "/") {
			return s + "/"
		}

		return s
	}
	// Ensure path ends with slash to align with normalizeTarget
	if !strings.HasSuffix(u.Path, "/") {
		u.Path = u.Path + "/"
	}

	return u.Scheme + "://" + u.Host + u.Path
}

// extractTargetFromFinding extracts the target identifier from a finding
func extractTargetFromFinding(finding map[string]any) string {
	if host, ok := finding["host"].(string); ok && host != "" {
		return host
	}
	if url, ok := finding["url"].(string); ok && url != "" {
		return url
	}
	if ip, ok := finding["ip"].(string); ok && ip != "" {
		return ip
	}
	if target, ok := finding["target"].(string); ok && target != "" {
		return target
	}

	return ""
}
