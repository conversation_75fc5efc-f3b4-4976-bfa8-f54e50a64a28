#!/usr/bin/env bash

# ==============================================================================
# FastScan Full Stack Development Environment Startup
# ==============================================================================
# This script starts the complete FastScan LocalStack

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --reset             Reset LocalStack data before starting"
    echo "  --force-restart     Force restart of all services"
    echo "  --help              Show this help message"
}

check_prerequisites() {
    log_header "Checking Prerequisites"
    require_tool "docker"
    require_tool "docker-compose"
    require_tool "go"
    log_success "All prerequisites met"
}

start_localstack() {
    log_header "Starting LocalStack Services"
    local setup_script="${PROJECT_ROOT}/scripts/tasks/docker/setup-local-development.sh"
    
    if [ ! -f "$setup_script" ]; then
        fail "LocalStack setup script not found: $setup_script"
    fi
    
    log_info "Starting LocalStack and setting up resources..."
    if ! bash "$setup_script" --create-layers; then
        fail "LocalStack setup failed"
    fi
    log_success "LocalStack started and resources configured"
}

reset_localstack() {
    log_header "Resetting LocalStack Environment"
    (
        cd "$PROJECT_ROOT"
        log_info "Stopping LocalStack services..."
        docker-compose down localstack
        
        log_info "Removing LocalStack data..."
        if [ -d "localstack-data" ]; then
            sudo rm -rf localstack-data/*
            log_success "LocalStack data cleared"
        fi
    )
    log_info "Restarting LocalStack with fresh setup..."
    start_localstack
}

display_environment_info() {
    log_header "FastScan Development Environment"
    echo "Services:"
    echo "  LocalStack:  http://localhost:4566"
}

stop_all_services() {
    log_header "Stopping All Services"
    
    log_info "Stopping LocalStack services..."
    (cd "$PROJECT_ROOT" && docker-compose down localstack) || true
    
    log_success "All services stopped"
}

main() {
    local RESET=false
    local FORCE_RESTART=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --reset) RESET=true; shift ;;
            --force-restart) FORCE_RESTART=true; shift ;;
            --help) usage; exit 0 ;;
            *) fail "Unknown option: $1" ;;
        esac
    done
    
    # Don't set trap here - we'll handle cleanup manually
    
    log_header "FastScan Full Stack Development Environment"
    check_prerequisites
    
    # Force restart if requested
    if [ "$FORCE_RESTART" = "true" ]; then
        stop_all_services
    fi
    
    if [ "$RESET" = "true" ]; then
        reset_localstack
    fi
    start_localstack
    
    display_environment_info
    log_success "FastScan LocalStack development environment is ready!"
    log_info "Press Ctrl+C to stop all services"
    
    # Create a simple foreground process that can be interrupted with Ctrl+C
    # This keeps the script running without immediately triggering the cleanup
    trap 'echo -e "\nReceived interrupt signal"; stop_all_services; exit 0' INT TERM
    
    # Keep script alive until user interrupts
    while true; do
        sleep 60 &
        wait $!
    done
}

main "$@"