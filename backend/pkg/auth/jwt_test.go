package auth

import (
	"os"
	"testing"
	"time"
)

func TestGenerateAndValidateJWT(t *testing.T) {
	// Set test JWT secret
	os.Setenv("JWT_SECRET", "test-secret-for-jwt-testing")
	defer os.Unsetenv("JWT_SECRET")

	userID := "test-user"

	// Generate JWT token
	token, err := GenerateJWT(userID)
	if err != nil {
		t.Fatalf("Failed to generate JWT token: %v", err)
	}

	if token == "" {
		t.Fatal("Generated token is empty")
	}

	// Validate JWT token
	claims, err := ValidateJWT(token)
	if err != nil {
		t.Fatalf("Failed to validate JWT token: %v", err)
	}

	// Check claims
	if claims.UserID != userID {
		t.Errorf("Expected UserID %s, got %s", userID, claims.UserID)
	}

	if claims.Issuer != "fastscan-demo" {
		t.<PERSON><PERSON>("Expected issuer 'fastscan-demo', got %s", claims.Issuer)
	}

	if claims.Subject != userID {
		t.<PERSON>rrorf("Expected subject %s, got %s", userID, claims.Subject)
	}

	// Check expiration (should be ~24 hours from now)
	expectedExpiry := time.Now().Add(23 * time.Hour) // Allow 1 hour margin
	if claims.ExpiresAt.Time.Before(expectedExpiry) {
		t.Errorf("Token expires too soon: %v", claims.ExpiresAt.Time)
	}
}

func TestValidateInvalidJWT(t *testing.T) {
	// Set test JWT secret
	os.Setenv("JWT_SECRET", "test-secret-for-jwt-testing")
	defer os.Unsetenv("JWT_SECRET")

	// Test with invalid token
	_, err := ValidateJWT("invalid-token")
	if err == nil {
		t.Fatal("Expected error for invalid token, got nil")
	}

	// Test with empty token
	_, err = ValidateJWT("")
	if err == nil {
		t.Fatal("Expected error for empty token, got nil")
	}
}

func TestJWTWithDifferentSecrets(t *testing.T) {
	// Generate token with one secret
	os.Setenv("JWT_SECRET", "secret-one")
	token, err := GenerateJWT("test-user")
	if err != nil {
		t.Fatalf("Failed to generate JWT token: %v", err)
	}

	// Try to validate with different secret
	os.Setenv("JWT_SECRET", "secret-two")
	_, err = ValidateJWT(token)
	if err == nil {
		t.Fatal("Expected error when validating token with different secret, got nil")
	}

	// Clean up
	os.Unsetenv("JWT_SECRET")
}
