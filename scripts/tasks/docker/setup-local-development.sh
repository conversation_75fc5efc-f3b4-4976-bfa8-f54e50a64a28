#!/usr/bin/env bash

# ==============================================================================
# Complete Local Development Environment Setup
# ==============================================================================
# This script sets up the complete local development environment including
# LocalStack services, AWS resources, and Lambda function deployment.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

S3_BUCKET="fastscan-nuclei-artifacts-local-us-east-1"

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --skip-docker     Skip Docker Compose startup (assume LocalStack is running)"
    echo "  --skip-lambda     Skip Lambda function deployment"
    echo "  --test-lambda     Test Lambda function after deployment"
    echo "  --create-layers   Create Lambda layers before deployment"
    echo "  --reset           Reset LocalStack data and restart services"
    echo "  --help            Show this help message"
}

check_prerequisites() {
    log_header "Checking Prerequisites"
    require_tool "docker"
    require_tool "docker-compose"
    require_tool "awslocal"

    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        fail "docker-compose.yml not found in project root"
    fi
    
    log_success "All prerequisites met"
}

start_localstack() {
    log_header "Starting LocalStack Services"
    (
        cd "$PROJECT_ROOT"
        if docker-compose ps localstack | grep -q "Up"; then
            log_info "LocalStack is already running"
            return 0
        fi
        
        log_info "Starting LocalStack with Docker Compose..."
        if ! docker-compose up -d localstack; then
            fail "Failed to start LocalStack"
        fi
        log_success "LocalStack started successfully"
    )
    sleep 3
}

reset_localstack() {
    log_header "Resetting LocalStack Environment"
    (
        cd "$PROJECT_ROOT"
        log_info "Stopping LocalStack services..."
        docker-compose down localstack
        
        log_info "Removing LocalStack data..."
        if [ -d "localstack-data" ]; then
            sudo rm -rf localstack-data/*
            log_success "LocalStack data cleared"
        fi
        
        log_info "Restarting LocalStack..."
        docker-compose up -d localstack
    )
    sleep 5
    log_success "LocalStack reset completed"
}

setup_aws_resources() {
    log_header "Setting Up AWS Resources"
    local setup_script="${PROJECT_ROOT}/scripts/tasks/aws/setup-resources.sh"
    
    if [ ! -f "$setup_script" ]; then
        fail "Resource setup script not found: $setup_script"
    fi
    
    if ! bash "$setup_script"; then
        fail "AWS resources setup failed"
    fi
    log_success "AWS resources setup completed"
}

generate_template_metadata() {
    log_header "Generating Template Metadata"
    local metadata_script="${PROJECT_ROOT}/scripts/tasks/metadata/generate-template-metadata.sh"
    
    if [ ! -f "$metadata_script" ]; then
        log_warn "Template metadata generator script not found: $metadata_script"
        log_warn "Skipping template metadata generation"
        return 0
    fi
    
    log_info "Running template metadata generator..."
    if ! bash "$metadata_script" --bucket "$S3_BUCKET" --local; then
        log_warn "Template metadata generation failed, but continuing setup"
        return 0
    fi
    log_success "Template metadata generation completed"
}

deploy_lambda() {
    log_header "Deploying Lambda Function"
    local deploy_script="${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh"
    
    if [ ! -f "$deploy_script" ]; then
        fail "Lambda deployment script not found: $deploy_script"
    fi
    
    local deploy_flags="--create-layers"
    if [ "${TEST_LAMBDA:-false}" = "true" ]; then
        deploy_flags="$deploy_flags --test"
    fi

    if ! bash "$deploy_script" $deploy_flags; then
        fail "Lambda deployment failed"
    fi
    log_success "Lambda deployment completed"
}

display_status() {
    log_header "Local Development Environment Status"
    "${PROJECT_ROOT}/scripts/tasks/verification/check-health.sh"
    
    echo ""
    log_info "Environment URLs:"
    echo "  LocalStack Dashboard: http://localhost:4566"
    echo "  LocalStack Health:    http://localhost:4566/health"
    echo ""
    log_info "Next steps:"
    echo "  1. Set environment variables for local development:"
    echo "  2. Start the backend:"
    echo "     devbox run backend"
    echo "  3. Start the frontend:"
    echo "     devbox run frontend"
}

main() {
    local SKIP_DOCKER=false
    local SKIP_LAMBDA=false
    local TEST_LAMBDA=false
    local RESET=false
    local CREATE_LAYERS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-docker) SKIP_DOCKER=true; shift ;;
            --skip-lambda) SKIP_LAMBDA=true; shift ;;
            --test-lambda) TEST_LAMBDA=true; shift ;;
            --reset) RESET=true; shift ;;
            --create-layers) CREATE_LAYERS=true; shift ;;
            --help) usage; exit 0 ;;
            *) fail "Unknown option: $1" ;;
        esac
    done
    
    export TEST_LAMBDA
    export CREATE_LAYERS
    
    log_header "FastScan Local Development Environment Setup"
    check_prerequisites
    
    if [ "$RESET" = "true" ]; then
        reset_localstack
    fi
    
    if [ "$SKIP_DOCKER" = "false" ]; then
        start_localstack
    fi
    
    setup_aws_resources
    
    if [ "$SKIP_LAMBDA" = "false" ]; then
        deploy_lambda
        # Generate template metadata after Lambda deployment
        generate_template_metadata
    fi
    
    display_status
    log_success "Local development environment setup completed!"
}

main "$@"