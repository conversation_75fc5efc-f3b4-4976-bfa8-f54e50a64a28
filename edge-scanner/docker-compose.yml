version: '3.8'

services:
  edge-scanner:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: edge-scanner
    environment:
      - API_ENDPOINT=http://api.edge.software-development.biz
      - WORKER_COUNT=10
      - TASK_CHANNEL_SIZE=100
      - RESULTS_DIR=results
      - NUCLEI_TEMPLATE_ID=CVE-2023-42793
    volumes:
      - ./results:/app/results
    restart: unless-stopped
    networks:
      - evidence-network

networks:
  evidence-network:
    external: true

