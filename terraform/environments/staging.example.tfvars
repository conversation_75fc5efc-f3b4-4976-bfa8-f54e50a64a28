# ==============================================================================
# STAGING ENVIRONMENT CONFIGURATION
# ==============================================================================

# Core configuration
project_name = "fastscan"
environment = "staging"

# ==============================================================================
# GOVERNANCE AND TAGGING
# ==============================================================================

owner = "security-team"
cost_center = "security"
business_unit = "security"

# Staging environment policies
backup_policy = "weekly"
monitoring_policy = "enhanced"
compliance_framework = "none"

# Resource discovery
enable_resource_discovery = true

# ==============================================================================
# INFRASTRUCTURE CONFIGURATION
# ==============================================================================

# Module enablement
enable_pow_targets = true  # Enable for testing

# Network configuration (production-like)
vpc_cidr = "10.0.0.0/16"
public_subnet_az1_cidr = "10.0.1.0/24"
public_subnet_az2_cidr = "10.0.2.0/24"
private_subnet_az1_cidr = "10.0.3.0/24"


# Lambda configuration (production-like)
nuclei_version = "3.1.7"
nuclei_arch = "linux_amd64"
nuclei_timeout = 55  # 55 seconds, to meet the deadline
memory_size = 512

# ECS configuration (production-like but smaller)
nuclear_pond_task_cpu = "512"
nuclear_pond_task_memory = "2048"
nuclear_pond_desired_count = 1
nuclear_pond_container_port = 8082
nuclear_pond_health_check_path = "/health-check"
nuclear_pond_log_retention_days = 30
nuclear_pond_enable_deletion_protection = false

# Authentication configuration
jwt_secret = "staging-jwt-secret-change-in-production"
demo_password = "StagingTestPass123"

# Frontend configuration
enable_frontend_cloudfront = true  # Test CDN functionality
frontend_domain_name = "try.rootevidence.com"
frontend_cloudfront_price_class = "PriceClass_100"

# PoW targets configuration
pow_create_hosted_zone = true
pow_domain_name = "fastscan-staging-demo.click"

# GitHub integration
github_token = ""
github_repository = "your-org/fastscan"

# Isolate Lambda function
enable_vpc_isolation = false

# ==============================================================================
# CUSTOM TAGS
# ==============================================================================

tags = {
  CreatedBy = "terraform"
  Purpose = "staging-testing"
  AutoShutdown = "scheduled"
  Team = "security-staging"
  Testing = "true"
  PreProd = "true"
} 