#!/usr/bin/env bash

# ==============================================================================
# Terraform Post-Apply Hook: Generate Template Metadata
# ==============================================================================
# This script is executed after Terraform apply to generate template metadata

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# Source common utilities if they exist
if [ -f "${PROJECT_ROOT}/scripts/lib/logging.sh" ]; then
  source "${PROJECT_ROOT}/scripts/lib/logging.sh"
else
  # Simple logging fallbacks if the library doesn't exist
  log_header() { echo -e "\n=== $1 ==="; }
  log_info() { echo -e "[INFO] $1"; }
  log_success() { echo -e "[SUCCESS] $1"; }
  log_warn() { echo -e "[WARNING] $1"; }
  fail() { echo -e "[ERROR] $1"; exit 1; }
fi

# Get the bucket name from Terraform output
get_bucket_name() {
  local tf_dir="$1"
  local output_name="$2"
  
  if [ ! -d "$tf_dir" ]; then
    fail "Terraform directory not found: $tf_dir"
  fi
  
  (
    cd "$tf_dir"
    if ! terraform output -json "$output_name" 2>/dev/null | jq -r '.' 2>/dev/null; then
      log_warn "Could not get bucket name from Terraform output: $output_name"
      echo ""
    fi
  )
}

run_metadata_generator() {
  local bucket="$1"
  
  if [ -z "$bucket" ]; then
    log_warn "No bucket name provided, skipping metadata generation"
    return 0
  fi
  
  log_header "Generating Template Metadata"
  
  local metadata_script="${PROJECT_ROOT}/scripts/tasks/aws/update-template-metadata.sh"
  
  if [ ! -f "$metadata_script" ]; then
    log_warn "Template metadata generator script not found: $metadata_script"
    log_warn "Skipping template metadata generation"
    return 0
  fi
  
  log_info "Running template metadata generator for bucket: $bucket"
  if ! bash "$metadata_script" --bucket "$bucket"; then
    log_warn "Template metadata generation failed, but continuing deployment"
    return 0
  fi
  
  log_success "Template metadata generation completed"
}

main() {
  log_header "Post-Apply Hook: Generate Template Metadata"
  
  # Try to get bucket name from Terraform output
  local nuclei_lambda_dir="${PROJECT_ROOT}/terraform/nuclei_lambda"
  local bucket_name=$(get_bucket_name "$nuclei_lambda_dir" "templates_bucket_name")
  
  if [ -z "$bucket_name" ]; then
    log_warn "Could not determine templates bucket name from Terraform output"
    log_warn "Checking environment variable NUCLEI_TEMPLATES_BUCKET"
    
    bucket_name="${NUCLEI_TEMPLATES_BUCKET:-}"
    
    if [ -z "$bucket_name" ]; then
      log_warn "NUCLEI_TEMPLATES_BUCKET environment variable not set"
      log_warn "Skipping metadata generation"
      exit 0
    fi
  fi
  
  run_metadata_generator "$bucket_name"
}

main "$@"