#!/usr/bin/env bash

# Logging functions with support for log levels and timestamps.
# Usage: log "INFO" "This is an info message."

# --- Colors ---
NC='\033[0m' # No Color
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'

log() {
    local level="$1"
    local message="$2"
    local timestamp
    timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    local color="${NC}"

    case "$level" in
        INFO)
            color="${BLUE}"
            ;;
        SUCCESS)
            color="${GREEN}"
            ;;
        WARN)
            color="${YELLOW}"
            ;;
        ERROR)
            color="${RED}"
            ;;
        DEBUG)
            color="${CYAN}"
            ;;
        *)
            level="INFO"
            ;;
    esac

    echo -e "${color}[${level}]${NC} [${timestamp}] ${message}"
}

log_header() {
    local message="$1"
    echo
    echo -e "${CYAN}==============================================================================${NC}"
    echo -e "${CYAN}# ${message}${NC}"
    echo -e "${CYAN}==============================================================================${NC}"
    echo
}

# --- Convenience functions ---
log_info() { log "INFO" "$1"; }
log_success() { log "SUCCESS" "$1"; }
log_warn() { log "WARN" "$1"; }
log_error() { log "ERROR" "$1"; }
log_debug() { log "DEBUG" "$1"; }


# --- compatilibity with previous versions ---
info() { log_info "$1"; }
success() { log_success "$1"; }
warn() { log_warn "$1"; }
error() { log_error "$1"; }
debug() { log_debug "$1"; }
