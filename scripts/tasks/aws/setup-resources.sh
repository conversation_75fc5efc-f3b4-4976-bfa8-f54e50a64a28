#!/usr/bin/env bash

# ==============================================================================
# LocalStack Resource Initialization Script
# ==============================================================================
# This script waits for LocalStack to be ready and creates all required AWS 
# resources for local development, matching the production infrastructure.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

wait_for_localstack() {
    log_info "Waiting for LocalStack to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "${LOCALSTACK_ENDPOINT}/_localstack/health" | grep -q "available"; then
            log_success "LocalStack is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: LocalStack not ready yet, waiting 2 seconds..."
        sleep 2
        ((attempt++))
    done
    
    fail "LocalStack failed to start after $max_attempts attempts"
}

create_s3_bucket() {
    log_info "Creating S3 bucket: $S3_BUCKET"
    
    if awslocal s3 ls "s3://$S3_BUCKET" >/dev/null 2>&1; then
        log_success "S3 bucket $S3_BUCKET already exists, skipping creation"
        return 0
    fi
    
    if ! awslocal s3 mb "s3://$S3_BUCKET" --region "$AWS_REGION"; then
        fail "Failed to create S3 bucket $S3_BUCKET"
    fi
    log_success "S3 bucket $S3_BUCKET created successfully"
    
    log_info "Creating S3 folder structure..."
    echo "" | awslocal s3 cp - "s3://$S3_BUCKET/findings/.keep"
    echo "" | awslocal s3 cp - "s3://$S3_BUCKET/templates/.keep"
    echo "" | awslocal s3 cp - "s3://$S3_BUCKET/artifacts/.keep"
    log_success "S3 folder structure created"

    log_info "Uploading Nuclei templates to S3..."
    if ! awslocal s3 cp --recursive "${PROJECT_ROOT}/backend/templates" "s3://${S3_BUCKET}/templates/"; then
        fail "Failed to upload Nuclei templates to S3"
    fi
    log_success "Nuclei templates uploaded successfully"
}

create_dynamodb_table() {
    log_info "Creating DynamoDB table: $DYNAMODB_TABLE"
    
    if awslocal dynamodb describe-table --table-name "$DYNAMODB_TABLE" >/dev/null 2>&1; then
        log_success "DynamoDB table $DYNAMODB_TABLE already exists, skipping creation"
        return 0
    fi
    
    if ! awslocal dynamodb create-table \
        --table-name "$DYNAMODB_TABLE" \
        --attribute-definitions AttributeName=scan_id,AttributeType=S \
        --key-schema AttributeName=scan_id,KeyType=HASH \
        --billing-mode PAY_PER_REQUEST \
        --region "$AWS_REGION" >/dev/null; then
        fail "Failed to create DynamoDB table $DYNAMODB_TABLE"
    fi
    log_success "DynamoDB table $DYNAMODB_TABLE created successfully"
    
    log_info "Waiting for DynamoDB table to be active..."
    awslocal dynamodb wait table-exists --table-name "$DYNAMODB_TABLE"
    log_success "DynamoDB table is now active"
}

create_iam_resources() {
    log_info "Creating IAM role: $LAMBDA_ROLE_NAME"
    
    if awslocal iam get-role --role-name "$LAMBDA_ROLE_NAME" >/dev/null 2>&1; then
        log_success "IAM role $LAMBDA_ROLE_NAME already exists, skipping creation"
        return 0
    fi
    
    local trust_policy
    trust_policy=$(cat <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": { "Service": "lambda.amazonaws.com" },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
)
    
    if ! echo "$trust_policy" | awslocal iam create-role \
        --role-name "$LAMBDA_ROLE_NAME" \
        --assume-role-policy-document file:///dev/stdin >/dev/null; then
        fail "Failed to create IAM role $LAMBDA_ROLE_NAME"
    fi
    log_success "IAM role $LAMBDA_ROLE_NAME created successfully"
    
    log_info "Creating Lambda execution policy..."
    local lambda_policy
    lambda_policy=$(cat <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"],
            "Resource": "arn:aws:logs:${AWS_REGION}:000000000000:log-group:/aws/lambda/${LAMBDA_FUNCTION_NAME}*"
        },
        {
            "Effect": "Allow",
            "Action": ["s3:PutObject", "s3:PutObjectAcl"],
            "Resource": "arn:aws:s3:::${S3_BUCKET}/findings/*"
        },
        {
            "Effect": "Allow",
            "Action": "s3:GetObject",
            "Resource": [
                "arn:aws:s3:::${S3_BUCKET}/nuclei.zip",
                "arn:aws:s3:::${S3_BUCKET}/nuclei-configs.zip",
                "arn:aws:s3:::${S3_BUCKET}/templates/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": "s3:ListBucket",
            "Resource": "arn:aws:s3:::${S3_BUCKET}",
            "Condition": { "StringLike": { "s3:prefix": ["findings/*", "templates/*"] } }
        }
    ]
}
EOF
)
    
    local policy_name="${PROJECT_NAME}-nuclei-lambda-policy"
    
    if ! echo "$lambda_policy" | awslocal iam create-policy \
        --policy-name "$policy_name" \
        --policy-document file:///dev/stdin >/dev/null; then
        fail "Failed to create Lambda execution policy"
    fi
    log_success "Lambda execution policy created successfully"
    
    if ! awslocal iam attach-role-policy \
        --role-name "$LAMBDA_ROLE_NAME" \
        --policy-arn "arn:aws:iam::000000000000:policy/$policy_name" >/dev/null; then
        fail "Failed to attach policy to role"
    fi
    log_success "Policy attached to role successfully"
}

verify_resources() {
    log_info "Verifying created resources..."
    local errors=0
    
    awslocal s3 ls "s3://$S3_BUCKET" >/dev/null 2>&1 || { log_error "✗ S3 bucket $S3_BUCKET is not accessible"; ((errors++)); }
    awslocal dynamodb describe-table --table-name "$DYNAMODB_TABLE" >/dev/null 2>&1 || { log_error "✗ DynamoDB table $DYNAMODB_TABLE is not accessible"; ((errors++)); }
    awslocal iam get-role --role-name "$LAMBDA_ROLE_NAME" >/dev/null 2>&1 || { log_error "✗ IAM role $LAMBDA_ROLE_NAME is not accessible"; ((errors++)); }
    
    if [ $errors -eq 0 ]; then
        log_success "All resources verified successfully!"
    else
        fail "$errors resource(s) failed verification"
    fi
}

display_resource_info() {
    log_info "LocalStack Resources Created:"
    echo "  S3 Bucket:       $S3_BUCKET"
    echo "  DynamoDB Table:  $DYNAMODB_TABLE"
    echo "  IAM Role:        $LAMBDA_ROLE_NAME"
    echo "  Lambda Function: $LAMBDA_FUNCTION_NAME (ready for deployment)"
}

main() {
    log_info "Starting LocalStack resource initialization..."
    require_tool "awslocal"
    require_tool "curl"
    
    wait_for_localstack
    
    create_s3_bucket
    create_dynamodb_table
    create_iam_resources
    
    verify_resources
    display_resource_info
    
    log_success "LocalStack resource initialization completed successfully!"
}

main "$@"