package scan

import (
	"log"
	"strings"
	"time"

	"nuclear_pond/pkg/aws"
	"nuclear_pond/pkg/core"
	"nuclear_pond/pkg/helpers"
	"nuclear_pond/pkg/outputs"
	"nuclear_pond/pkg/types"

	lambdapkg "nuclear_pond/pkg/lambda"
)

// BackgroundScan orchestrates a scan: persists request, invokes workers, aggregates results, and finalizes.
func BackgroundScan(scanInput types.Request, scanId string) {
	targets := helpers.RemoveEmpty(scanInput.Targets)
	batches := helpers.SplitSlice(targets, scanInput.Batches)

	// Convert scanId to a DynamoDB key (no dashes)
	requestId := strings.ReplaceAll(scanId, "-", "")

	now := time.Now()
	scanRequest := &types.ScanRequest{
		ScanID:      requestId,
		RequestID:   scanId,
		Status:      "running",
		TargetCount: len(targets),
		Batches:     scanInput.Batches,
		Threads:     scanInput.Threads,
		Templates:   scanInput.Templates,
		CreatedAt:   now,
		UpdatedAt:   now,
		TTL:         now.Add(24 * time.Hour).Unix(),
	}

	if err := storeScanRequest(scanRequest); err != nil {
		// If we can't store the initial request, do not proceed
		_ = updateScanStatus(requestId, "failed")

		return
	}

	// Validate AWS config presence for execution
	awsConfig := aws.NewAWSConfig()
	if awsConfig.LambdaFunction == "" || awsConfig.Region == "" || awsConfig.DynamoDBTable == "" {
		// Avoid log.Fatal in libraries to prevent process exit
		log.Printf("Invalid AWS config for scan %s: lambda=%q region=%q table=%q", scanId, awsConfig.LambdaFunction, awsConfig.Region, awsConfig.DynamoDBTable)
		_ = updateScanStatus(requestId, "failed")

		return
	}

	// Create a reusable Lambda invoker once per scan
	invoker, invErr := lambdapkg.NewClient(awsConfig.LambdaFunction)
	if invErr != nil {
		log.Printf("Failed to initialize Lambda client: %v", invErr)
		_ = updateScanStatus(requestId, "failed")

		return
	}

	// Measure execution duration
	execStart := time.Now()
	// Execute scan batches via Lambda-based executor
	batchResults, execErr := core.ExecuteScans(batches, invoker, scanInput.Threads, scanInput.Templates)
	if execErr != nil {
		log.Printf("ExecuteScans failed for %s: %v", scanId, execErr)
		_ = updateScanStatus(requestId, "failed")

		return
	}

	var allNormalized []map[string]any
	failedBatches := 0
	successfulBatches := 0

	for _, br := range batchResults {
		if strings.TrimSpace(br.Error) != "" {
			failedBatches++

			continue
		}
		normalized, _ := outputs.ValidateAndNormalizeFindings(br.Findings, 0)

		allNormalized = append(allNormalized, normalized...)
		successfulBatches++
	}

	if successfulBatches == 0 {
		log.Printf("No successful batches found for scan %s", scanId)
		_ = updateScanStatus(requestId, "failed")

		return
	}

	// Compute vulnerable targets using the original input targets directly
	var vulnerableTargets map[string][]string
	var totalVulnerableCount int
	if len(allNormalized) > 0 {
		vulnerableTargets, totalVulnerableCount = core.ProcessVulnerableTargetsWithErrorHandling(allNormalized, targets)
	} else {
		vulnerableTargets, totalVulnerableCount = map[string][]string{}, 0
	}

	// Persist results minimally: only vulnerable_targets and completion metadata
	if err := outputs.FinalizeScanDDB(requestId, time.Now(), vulnerableTargets, totalVulnerableCount); err != nil {
		log.Printf("Failed to finalize scan %s: %v", scanId, err)
		_ = updateScanStatus(requestId, "failed")

		return
	}

	durationSeconds := int64(time.Since(execStart).Seconds())
	log.Printf("Scan %s completed: findings=%d batches_ok=%d batches_err=%d duration=%ds", scanId, len(allNormalized), successfulBatches, failedBatches, durationSeconds)
}
