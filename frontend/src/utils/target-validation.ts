/**
 * Security utility to validate that targets are only demo targets
 * Only allows subdomains of fast-scan-demo-target.click
 */

const ALLOWED_DEMO_DOMAIN = 'fast-scan-demo-target.click';

/**
 * Validates if a target URL is a valid demo target
 * @param target - The target URL to validate
 * @returns true if the target is a valid demo target, false otherwise
 */
export function isValidDemoTarget(target: string): boolean {
  try {
    const url = new URL(target);
    const hostname = url.hostname.toLowerCase();
    
    // Allow exact match of the demo domain
    if (hostname === ALLOWED_DEMO_DOMAIN) {
      return true;
    }
    
    // Allow subdomains of the demo domain
    if (hostname.endsWith('.' + ALLOWED_DEMO_DOMAIN)) {
      return true;
    }
    
    return false;
  } catch (error) {
    // Invalid URL format
    return false;
  }
}

/**
 * Validates a list of targets to ensure all are demo targets
 * @param targets - Array of target URLs to validate
 * @returns Object with validation results
 */
export function validateDemoTargets(targets: string[]): {
  isValid: boolean;
  invalidTargets: string[];
  validTargets: string[];
} {
  const invalidTargets: string[] = [];
  const validTargets: string[] = [];
  
  for (const target of targets) {
    if (isValidDemoTarget(target)) {
      validTargets.push(target);
    } else {
      invalidTargets.push(target);
    }
  }
  
  return {
    isValid: invalidTargets.length === 0,
    invalidTargets,
    validTargets
  };
}

/**
 * Generates a demo target URL with a random subdomain
 * @returns A valid demo target URL
 */
export function generateDemoTarget(): string {
  const randomString = Math.random().toString(36).substring(2, 10);
  return `https://${randomString}.${ALLOWED_DEMO_DOMAIN}`;
}