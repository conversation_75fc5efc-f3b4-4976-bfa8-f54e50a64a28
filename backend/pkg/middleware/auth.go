package middleware

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/auth"
	"nuclear_pond/pkg/types"
	"strings"

	"github.com/go-chi/render"
)

// JWTMiddleware checks for a valid JWT token for authentication.
func JWTMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip authentication for public endpoints
		if r.URL.Path == "/health-check" || r.URL.Path == "/auth" {
			next.ServeHTTP(w, r)

			return
		}

		// Check for JWT token
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			if validateJWTToken(token) {
				next.ServeHTTP(w, r)

				return
			}
		}

		log.Printf("Unauthorized request %s %s from %s", r.<PERSON>, r.URL.Path, r.RemoteAddr)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, types.Response{
			Error:   "Unauthorized",
			Message: "Invalid or missing JWT token.",
		})
	})
}

// validateJWTToken validates a real JWT token with proper signature and expiration
func validateJWTToken(token string) bool {
	_, err := auth.ValidateJWT(token)
	if err != nil {
		log.Printf("JWT validation failed: %v", err)

		return false
	}

	return true
}
