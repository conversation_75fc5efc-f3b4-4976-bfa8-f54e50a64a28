import { TargetIcon, TrashIcon } from "lucide-react";
import { type UseFormReturn, useWatch } from "react-hook-form";
import { FixedSizeList as List } from "react-window";
import { useToast } from "@/hooks/use-toast";

interface TargetListProps {
  formMethods: UseFormReturn<{
    targets: string[];
    templates: string[];
  }>;
}

export const TargetList = ({ formMethods }: TargetListProps) => {
  const { setValue } = formMethods;
  const targets = useWatch({ name: "targets", control: formMethods.control });
  const { toast } = useToast();

  const removeTarget = (index: number) => {
    setValue(
      "targets",
      targets.filter((_, i) => i !== index),
    );
  };

  const clearTargets = () => {
    setValue("targets", []);
    toast({ title: "All Targets Cleared", description: "The target list has been emptied." });
  };

  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style} className="flex items-center justify-between p-2">
      <span className="truncate">{targets[index]}</span>
      <button type="button" className="btn btn-ghost btn-xs text-error" onClick={() => removeTarget(index)}>
        <TrashIcon className="h-3 w-3" />
      </button>
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-lg">Target List</h3>
        {targets.length > 0 && (
          <div className="badge badge-primary badge-lg">
            {targets.length} {targets.length === 1 ? "Target" : "Targets"}
          </div>
        )}
      </div>

      {targets.length > 0 ? (
        <div>
          <div className="h-64 w-full">
            <List height={256} itemCount={targets.length} itemSize={22} width="100%">
              {Row}
            </List>
          </div>
          <div className="card-actions justify-end pt-4 border-t border-base-300">
            <button type="button" className="btn btn-warning btn-sm" onClick={clearTargets}>
              <TrashIcon className="h-4 w-4" />
              Clear All
            </button>
          </div>
        </div>
      ) : (
        <div className="card bg-base-200 border-2 border-dashed border-base-300">
          <div className="card-body text-center py-12">
            <TargetIcon className="h-12 w-12 text-base-content/40 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-base-content/70 mb-2">No Targets Added</h3>
            <p className="text-base-content/60">Use one of the methods above to add targets for scanning.</p>
          </div>
        </div>
      )}
    </div>
  );
};
