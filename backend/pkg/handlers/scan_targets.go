package handlers

import (
	"log"
	"net/http"

	"github.com/go-chi/render"
)

// ScanTargetsHandler returns the list of targets for a given scan.
func ScanTargetsHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan targets request: %s %s", r.Method, r.URL.Path)

	requestID, scanID, err := parseScanID(r)
	if err != nil {
		writeAPIError(w, r, err)

		return
	}

	_, err = fetchScanOr404(requestID)
	if err != nil {
		log.Printf("Error getting scan details for %s: %v", scanID, err)
		writeAPIError(w, r, err)

		return
	}

	// Targets retrieval from S3 is deprecated in simplified flow; return empty list.
	render.JSON(w, r, map[string]interface{}{"targets": []string{}})
}
