# Edge Scanner - Local Development Guide

## 🚀 Quick Start

### Standalone Setup (edge-scanner only)
```bash
# 1. Set up development environment
./scripts/dev-setup.sh

# 2. Start mock API (in separate terminal)  
./scripts/run-mock-api.sh

# 3. Run scanner in development mode
./scripts/run-dev.sh
```

### Integrated Setup (with parent devbox.json)
```bash
# From the fast-scan root directory
devbox run edge:setup     # Set up development environment
devbox run edge:mock-api  # Start mock API
devbox run edge:dev       # Run scanner in development mode
```

## 📁 Development Structure

```
edge-scanner/
├── .env.example              # Environment template
├── scripts/                  # Development scripts
│   ├── dev-setup.sh         # Initial setup
│   ├── run-dev.sh           # Run with dev settings
│   ├── run-single.sh        # Single scan cycle
│   ├── run-mock-api.sh      # Start mock API
│   └── test-integration.sh  # Integration tests
├── mock-api/                 # Mock API configuration
│   ├── docker-compose.mock.yml
│   ├── mappings/            # WireMock request mappings
│   └── responses/           # Mock API responses
├── cmd/scanner/
│   ├── main.go             # Production entry point
│   └── main_dev.go         # Development entry point
└── results/                # Scan results (auto-created)
```

## 🛠️ Available Commands

### Standalone Commands (from edge-scanner/)
| Command | Description |
|---------|-------------|
| `./scripts/dev-setup.sh` | Initial development environment setup |
| `./scripts/run-dev.sh` | Run scanner with development settings |
| `./scripts/run-single.sh` | Run single scan cycle for testing |
| `./scripts/run-mock-api.sh` | Start mock API server |
| `./scripts/test-integration.sh` | Run integration tests |

### Devbox Commands (from fast-scan root)
| Command | Description |
|---------|-------------|
| `devbox run edge:setup` | Set up development environment |
| `devbox run edge:dev` | Run scanner in development mode |
| `devbox run edge:single` | Run single scan cycle |
| `devbox run edge:mock-api` | Start mock API server |
| `devbox run edge:test` | Run integration tests |
| `devbox run edge:stop` | Stop mock API server |

## ⚙️ Configuration

### Environment Variables (.env file)
```bash
# Core API configuration
API_ENDPOINT=http://localhost:8080  # Mock API endpoint
PRODUCT=edge_scan_dev               # Product identifier

# Worker configuration
WORKER_COUNT=5                      # Reduced for local testing
TASK_CHANNEL_SIZE=50
RESULTS_DIR=./results

# Nuclei configuration
NUCLEI_TEMPLATE_ID=CVE-2023-42793

# Development settings
DEBUG_MODE=true                     # Enable debug output
SCAN_INTERVAL_SECONDS=30            # Faster cycles for development
```

### Production Configuration
To test against the production API, update your `.env`:
```bash
API_ENDPOINT=https://api.edge.software-development.biz
PRODUCT=edge_scan
WORKER_COUNT=10
SCAN_INTERVAL_SECONDS=120
DEBUG_MODE=false
```

## 🧪 Testing Workflow

### 1. Run Integration Tests
```bash
./scripts/test-integration.sh
```
This validates:
- Environment setup
- Go dependencies
- Nuclei installation
- Docker functionality
- Mock API connectivity
- Configuration loading

### 2. Single Scan Cycle Test
```bash
./scripts/run-single.sh
```
Runs one complete scan cycle with debug output.

### 3. Full Development Testing
```bash
# Terminal 1: Start mock API
./scripts/run-mock-api.sh

# Terminal 2: Run scanner
./scripts/run-dev.sh

# Terminal 3: Monitor results
watch -n 2 'ls -la results/'
```

## 🎭 Mock API

The mock API simulates the production endpoints:

### Endpoints
- **GET** `/job/batch/next?queue=edge_scan_dev` - Returns test targets
- **POST** `/job/{id}/batch/{batchId}/product/{product}/result` - Accepts scan results

### Test Data
The mock returns these test IPs (hex format):
- `08080808` → `*******` (Google DNS)
- `01010101` → `*******` (Cloudflare DNS)  
- `0a000001` → `********` (Private IP)

### Mock API Management
```bash
# Start mock API
./scripts/run-mock-api.sh

# View logs
docker-compose -f mock-api/docker-compose.mock.yml logs -f

# Stop mock API
docker-compose -f mock-api/docker-compose.mock.yml down
```

## 🐛 Debugging

### Debug Mode Features
When `DEBUG_MODE=true`:
- Detailed logging of configuration
- Request/response logging
- Worker status information
- Scan timing details

### Common Issues

**Issue: Nuclei not found**
```bash
# Install Nuclei
go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@3.1.7

# Update templates
nuclei -update-templates
```

**Issue: Docker permissions**
```bash
# Fix Docker socket permissions
sudo chmod 666 /var/run/docker.sock
```

**Issue: Port 8080 already in use**
```bash
# Find process using port
lsof -i :8080

# Change mock API port in docker-compose.mock.yml
```

## 📊 Performance Tuning

### Development Settings
- **WORKER_COUNT**: `5` (vs 10 in production)
- **SCAN_INTERVAL_SECONDS**: `30` (vs 120 in production)
- **TASK_CHANNEL_SIZE**: `50` (vs 100 in production)

### Resource Monitoring
```bash
# Monitor Docker resources
docker stats edge-scanner-mock-api

# Monitor scan results
watch -n 2 'find results/ -name "*.json" | wc -l'

# Monitor Go processes
ps aux | grep "go run"
```

## 🚀 Production Validation

Before deploying to production:

1. **Test with production API** (temporarily):
   ```bash
   export API_ENDPOINT="https://api.edge.software-development.biz"
   ./scripts/run-single.sh
   ```

2. **Validate Docker build**:
   ```bash
   docker build -t edge-scanner:test .
   ```

3. **Performance test**:
   ```bash
   export WORKER_COUNT=10
   export SCAN_INTERVAL_SECONDS=120
   ./scripts/run-single.sh
   ```

## 📚 Architecture Notes

### Key Components
- **API Client** (`internal/apiclient/`) - Handles API communication
- **Port Scanner** (`internal/ports/`) - TCP port scanning
- **Nuclei Integration** (`internal/nuclei/`) - Vulnerability scanning
- **Worker Pool** (`internal/scanner/`) - Concurrent processing
- **Controller** (`internal/controller/`) - Orchestrates scan cycles

### Data Flow
1. Fetch targets from API endpoint
2. Convert hex IP addresses to readable format
3. Distribute scanning tasks to worker pool
4. Run Nuclei scans on targets
5. Save results as JSON files
6. Upload results back to API
7. Clean up local files
8. Wait for next scan interval

This setup provides a robust, configurable development environment that closely mirrors production while being safe and efficient for local testing.