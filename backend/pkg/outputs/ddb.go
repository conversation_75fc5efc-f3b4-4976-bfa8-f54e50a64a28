package outputs

import (
	"fmt"
	"nuclear_pond/pkg/aws"
	"time"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/dynamodb"
)

// AppendBatchResultDDB updates a scan record with a new batch S3 key and increments received count.
func AppendBatchResultDDB(scanID, batchS3Key string) error {
	awsConfig := aws.NewAWSConfig()
	sess, err := awsConfig.CreateSession()
	if err != nil {
		return err
	}
	svc := dynamodb.New(sess)

	updateExpr := "SET results_updated_at = :updated_at, findings_keys = list_append(if_not_exists(findings_keys, :empty_list), :new_key) ADD results_batches_received :one"
	input := &dynamodb.UpdateItemInput{
		TableName:        awssdk.String(awsConfig.DynamoDBTable),
		Key:              map[string]*dynamodb.AttributeValue{"scan_id": {S: awssdk.String(scanID)}},
		UpdateExpression: awssdk.String(updateExpr),
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":updated_at": {S: awssdk.String(time.Now().Format(time.RFC3339))},
			":empty_list": {L: []*dynamodb.AttributeValue{}},
			":new_key":    {L: []*dynamodb.AttributeValue{{S: awssdk.String(batchS3Key)}}},
			":one":        {N: awssdk.String("1")},
		},
		ConditionExpression: awssdk.String("attribute_exists(scan_id)"),
	}
	_, err = svc.UpdateItem(input)

	return err
}

// FinalizeScanDDB writes only vulnerable_targets and completion metadata; marks scan completed.
func FinalizeScanDDB(scanID string, completedAt time.Time, vulnerableTargets map[string][]string, totalVulnerableCount int) error {
	awsConfig := aws.NewAWSConfig()
	sess, err := awsConfig.CreateSession()
	if err != nil {
		return err
	}
	svc := dynamodb.New(sess)

	// Build vulnerable_targets map for DynamoDB
	vtMap := map[string]*dynamodb.AttributeValue{}
	for target, templateIDs := range vulnerableTargets {
		list := make([]*dynamodb.AttributeValue, len(templateIDs))
		for i, id := range templateIDs {
			list[i] = &dynamodb.AttributeValue{S: awssdk.String(id)}
		}
		vtMap[target] = &dynamodb.AttributeValue{L: list}
	}

	updateExpr := "SET #status = :completed, vulnerable_targets = :vt, updated_at = :updated_at, completed_at = :completed_at, total_vulnerable_targets = :total_vulnerable_targets"
	input := &dynamodb.UpdateItemInput{
		TableName:        awssdk.String(awsConfig.DynamoDBTable),
		Key:              map[string]*dynamodb.AttributeValue{"scan_id": {S: awssdk.String(scanID)}},
		UpdateExpression: awssdk.String(updateExpr),
		ExpressionAttributeNames: map[string]*string{
			"#status": awssdk.String("status"),
		},
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":completed":                {S: awssdk.String("completed")},
			":vt":                       {M: vtMap},
			":updated_at":               {S: awssdk.String(time.Now().Format(time.RFC3339))},
			":completed_at":             {S: awssdk.String(completedAt.Format(time.RFC3339))},
			":total_vulnerable_targets": {N: awssdk.String(fmt.Sprintf("%d", totalVulnerableCount))},
		},
		ConditionExpression: awssdk.String("attribute_exists(scan_id)"),
	}
	_, err = svc.UpdateItem(input)

	return err
}
