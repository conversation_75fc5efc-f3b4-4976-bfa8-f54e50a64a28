# FastScan Devbox Setup

## Overview

This guide provides a step-by-step setup to Jetify Devbox.


## Installation

### Step 1: Install Devbox

```bash
# Install Devbox
curl -fsSL https://get.jetify.com/devbox | bash

# Verify installation
devbox version
```

### Step 2: Use Devbox

```bash
# Start Devbox shell
devbox shell
```


## IDE-Specific Setup Instructions

### VS Code and forks

1. Install the Devbox extension (if available)
2. Open terminal in VS Code - it should automatically use Devbox shell
3. Extensions will work with Devbox-provided tools

## Troubleshooting

### Common Issues

1. **Package Not Found**:
   ```bash
   # Search for packages
   devbox search <package-name>
   
   # Or check nixhub.io
   ```
2. **Shell Integration**:
   ```bash
   # Re-generate shell integration
   devbox generate direnv --force
   ```

4. **Yarn/Node Issues**:
   ```bash
   # Clear Yarn cache
   devbox run -- yarn cache clean
   
   # Reinstall dependencies
   devbox run setup
   ```

### Performance Optimization

1. **Faster Package Installation**:
   ```bash
   # Configure Devbox cache
   devbox cache configure
   ```

2. **Preload Common Packages**:
   ```bash
   # Install globally used tools
   devbox global install go nodejs terraform awscli2
   ```

## Go Linting with golangci-lint

This repo includes `golangci-lint` via Devbox and a monorepo runner.

Commands (run inside Devbox shell):

```bash
# Lint all Go modules
devbox run go:lint

# Attempt auto-fix where safe
devbox run go:lint:fix
```

Notes:

- Global config: `.golangci.yml` at repo root
- Script: `scripts/tasks/go/lint.sh` discovers all `go.mod` and runs lint per module
- WSL2: ensure you run inside the Devbox shell so `golangci-lint` is on PATH
