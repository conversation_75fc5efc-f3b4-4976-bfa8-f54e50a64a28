#!/usr/bin/env bash

# ==============================================================================
# LocalStack Lambda Preparation Script
# ==============================================================================
# This script prepares the Lambda function for LocalStack by copying the nuclei binary
# directly into the Lambda function package, since LocalStack doesn't properly mount layers.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"
source "${PROJECT_ROOT}/scripts/config/development.conf"

# --- Paths & Names ---
readonly ARTIFACTS_DIR="${PROJECT_ROOT}/build/artifacts"
readonly LAMBDA_ZIP_FILE="${ARTIFACTS_DIR}/lambda-function.zip"
readonly NUCLEI_LAYER_ZIP="${ARTIFACTS_DIR}/nuclei-layer.zip"
readonly TEMP_DIR=$(mktemp -d)

cleanup() {
    if [[ -d "${TEMP_DIR}" ]]; then
        rm -rf "${TEMP_DIR}"
    fi
}

trap cleanup EXIT

prepare_lambda_for_localstack() {
    log_header "Preparing Lambda Function for LocalStack"
    
    log_info "Extracting nuclei binary from layer..."
    mkdir -p "${TEMP_DIR}/extract"
    unzip -q "${NUCLEI_LAYER_ZIP}" -d "${TEMP_DIR}/extract"
    
    if [[ ! -f "${TEMP_DIR}/extract/nuclei" ]]; then
        fail "Nuclei binary not found in layer at /nuclei"
    fi
    
    log_success "Nuclei binary extracted from layer"
    
    log_info "Creating directory structure for Lambda package..."
    mkdir -p "${TEMP_DIR}/lambda/opt"
    
    log_info "Copying nuclei binary to Lambda package..."
    cp "${TEMP_DIR}/extract/nuclei" "${TEMP_DIR}/lambda/opt/nuclei"
    chmod +x "${TEMP_DIR}/lambda/opt/nuclei"
    
    # Extract and copy templates
    log_info "Extracting templates from layer..."
    mkdir -p "${TEMP_DIR}/extract-templates"
    unzip -q "${ARTIFACTS_DIR}/templates-layer.zip" -d "${TEMP_DIR}/extract-templates"
    
    if [[ -d "${TEMP_DIR}/extract-templates/templates" ]]; then
        log_info "Copying templates to Lambda package..."
        mkdir -p "${TEMP_DIR}/lambda/opt/templates"
        cp -r "${TEMP_DIR}/extract-templates/templates/"* "${TEMP_DIR}/lambda/opt/templates/"
        log_success "Templates copied to Lambda package"
    else
        log_warning "Templates directory not found in layer"
    fi
    
    log_info "Extracting Lambda function package..."
    mkdir -p "${TEMP_DIR}/lambda-original"
    unzip -q "${LAMBDA_ZIP_FILE}" -d "${TEMP_DIR}/lambda-original"
    
    log_info "Copying Lambda function to package with nuclei binary..."
    cp "${TEMP_DIR}/lambda-original/bootstrap" "${TEMP_DIR}/lambda/bootstrap"
    
    log_info "Creating updated Lambda package..."
    cd "${TEMP_DIR}/lambda"
    zip -r "${TEMP_DIR}/updated-lambda.zip" . >/dev/null
    
    log_info "Backing up original Lambda package..."
    cp "${LAMBDA_ZIP_FILE}" "${LAMBDA_ZIP_FILE}.bak"
    
    log_info "Replacing Lambda package with updated version..."
    cp "${TEMP_DIR}/updated-lambda.zip" "${LAMBDA_ZIP_FILE}"
    
    log_success "Lambda function prepared for LocalStack with embedded nuclei binary"
}

main() {
    # Check if nuclei layer exists
    if [[ ! -f "${NUCLEI_LAYER_ZIP}" ]]; then
        fail "Nuclei layer zip not found: ${NUCLEI_LAYER_ZIP}"
    fi
    
    # Check if Lambda function exists
    if [[ ! -f "${LAMBDA_ZIP_FILE}" ]]; then
        fail "Lambda function zip not found: ${LAMBDA_ZIP_FILE}"
    fi
    
    prepare_lambda_for_localstack
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi