# Manual DNS Setup Guide

This guide explains how to set up DNS for your custom domain when using the FastScan frontend with CloudFront.

## Overview

The Terraform configuration has been simplified to use manual DNS management instead of automatic Route53 integration. This approach provides more flexibility and works with any DNS provider.

## Prerequisites

- You own a domain name
- You have access to your domain's DNS management interface
- You have deployed the frontend infrastructure with Terraform

## DNS Record Type Recommendation

**ALIAS records are strongly recommended over CNAME records** when available:
**Bottom line**: Use ALIAS if your DNS provider supports it (Route53, Cloudflare, etc.), otherwise use CNAME.

## Step-by-Step Setup

### 1. Deploy Infrastructure

First, deploy the frontend infrastructure:

```bash
cd terraform
terraform apply -var-file="environments/your-env.tfvars"
```

### 2. Get DNS Information

After deployment, Terraform will output the information you need for DNS setup:

```bash
terraform output cloudfront_domain_name_for_dns
terraform output acm_certificate_validation_records
```

### 3. Validate ACM Certificate

**Important**: You must validate the ACM certificate before your custom domain will work.

1. Go to the AWS Console → Certificate Manager (ACM)
2. Make sure you're in the **us-east-1** region (required for CloudFront certificates)
3. Find your certificate (it will be in "Pending validation" status)
4. Click on the certificate to see the validation records

Add the validation records to your DNS:
- **Type**: CNAME
- **Name**: The validation record name (e.g., `_abc123def456.yourdomain.com`)
- **Value**: The validation record value (e.g., `_xyz789.acm-validations.aws.`)

**Wait for validation**: This usually takes 5-10 minutes. The certificate status will change to "AWS Issued" when complete.

### 4. Create Main Domain Record

Once the certificate is validated, create the main domain record:

**For Route53 (Recommended - ALIAS record):**
- **Type**: A (ALIAS)
- **Name**: Your domain (e.g., `app.yourdomain.com` or `@` for root domain)
- **Alias Target**: The CloudFront distribution domain name
- **Evaluate Target Health**: No

**For other DNS providers (CNAME record):**
- **Type**: CNAME
- **Name**: Your domain (e.g., `app.yourdomain.com`)
- **Value**: The CloudFront distribution domain name
- **TTL**: 300 (5 minutes) or your preferred value

⚠️ **Note**: CNAME records cannot be used for root domains (apex domains). If you need to use a root domain like `example.com`, you must use ALIAS records (Route53) or A records with static IPs (not recommended for CloudFront).

Examples:
```
# Route53 ALIAS (recommended)
app.yourdomain.com A (ALIAS) d1234567890.cloudfront.net

# Other providers CNAME
app.yourdomain.com CNAME d1234567890.cloudfront.net
```
## DNS Provider Examples

### Route53 (Different AWS Account) - Recommended

1. Log in to AWS Console (your DNS account)
2. Go to Route53 → Hosted zones
3. Select your domain
4. Create records → Simple routing
5. **For main domain record**:
   - Record name: Your subdomain (e.g., `app`) or leave blank for root
   - Record type: A
   - Value/Route traffic to: Alias to CloudFront distribution
   - Choose distribution: Select your CloudFront distribution or enter domain manually
   - Evaluate target health: No
6. **For certificate validation**: Use CNAME records as provided by ACM
