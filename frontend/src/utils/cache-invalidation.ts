import { invalidateCache } from "alova";
import { alovaClient } from "@/lib/api-client";

export interface CacheInvalidationEvent {
  type: "scan_started" | "scan_completed" | "scan_failed";
  scanId: string;
  timestamp: string;
  affectedCaches: string[];
}

export const _invalidateCache = (event: CacheInvalidationEvent) => {
  // Invalidate dashboard metrics cache using snapshots.match
  const dashboardMethods = alovaClient.snapshots.match({
    filter: (method) => method.url === "/dashboard/metrics",
  });
  if (dashboardMethods.length > 0) {
    invalidateCache(dashboardMethods);
  }

  // Invalidate scans list cache
  const scansMethods = alovaClient.snapshots.match({
    filter: (method) => method.url.startsWith("/scans"),
  });
  if (scansMethods.length > 0) {
    invalidateCache(scansMethods);
  }

  // Invalidate specific scan cache
  const scanMethods = alovaClient.snapshots.match({
    filter: (method) => method.url.includes(`/scan/${event.scanId}`),
  });
  if (scanMethods.length > 0) {
    invalidateCache(scanMethods);
  }

  console.log(`Cache invalidated for event: ${event.type}, scanId: ${event.scanId}`);
};

export const triggerCacheInvalidation = (type: CacheInvalidationEvent["type"], scanId: string) => {
  const event: CacheInvalidationEvent = {
    type,
    scanId,
    timestamp: new Date().toISOString(),
    affectedCaches: ["/dashboard/metrics", "/scans", `/scan/${scanId}`],
  };

  _invalidateCache(event);
};
