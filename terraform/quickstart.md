# Quickstart: Current Architecture Overview
Your FastScan project consists of these main components:

*   **<PERSON><PERSON><PERSON><PERSON>da:** AWS Lambda function for serverless vulnerability scanning
*   **Nuclear Pond Backend:** Go-based API service running on ECS Fargate
*   **Frontend:** React/TypeScript application hosted on S3 with optional CloudFront
*   **Network Infrastructure:** VPC with public/private subnets, NAT Gateway
*   **PoW Targets:** EC2 instances for demo scanning (which you want to exclude)

## Deployment Guide for New AWS Account

### 1. Prerequisites
Ensure you have required tools:

```bash
aws --version
terraform --version  # Should be >= 1.0
docker --version
```

### 2. Configure AWS Credentials
Configure AWS CLI for your new account, or use environment variables:

```bash
aws configure
# Or use environment variables
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"  # Recommended region
```

### 3. Prepare Terraform Configuration
Navigate to the `terraform` directory. This project uses environment-specific `.tfvars` files for configuration, located in the `environments/` directory.

For a development environment, you can start by copying the example file:
```bash
cd terraform
cp environments/dev.example.tfvars environments/dev.tfvars
```

### 4. Customize Configuration
Edit the new `environments/dev.tfvars` file with your specific details. For example:
```hcl
# Core configuration
project_name = "your-project-name"
environment = "dev"

# Governance
owner = "your-team"
cost_center = "your-cost-center"
business_unit = "your-business-unit"

# Enable discovery features
enable_resource_discovery = true
```

### 5. Initialize and Deploy
Now, run the Terraform commands, making sure to specify your environment's variable file using the `-var-file` flag.

```bash
# Initialize Terraform
terraform init

# Review the deployment plan
terraform plan -var-file="environments/dev.tfvars"

# Deploy the infrastructure
terraform apply -var-file="environments/dev.tfvars"
```

### 6. Key Outputs After Deployment
After successful deployment, you can view important outputs:

```bash
# View deployment outputs
terraform output

# Key outputs include:
# - nuclei_lambda_function_name: For invoking scans
# - nuclear_pond_backend_service_url: API endpoint
# - frontend_url: Web application URL
# - nuclei_s3_bucket_name: For scan results storage
```

### 7. Verify Deployment
After deployment, you can verify the created resources using the AWS CLI. This command uses the tags applied by Terraform to find all resources associated with your new project environment.

```bash
# Verify project resources (replace with your project_name from the .tfvars file)
aws resourcegroupstaggingapi get-resources \
  --tag-filters Key=Project,Values=your-project-name Key=Environment,Values=dev
```

## What Gets Deployed

#### Core Infrastructure:
*   **VPC:** `10.0.0.0/16`
*   **Subnets:** Public and private subnets across 2 AZs
*   **NAT Gateway:** For private subnet internet access
*   **Security Groups:** Properly configured for each component

#### Services:
*   **Nuclei Lambda Function:**
    *   Runtime: `provided.al2` (Go binary)
    *   Memory: `512MB`
    *   Timeout: `15` minutes
    *   Nuclei version: `3.1.7`
*   **Nuclear Pond Backend:**
    *   Platform: ECS Fargate
    *   CPU: `256` units
    *   Memory: `512 MB`
    *   Container port: `8082`
    *   Application Load Balancer

#### Frontend:
*   S3 static website hosting
*   Optional CloudFront distribution
*   Automated deployment scripts

#### Storage:
*   S3 buckets for scan results and artifacts
*   DynamoDB table for scan state management

## Post-Deployment Steps

### 1. Build and Deploy Nuclear Pond Backend
```bash
# Use the generated deployment script
cd terraform/nuclear_pond_backend
./deploy-backend-dev.sh
```

### 2. Build and Deploy Frontend
```bash
# Use the generated deployment script
cd terraform/frontend
./deploy-frontend-dev.sh
```

### 3. Test the Deployment
```bash
# Check Nuclear Pond health
curl http://[alb-dns-name]/health-check

# Test Nuclei Lambda function
aws lambda invoke \
  --function-name [nuclei-function-name] \
  --payload '{"targets":["httpbin.org"],"templates":["http/technologies/tech-detect.yaml"]}' \
  response.json
```
