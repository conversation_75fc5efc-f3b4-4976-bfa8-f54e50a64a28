package ports

import (
	"net"
	"testing"
	"time"
)

func TestScanOpenPorts(t *testing.T) {
	// Start a test server on a random port
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		t.Fatalf("Failed to create test listener: %v", err)
	}
	defer listener.Close()

	// Get the port the server is listening on
	addr := listener.Addr().(*net.TCPAddr)
	openPort := addr.Port

	// Test scanning with the open port and some closed ports
	portList := []int{openPort, 9999, 9998, 9997}
	openPorts := ScanOpenPorts("127.0.0.1", portList)

	// Should find exactly one open port
	if len(openPorts) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 open port, got %d", len(openPorts))
	}

	if openPorts[0] != openPort {
		t.Errorf("Expected open port %d, got %d", openPort, openPorts[0])
	}
}

func TestScanOpenPortsAllClosed(t *testing.T) {
	// Test with ports that should all be closed
	portList := []int{9999, 9998, 9997, 9996}
	openPorts := ScanOpenPorts("127.0.0.1", portList)

	if len(openPorts) != 0 {
		t.Errorf("Expected 0 open ports, got %d", len(openPorts))
	}
}

func TestScanOpenPortsEmptyList(t *testing.T) {
	portList := []int{}
	openPorts := ScanOpenPorts("127.0.0.1", portList)

	if len(openPorts) != 0 {
		t.Errorf("Expected 0 open ports for empty list, got %d", len(openPorts))
	}
}

func TestScanOpenPortsMultipleServers(t *testing.T) {
	// Start multiple test servers
	listeners := make([]net.Listener, 3)
	openPorts := make([]int, 3)

	for i := 0; i < 3; i++ {
		listener, err := net.Listen("tcp", "127.0.0.1:0")
		if err != nil {
			t.Fatalf("Failed to create test listener %d: %v", i, err)
		}
		defer listener.Close()

		addr := listener.Addr().(*net.TCPAddr)
		openPorts[i] = addr.Port
		listeners[i] = listener
	}

	// Test scanning with all open ports plus some closed ones
	portList := append(openPorts, 9999, 9998, 9997)
	foundOpenPorts := ScanOpenPorts("127.0.0.1", portList)

	if len(foundOpenPorts) != 3 {
		t.Errorf("Expected 3 open ports, got %d", len(foundOpenPorts))
	}

	// Check that all expected ports are found
	foundMap := make(map[int]bool)
	for _, port := range foundOpenPorts {
		foundMap[port] = true
	}

	for _, expectedPort := range openPorts {
		if !foundMap[expectedPort] {
			t.Errorf("Expected port %d to be open, but it was not found", expectedPort)
		}
	}
}

func TestScanOpenPortsInvalidIP(t *testing.T) {
	portList := []int{80, 443, 8080}
	openPorts := ScanOpenPorts("invalid-ip-address", portList)

	// Should return empty list for invalid IP
	if len(openPorts) != 0 {
		t.Errorf("Expected 0 open ports for invalid IP, got %d", len(openPorts))
	}
}

func TestScanOpenPortsTimeout(t *testing.T) {
	// Test with a port that should timeout (assuming no service on high port)
	portList := []int{65535} // Very high port number, likely closed
	start := time.Now()
	openPorts := ScanOpenPorts("127.0.0.1", portList)
	duration := time.Since(start)

	// Should return empty list
	if len(openPorts) != 0 {
		t.Errorf("Expected 0 open ports, got %d", len(openPorts))
	}

	// Should complete within reasonable time (less than 5 seconds total)
	if duration > 5*time.Second {
		t.Errorf("Port scan took too long: %v", duration)
	}
}

func TestScanOpenPortsCommonPorts(t *testing.T) {
	// Test with common port numbers
	commonPorts := []int{21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080, 8443}
	openPorts := ScanOpenPorts("127.0.0.1", commonPorts)

	// Most of these should be closed on localhost, but we don't make assumptions
	// Just check that the function doesn't crash and returns a reasonable result
	if len(openPorts) > len(commonPorts) {
		t.Errorf("Found more open ports than scanned: %d > %d", len(openPorts), len(commonPorts))
	}

	// Check that all returned ports are in the original list
	portMap := make(map[int]bool)
	for _, port := range commonPorts {
		portMap[port] = true
	}

	for _, foundPort := range openPorts {
		if !portMap[foundPort] {
			t.Errorf("Found port %d that was not in the original scan list", foundPort)
		}
	}
}

func TestScanOpenPortsLargePortList(t *testing.T) {
	// Test with a large list of ports
	portList := make([]int, 100)
	for i := 0; i < 100; i++ {
		portList[i] = 10000 + i // Ports 10000-10099
	}

	start := time.Now()
	openPorts := ScanOpenPorts("127.0.0.1", portList)
	duration := time.Since(start)

	// Should complete within reasonable time
	if duration > 10*time.Second {
		t.Errorf("Large port scan took too long: %v", duration)
	}

	// Should return a reasonable number of results
	if len(openPorts) > len(portList) {
		t.Errorf("Found more open ports than scanned: %d > %d", len(openPorts), len(portList))
	}
}

func TestScanOpenPortsEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		ip       string
		portList []int
	}{
		{
			name:     "Nil port list",
			ip:       "127.0.0.1",
			portList: nil,
		},
		{
			name:     "Empty IP",
			ip:       "",
			portList: []int{80, 443},
		},
		{
			name:     "Localhost IP",
			ip:       "localhost",
			portList: []int{80, 443},
		},
		{
			name:     "Zero ports",
			ip:       "127.0.0.1",
			portList: []int{0, 0, 0},
		},
		{
			name:     "Negative ports",
			ip:       "127.0.0.1",
			portList: []int{-1, -2, -3},
		},
		{
			name:     "Very large ports",
			ip:       "127.0.0.1",
			portList: []int{65535, 65534, 65533},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Should not panic
			openPorts := ScanOpenPorts(tt.ip, tt.portList)

			// Should return a slice (even if empty)
			if openPorts == nil {
				t.Error("Expected non-nil result, got nil")
			}
		})
	}
}

func BenchmarkScanOpenPorts(b *testing.B) {
	portList := []int{80, 443, 8080, 8443, 22, 21, 23, 25, 53, 110}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ScanOpenPorts("127.0.0.1", portList)
	}
}

func BenchmarkScanOpenPortsLargeList(b *testing.B) {
	portList := make([]int, 100)
	for i := 0; i < 100; i++ {
		portList[i] = 10000 + i
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ScanOpenPorts("127.0.0.1", portList)
	}
}
