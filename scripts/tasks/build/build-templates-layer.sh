#!/bin/bash

# Build Templates Layer
# Packages Nuclei templates from backend/templates/ into a Lambda layer

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT
TEMPLATES_SOURCE_DIR="${PROJECT_ROOT}/backend/templates"

source "${PROJECT_ROOT}/scripts/lib/logging.sh"

# Validate input parameters
if [[ $# -ne 1 ]]; then
    log_error "Usage: $0 <output_directory>"
    log_error "Example: $0 /path/to/artifacts"
    exit 1
fi

OUTPUT_DIR="$1"
TEMP_DIR=$(mktemp -d)
LAYER_DIR="${TEMP_DIR}/templates-layer"
# AWS Lambda layers extract to /opt, so templates/ becomes /opt/templates/
TEMPLATES_LAYER_DIR="${LAYER_DIR}/templates"

# Cleanup function
cleanup() {
    if [[ -d "${TEMP_DIR}" ]]; then
        rm -rf "${TEMP_DIR}"
    fi
}

trap cleanup EXIT

# Validate source templates directory
validate_source_templates() {
    log_info "Validating source templates directory..."
    
    if [[ ! -d "${TEMPLATES_SOURCE_DIR}" ]]; then
        log_error "Templates source directory not found: ${TEMPLATES_SOURCE_DIR}"
        return 1
    fi
    
    # Count template files
    local template_count=$(find "${TEMPLATES_SOURCE_DIR}" -name "*.yaml" -o -name "*.yml" | wc -l)
    
    if [[ "${template_count}" -eq 0 ]]; then
        log_error "No template files found in: ${TEMPLATES_SOURCE_DIR}"
        return 1
    fi
    
    log_success "Found ${template_count} template files in source directory"
}

# Create layer directory structure
create_layer_structure() {
    log_info "Creating templates layer directory structure..."
    
    mkdir -p "${TEMPLATES_LAYER_DIR}"
    
    log_success "Layer directory created: ${TEMPLATES_LAYER_DIR}"
}

# Copy and validate templates
copy_templates() {
    log_info "Copying templates to layer directory..."
    
    # Copy all YAML files from source to layer
    local copied_count=0
    local failed_count=0
    
    while IFS= read -r -d '' template_file; do
        local relative_path="${template_file#${TEMPLATES_SOURCE_DIR}/}"
        local dest_file="${TEMPLATES_LAYER_DIR}/${relative_path}"
        local dest_dir=$(dirname "${dest_file}")
        
        # Create destination directory if needed
        mkdir -p "${dest_dir}"
        
        # Copy file
        if cp "${template_file}" "${dest_file}"; then
            ((copied_count++))
            log_info "Copied: ${relative_path}"
        else
            ((failed_count++))
            log_error "Failed to copy: ${relative_path}"
        fi
    done < <(find "${TEMPLATES_SOURCE_DIR}" -type f \( -name "*.yaml" -o -name "*.yml" \) -print0)
    
    if [[ "${failed_count}" -gt 0 ]]; then
        log_error "Failed to copy ${failed_count} template files"
        return 1
    fi
    
    if [[ "${copied_count}" -eq 0 ]]; then
        log_error "No template files were copied"
        return 1
    fi
    
    log_success "Copied ${copied_count} template files successfully"
}

# Validate templates syntax
validate_templates() {
    log_info "Validating template files..."
    
    local valid_count=0
    local invalid_count=0
    local total_size=0
    
    while IFS= read -r -d '' template_file; do
        local relative_path="${template_file#${TEMPLATES_LAYER_DIR}/}"
        
        # Check if file is readable
        if [[ ! -r "${template_file}" ]]; then
            log_error "Template file not readable: ${relative_path}"
            ((invalid_count++))
            continue
        fi
        
        # Check file size
        local file_size=$(stat -f%z "${template_file}" 2>/dev/null || stat -c%s "${template_file}" 2>/dev/null || echo "0")
        if [[ "${file_size}" -eq 0 ]]; then
            log_error "Empty template file: ${relative_path}"
            ((invalid_count++))
            continue
        fi
        
        # Basic YAML syntax validation (check for basic structure)
        if ! grep -q "^id:" "${template_file}"; then
            log_warning "Template may be missing 'id' field: ${relative_path}"
        fi
        
        if ! grep -q "^info:" "${template_file}"; then
            log_warning "Template may be missing 'info' field: ${relative_path}"
        fi
        
        ((valid_count++))
        total_size=$((total_size + file_size))
        
    done < <(find "${TEMPLATES_LAYER_DIR}" -type f \( -name "*.yaml" -o -name "*.yml" \) -print0)
    
    if [[ "${invalid_count}" -gt 0 ]]; then
        log_error "${invalid_count} template files failed validation"
        return 1
    fi
    
    local total_size_kb=$((total_size / 1024))
    log_success "Validated ${valid_count} template files (${total_size_kb}KB total)"
}

# Create templates index file
create_templates_index() {
    log_info "Creating templates index file..."
    
    local index_file="${TEMPLATES_LAYER_DIR}/index.txt"
    
    # Create index with relative paths
    find "${TEMPLATES_LAYER_DIR}" -type f \( -name "*.yaml" -o -name "*.yml" \) | while read -r template_file; do
        local relative_path="${template_file#${TEMPLATES_LAYER_DIR}/}"
        echo "${relative_path}"
    done | sort > "${index_file}"
    
    local template_count=$(wc -l < "${index_file}")
    log_success "Created templates index with ${template_count} entries"
}

# Create layer zip file
create_layer_zip() {
    log_info "Creating templates layer zip file..."
    
    local output_file="${OUTPUT_DIR}/templates-layer.zip"
    
    # Ensure output directory exists
    mkdir -p "${OUTPUT_DIR}"
    
    # Create zip file from layer directory
    cd "${LAYER_DIR}"
    if ! zip -r "${output_file}" . >/dev/null; then
        log_error "Failed to create layer zip file: ${output_file}"
        return 1
    fi
    
    # Validate created zip file
    if [[ ! -f "${output_file}" ]]; then
        log_error "Layer zip file not created: ${output_file}"
        return 1
    fi
    
    if ! unzip -t "${output_file}" >/dev/null 2>&1; then
        log_error "Created zip file is invalid: ${output_file}"
        return 1
    fi
    
    local zip_size=$(stat -f%z "${output_file}" 2>/dev/null || stat -c%s "${output_file}" 2>/dev/null || echo "0")
    local zip_size_kb=$((zip_size / 1024))
    log_success "Templates layer created: ${output_file} (${zip_size_kb}KB)"
    
    # Show layer structure (templates/ will become /opt/templates/ when extracted)
    log_info "Layer structure:"
    unzip -l "${output_file}" | grep -E "templates/" | head -10 | while read -r line; do
        echo "  ${line}"
    done
    
    local total_files=$(unzip -l "${output_file}" | grep -c "templates/.*\.ya*ml$" || echo "0")
    if [[ "${total_files}" -gt 10 ]]; then
        echo "  ... and $((total_files - 10)) more template files"
    fi
}

# Verify layer accessibility
verify_layer_accessibility() {
    log_info "Verifying layer accessibility..."
    
    # Extract to temporary location and verify structure
    local verify_dir="${TEMP_DIR}/verify"
    mkdir -p "${verify_dir}"
    
    local output_file="${OUTPUT_DIR}/templates-layer.zip"
    if ! unzip -q "${output_file}" -d "${verify_dir}"; then
        log_error "Failed to extract layer for verification"
        return 1
    fi
    
    # Check if templates directory exists (will be /opt/templates when extracted)
    if [[ ! -d "${verify_dir}/templates" ]]; then
        log_error "Layer does not contain templates directory"
        return 1
    fi
    
    # Check if templates are accessible
    local template_count=$(find "${verify_dir}/templates" -name "*.yaml" -o -name "*.yml" | wc -l)
    if [[ "${template_count}" -eq 0 ]]; then
        log_error "No template files found in extracted layer"
        return 1
    fi
    
    # Check if index file exists
    if [[ ! -f "${verify_dir}/templates/index.txt" ]]; then
        log_warning "Templates index file not found (this is optional)"
    fi
    
    log_success "Layer accessibility verified: ${template_count} templates accessible at /opt/templates/ (when extracted)"
}

# Main execution
main() {
    log_info "Building templates layer..."
    log_info "Source directory: ${TEMPLATES_SOURCE_DIR}"
    log_info "Output directory: ${OUTPUT_DIR}"
    
    # Validate source templates
    if ! validate_source_templates; then
        log_error "Source templates validation failed"
        exit 1
    fi
    
    # Create layer structure
    if ! create_layer_structure; then
        log_error "Failed to create layer structure"
        exit 1
    fi
    
    # Copy templates
    if ! copy_templates; then
        log_error "Failed to copy templates"
        exit 1
    fi
    
    # Validate templates
    if ! validate_templates; then
        log_error "Template validation failed"
        exit 1
    fi
    
    # Create templates index
    if ! create_templates_index; then
        log_error "Failed to create templates index"
        exit 1
    fi
    
    # Create layer zip
    if ! create_layer_zip; then
        log_error "Failed to create layer zip"
        exit 1
    fi
    
    # Verify layer accessibility
    if ! verify_layer_accessibility; then
        log_error "Layer accessibility verification failed"
        exit 1
    fi
    
    log_success "Templates layer build completed successfully!"
}

# Execute main function
main "$@"