# ==============================================================================
# CLOUDWATCH CONFIGURATION FOR NUCLEI LAMBDA
# ==============================================================================

# ==============================================================================
# CLOUDWATCH LOG GROUP
# ==============================================================================

# CloudWatch log group for Lambda function
# tfsec:ignore:aws-cloudwatch-log-group-customer-key
resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name = "/aws/lambda/${var.project_name}-nuclei-function"

  retention_in_days = var.log_retention_days

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-lambda-logs"
    Component   = "nuclei-lambda"
    Type        = "monitoring"
    Purpose     = "lambda-logs"
  })
}

# ==============================================================================
# CLOUDWATCH METRICS AND ALARMS (OPTIONAL)
# ==============================================================================

# CloudWatch metric for Lambda duration
resource "aws_cloudwatch_metric_alarm" "lambda_duration" {
  count = var.enable_cloudwatch_alarms ? 1 : 0

  alarm_name          = "${var.project_name}-nuclei-lambda-duration"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Duration"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Average"
  threshold           = var.nuclei_timeout * 1000 * 0.8  # 80% of timeout in milliseconds
  alarm_description   = "This metric monitors duration for Nuclei Lambda function"
  alarm_actions       = var.cloudwatch_alarm_actions

  dimensions = {
    FunctionName = aws_lambda_function.nuclei_function.function_name
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-lambda-duration-alarm"
    Component   = "nuclei-lambda"
    Type        = "monitoring"
  })
}

# ==============================================================================
# CLOUDWATCH DASHBOARD (OPTIONAL)
# ==============================================================================

# CloudWatch dashboard for monitoring Nuclei Lambda
resource "aws_cloudwatch_dashboard" "nuclei_lambda_dashboard" {
  count = var.enable_cloudwatch_dashboard ? 1 : 0

  dashboard_name = "${var.project_name}-nuclei-lambda-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/Lambda", "Invocations", "FunctionName", aws_lambda_function.nuclei_function.function_name],
            [".", "Errors", ".", "."],
            [".", "Duration", ".", "."],
            [".", "Throttles", ".", "."]
          ]
          view    = "timeSeries"
          stacked = false
          region  = data.aws_region.current.name
          title   = "Lambda Function Metrics"
          period  = 300
        }
      },
      {
        type   = "log"
        x      = 0
        y      = 6
        width  = 24
        height = 6

        properties = {
          query   = "SOURCE '${aws_cloudwatch_log_group.lambda_log_group.name}' | fields @timestamp, @message | sort @timestamp desc | limit 100"
          region  = data.aws_region.current.name
          title   = "Recent Lambda Logs"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/S3", "BucketSizeBytes", "BucketName", aws_s3_bucket.artifacts_bucket.id, "StorageType", "StandardStorage"],
            [".", "NumberOfObjects", ".", ".", ".", "AllStorageTypes"]
          ]
          view    = "timeSeries"
          stacked = false
          region  = data.aws_region.current.name
          title   = "S3 Bucket Metrics"
          period  = 86400
        }
      }
    ]
  })


}

# ==============================================================================
# LOG INSIGHTS QUERIES (SAVED QUERIES)
# ==============================================================================

# Saved CloudWatch Insights query for error analysis
resource "aws_cloudwatch_query_definition" "lambda_errors" {
  count = var.enable_log_insights_queries ? 1 : 0

  name = "${var.project_name}-nuclei-lambda-errors"

  log_group_names = [
    aws_cloudwatch_log_group.lambda_log_group.name
  ]

  query_string = <<-EOT
    fields @timestamp, @message
    | filter @message like /ERROR/
    | sort @timestamp desc
    | limit 100
  EOT
}

# Saved CloudWatch Insights query for scan performance
resource "aws_cloudwatch_query_definition" "scan_performance" {
  count = var.enable_log_insights_queries ? 1 : 0

  name = "${var.project_name}-nuclei-lambda-performance"

  log_group_names = [
    aws_cloudwatch_log_group.lambda_log_group.name
  ]

  query_string = <<-EOT
    fields @timestamp, @duration, @billedDuration, @memorySize, @maxMemoryUsed
    | filter @type = "REPORT"
    | stats avg(@duration), max(@duration), min(@duration), avg(@maxMemoryUsed) by bin(5m)
    | sort @timestamp desc
  EOT
}
