{
  "biome.lsp": {
    "workingDirectory": "frontend"
  },
  "editor.defaultFormatter": "biomejs.biome",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit",
    "source.organizeImports": "explicit"
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.ts": "typescript",
    "*.jsx": "javascriptreact",
    "*.js": "javascript"
  },
  "terminal.integrated.defaultProfile.linux": "zsh",
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "devbox.enable": true,
  "[terraform]": {
    "editor.defaultFormatter": "hashicorp.terraform"
  },
  "bashIde.explainshellEndpoint": "http://localhost:5000",
  "[shellscript]": {
    "editor.defaultFormatter": "mads-hartmann.bash-ide-vscode"
  },
  "shellcheck.customArgs": ["-x"],
  "window.zoomLevel": -1.35,
  "[terraform-vars]": {
    "editor.defaultFormatter": "hashicorp.terraform"
  },
  // Lint with golangci-lint from Devbox
  "go.lintTool": "golangci-lint",
  "go.lintOnSave": "package",

  // Prepend Devbox bin to PATH for all Go tools
  "go.toolsEnvVars": {
    "PATH": "${workspaceFolder}/.devbox/nix/profile/default/bin:/home/<USER>/go/bin:${env:PATH}"
  },

  // Formatting configuration
  "go.formatTool": "gofumpt",
  "go.useLanguageServer": true,
  "go.formatFlags": ["-extra"],

  // Use Devbox-managed binaries explicitly
  "go.alternateTools": {
    "go": "/home/<USER>/projects/fast-scan/.devbox/nix/profile/default/bin/go",
    "gopls": "/home/<USER>/projects/fast-scan/.devbox/nix/profile/default/bin/gopls",
    "gofumpt": "/home/<USER>/projects/fast-scan/.devbox/nix/profile/default/bin/gofumpt",
    "goimports": "/home/<USER>/projects/fast-scan/.devbox/nix/profile/default/bin/goimports",
    "golangci-lint": "/home/<USER>/projects/fast-scan/.devbox/nix/profile/default/bin/golangci-lint"
  },

  // Go-specific editor settings (CRITICAL: spaces over tabs)
  "[go]": {
    "editor.defaultFormatter": "golang.go",
    "editor.formatOnSave": true,
    "editor.insertSpaces": true,
    "editor.tabSize": 2,
    "editor.detectIndentation": false,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },
  "[go.mod]": {
    "editor.formatOnSave": true,
    "editor.insertSpaces": true,
    "editor.tabSize": 2
  },
  "[go.sum]": {
    "editor.formatOnSave": false
  },

  // Additional Go tools configuration
  "go.gocodeAutoBuild": false,
  "go.buildOnSave": "off",
  "go.vetOnSave": "off", // We use golangci-lint instead
  "go.lintFlags": ["--config=${workspaceFolder}/.golangci.yml", "--fast"],

  // Enable additional Go language server features
  "gopls": {
    "ui.semanticTokens": true,
    "analyses": {
      "unusedparams": true,
      "shadow": true
    },
    "staticcheck": true,
    "gofumpt": true
  }
}
