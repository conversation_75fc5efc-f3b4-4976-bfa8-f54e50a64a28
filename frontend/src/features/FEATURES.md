# Features Layer

This directory contains the business logic for the FastScan frontend, organized by feature domain. The goal is to separate complex logic from our UI components, making the app easier to build and maintain.

## How It's Organized

We group code by business domain (e.g., `auth`, `scans`), not by technical layer. Each feature contains its own hooks, types, and utility functions.

```
features/
├── auth/
│   ├── hooks.ts       # All React hooks with business logic
│   ├── services.ts    # API calls and business services
│   ├── state.ts       # Global state management (atoms, stores)
│   ├── types.ts       # TypeScript types for this feature
│   └── utils.ts       # Helper functions and validation
├── scans/
│   └── ...
└── shared/
    ├── hooks.ts       # Hooks used across multiple features
    ├── types.ts       # Types shared across features
    └── utils.ts       # Utilities shared across features
```

## The Core Idea

- **Business logic lives in hooks.** Instead of putting complex logic inside components, we extract it into custom hooks (e.g., `useLogin`, `useScans`).
- **Components stay simple.** They consume these hooks and focus on rendering the UI.
- **Code is colocated.** Everything related to a feature (logic, types, helpers) lives in the same place, making it easy to find and work with.
- **Direct imports over barrel files.** We use specific imports (`@/features/auth/hooks`) instead of barrel files to improve tree shaking and bundle optimization.
- **Meaningful file names.** Single files with descriptive names (e.g., `hooks.ts`, `services.ts`) instead of generic `index.ts` files for better IDE navigation.

This approach helps us keep the codebase organized, test business logic in isolation, onboard new developers faster, and optimize bundle size.

## Architecture Decisions

### File Structure
- **Single files over directories:** We use single files (e.g., `hooks.ts`) instead of directories with index files to avoid multiple `index.ts` files that hurt IDE navigation.
- **No barrel files:** We eliminate feature-level barrel files (`auth/index.ts`) to improve tree shaking and prevent unintended dependencies.
- **Scaling strategy:** When a file grows beyond ~200 lines, we can split it into a folder structure (e.g., `hooks.ts` + `hooks/` directory).

### Import Strategy
- **Direct imports:** Use specific paths like `@/features/auth/hooks` instead of `@/features/auth`
- **Explicit dependencies:** Import paths clearly show what functionality you're using
- **Tree shaking friendly:** Bundlers can eliminate unused code more effectively

## Example: Using a Feature Hook

Here's how a component uses a feature hook to handle login logic without containing the logic itself.

**The Hook:** `features/auth/hooks.ts`
```typescript
export const useLogin = () => {
  // ... state management, API calls, validation ...
  
  const handleSubmit = async (e: React.FormEvent) => {
    // Complex business logic here
  };
  
  return { handleSubmit, isLoading, ... };
};
```

**The Component:** `pages/Login.tsx`
```typescript
import { useLogin } from '@/features/auth/hooks';

const Login: React.FC = () => {
  const { handleSubmit, isLoading } = useLogin();
  
  return (
    <form onSubmit={handleSubmit}>
      {/* UI is clean, logic is in the hook */}
    </form>
  );
};
```

## Import Examples

```typescript
// ✅ Good - Direct imports from consolidated files
import { useLogin, useAuth, useLogout } from '@/features/auth/hooks';
import { login, logout } from '@/features/auth/services';
import { AuthUser, LoginCredentials } from '@/features/auth/types';
import { validateEmail } from '@/features/auth/utils';

// ❌ Avoid - Barrel file imports (removed)
// import { useLogin } from '@/features/auth';

// ❌ Avoid - Deep imports to specific files (unnecessary)
// import { useLogin } from '@/features/auth/hooks/useLogin';
```

## Testing

Feature hooks and utilities should be unit-tested. Place test files in a `__tests__` directory next to the file being tested.