package handlers

import (
	"log"
	"net/http"
	"nuclear_pond/pkg/types"

	"github.com/go-chi/render"
)

// ScanStatusHandler returns a summary/status view for a scan.
func ScanStatusHandler(w http.ResponseWriter, r *http.Request) {
	requestID, scanID, err := parseScanID(r)
	if err != nil {
		writeAPIError(w, r, err)

		return
	}

	scanRequest, err := fetchScanOr404(requestID)
	if err != nil {
		log.Printf("Error getting scan details for %s: %v", scanID, err)
		writeAPIError(w, r, err)

		return
	}

	var resultsSummary *types.ResultsSummary
	if scanRequest.Status == "completed" && scanRequest.ResultsSummary != nil {
		resultsSummary = scanRequest.ResultsSummary
	}

	// results tracking removed from API surface

	resp := types.ScanStatusResponse{
		ScanID:      scanRequest.ScanID,
		RequestID:   scanRequest.RequestID,
		Status:      scanRequest.Status,
		CreatedAt:   scanRequest.CreatedAt,
		UpdatedAt:   scanRequest.UpdatedAt,
		CompletedAt: scanRequest.CompletedAt,
		Config: types.ScanConfig{
			TargetCount: scanRequest.TargetCount,
			Targets:     []string{},
			Batches:     scanRequest.Batches,
			Threads:     scanRequest.Threads,
			Scanners:    scanRequest.Threads,
			Mode:        scanRequest.Mode,
			Templates:   scanRequest.Templates,
		},
		ResultsSummary:         resultsSummary,
		TotalVulnerableTargets: scanRequest.TotalVulnerableTargets,
	}

	if scanRequest.Status == "completed" && scanRequest.VulnerableTargets != nil {
		resp.VulnerableTargets = scanRequest.VulnerableTargets
	}

	// Start with a map so we can conditionally include fields without zero-values
	responseMap := map[string]interface{}{
		"scan_id":                  resp.ScanID,
		"request_id":               resp.RequestID,
		"status":                   resp.Status,
		"created_at":               resp.CreatedAt,
		"updated_at":               resp.UpdatedAt,
		"completed_at":             resp.CompletedAt,
		"config":                   resp.Config,
		"results_summary":          resp.ResultsSummary,
		"total_vulnerable_targets": resp.TotalVulnerableTargets,
	}

	if resp.VulnerableTargets != nil {
		responseMap["vulnerable_targets"] = resp.VulnerableTargets
	}

	render.JSON(w, r, responseMap)
}
