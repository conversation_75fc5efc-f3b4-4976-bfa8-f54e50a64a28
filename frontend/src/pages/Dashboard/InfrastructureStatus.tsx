import { ServerIcon, ZapIcon } from "lucide-react";

interface InfrastructureStatusProps {
  className?: string;
}

export default function InfrastructureStatus({ className = "" }: InfrastructureStatusProps) {
  // const { status, startInfrastructure, stopInfrastructure, isOperationInProgress, lastOperation, operationError } = usePowManagement();
  const status: "online" | "offline" | "loading" = "online";

  const getStatusIndicator = (serviceStatus: "online" | "offline" | "loading") => {
    switch (serviceStatus) {
      case "online":
        return <div className="w-3 h-3 bg-success rounded-full"></div>;
      case "offline":
        return <div className="w-3 h-3 bg-error rounded-full"></div>;
      case "loading":
        return <div className="loading loading-spinner loading-xs"></div>;
      default:
        return <div className="w-3 h-3 bg-base-300 rounded-full"></div>;
    }
  };

  const getTestingTargetsStatus = () => {
    return status === "online" ? "online" : "offline";
  };

  const getTestingTargetsBadge = () => {
    switch (status) {
      // @ts-expect-error
      case "loading":
        return (
          <div className="badge badge-ghost badge-sm gap-1">
            <div className="loading loading-spinner loading-xs"></div>
            Loading
          </div>
        );
      case "online":
        return <div className="badge badge-success badge-sm">Online</div>;
      default:
        return <div className="badge badge-error badge-sm">Offline</div>;
    }
  };

  return (
    <div className={`card bg-base-100 shadow border border-base-300 ${className}`}>
      <div className="card-body">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-info/10 rounded-lg">
            <ServerIcon className="h-5 w-5 text-info" />
          </div>
          <div>
            <h2 className="card-title text-lg">Infrastructure Status</h2>
            <p className="text-base-content/60 text-sm">Scanning services and testing environment</p>
          </div>
        </div>

        {/* Status Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* Scanner Engine */}
          <div className="flex items-center justify-between p-4 bg-base-200 rounded-lg border border-base-300">
            <div className="flex items-center gap-3">
              {getStatusIndicator("online")}
              <div className="flex items-center gap-2">
                <ZapIcon className="h-4 w-4 text-primary" />
                <span className="font-medium text-sm">Scanner Engine</span>
              </div>
            </div>
            <div className="badge badge-success badge-sm">Online</div>
          </div>

          {/* Orchestrator */}
          <div className="flex items-center justify-between p-4 bg-base-200 rounded-lg border border-base-300">
            <div className="flex items-center gap-3">
              {getStatusIndicator("online")}
              <div className="flex items-center gap-2">
                <ServerIcon className="h-4 w-4 text-secondary" />
                <span className="font-medium text-sm">Scan Orchestrator</span>
              </div>
            </div>
            <div className="badge badge-success badge-sm">Online</div>
          </div>

          {/* Testing Targets */}
          <div className="flex items-center justify-between p-4 bg-base-200 rounded-lg border border-base-300">
            <div className="flex flex-col gap-2 w-full">
              <div className="flex items-center gap-3">
                {getStatusIndicator(getTestingTargetsStatus())}
                <div className="flex items-center gap-2">
                  <ServerIcon className="h-4 w-4 text-accent" />
                  <span className="font-medium text-sm">Testing Targets</span>
                </div>
                {getTestingTargetsBadge()}
              </div>
              <p className="text-xs text-base-content/60">On-demand testing environment</p>

              {/* Testing Targets Control Panel */}
              {/* <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={stopInfrastructure}
                    disabled={status === "offline"}
                    className={`btn btn-outline btn-error btn-sm ${isOperationInProgress && status === "online" ? "loading" : ""}`}
                  >
                    <StopCircleIcon className="h-3 w-3" />
                    Stop
                  </button>
                  <button
                    type="button"
                    onClick={startInfrastructure}
                    disabled={status === "online"}
                    className={`btn btn-primary btn-sm ${isOperationInProgress && status === "offline" ? "loading" : ""}`}
                  >
                    <PlayIcon className="h-3 w-3" />
                    Start
                  </button>
                </div>
              </div> */}

              {/* Operation Status */}
              {/* {lastOperation && (
                <div className="mb-3">
                  <div className="alert alert-info alert-sm">
                    <CheckCircleIcon className="h-3 w-3" />
                    <span className="text-xs">{lastOperation.message}</span>
                  </div>
                </div>
              )}

              {operationError && (
                <div className="mb-3">
                  <div className="alert alert-error alert-sm">
                    <AlertCircleIcon className="h-3 w-3" />
                    <span className="text-xs">{operationError.message}</span>
                  </div>
                </div>
              )} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
