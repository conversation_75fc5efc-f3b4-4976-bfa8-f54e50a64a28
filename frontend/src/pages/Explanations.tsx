import { AlertTriangleIcon, BookOpenIcon, FileTextIcon, GraduationCapIcon } from "lucide-react";

export default function Explanations() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <BookOpenIcon className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-primary mb-2">Explanations</h1>
            <p className="text-base-content/70 text-lg">Detailed explanations and guidance for security findings</p>
          </div>
        </div>
      </div>

      {/* Coming Soon Card */}
      <div className="card bg-base-100 shadow border border-base-300">
        <div className="card-body">
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="p-4 bg-warning/10 rounded-full mb-6">
              <AlertTriangleIcon className="h-12 w-12 text-warning" />
            </div>
            <h2 className="text-2xl font-bold text-base-content mb-4">Coming Soon</h2>
            <p className="text-base-content/70 text-lg mb-8 max-w-2xl leading-relaxed">
              The Explanations module is currently under development. This feature will provide comprehensive explanations, educational content, and detailed guidance for
              understanding and addressing security vulnerabilities discovered during scans.
            </p>

            {/* Feature Preview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl">
              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <FileTextIcon className="h-8 w-8 text-success mb-3" />
                  <h3 className="font-semibold text-base-content">Vulnerability Details</h3>
                  <p className="text-sm text-base-content/60">In-depth explanations of security vulnerabilities</p>
                </div>
              </div>

              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <GraduationCapIcon className="h-8 w-8 text-info mb-3" />
                  <h3 className="font-semibold text-base-content">Learning Resources</h3>
                  <p className="text-sm text-base-content/60">Educational content and best practices</p>
                </div>
              </div>

              <div className="card bg-base-200/50 border border-base-300">
                <div className="card-body items-center text-center">
                  <BookOpenIcon className="h-8 w-8 text-secondary mb-3" />
                  <h3 className="font-semibold text-base-content">Step-by-Step Guides</h3>
                  <p className="text-sm text-base-content/60">Detailed remediation and mitigation guides</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
