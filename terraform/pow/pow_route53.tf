# Route53 DNS configuration for PoW targets

# Try to find existing hosted zone (only if not creating new one)
data "aws_route53_zone" "pow_zone_existing" {
  count = local.create_pow_resources > 0 && !var.pow_create_hosted_zone ? 1 : 0
  
  name         = var.pow_domain_name
  private_zone = false
}

# Create new hosted zone if requested
resource "aws_route53_zone" "pow_zone_new" {
  count = local.create_pow_resources > 0 && var.pow_create_hosted_zone ? 1 : 0
  
  name = var.pow_domain_name
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-zone"
  })
}

# Use appropriate zone ID based on whether we're creating or using existing
locals {
  pow_zone_id = local.create_pow_resources > 0 ? (
    var.pow_create_hosted_zone ? 
    aws_route53_zone.pow_zone_new[0].zone_id : 
    data.aws_route53_zone.pow_zone_existing[0].zone_id
  ) : null
}

# Wildcard DNS record pointing to the ALB
resource "aws_route53_record" "pow_wildcard_dns" {
  count = local.create_pow_resources
  
  zone_id = local.pow_zone_id
  name    = "*.${var.pow_domain_name}"
  type    = "A"

  alias {
    name                   = aws_lb.pow_alb[0].dns_name
    zone_id                = aws_lb.pow_alb[0].zone_id
    evaluate_target_health = true
  }
}

# Also add a root domain record pointing to the ALB
resource "aws_route53_record" "pow_root_dns" {
  count = local.create_pow_resources
  
  zone_id = local.pow_zone_id
  name    = var.pow_domain_name
  type    = "A"

  alias {
    name                   = aws_lb.pow_alb[0].dns_name
    zone_id                = aws_lb.pow_alb[0].zone_id
    evaluate_target_health = true
  }
}

# Output the hosted zone ID (useful for reference)
output "pow_zone_id" {
  description = "The Route53 hosted zone ID for the PoW domain"
  value       = local.pow_zone_id
}

# Automatically update domain nameservers to match the hosted zone
resource "aws_route53domains_registered_domain" "pow_domain" {
  count = local.create_pow_resources > 0 && var.pow_create_hosted_zone ? 1 : 0
  
  domain_name = var.pow_domain_name
  
  dynamic "name_server" {
    for_each = aws_route53_zone.pow_zone_new[0].name_servers
    content {
      name = name_server.value
    }
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-domain"
  })
}

# Output name servers for the hosted zone (useful for domain configuration)
output "pow_zone_name_servers" {
  description = "Name servers for the PoW hosted zone"
  value = local.create_pow_resources > 0 ? (
    var.pow_create_hosted_zone ? 
    aws_route53_zone.pow_zone_new[0].name_servers : 
    data.aws_route53_zone.pow_zone_existing[0].name_servers
  ) : null
}

