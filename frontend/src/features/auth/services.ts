import { authService } from "@/lib/auth";
import type { AuthUser } from "./types";

// Login function that uses JWT authentication
export const login = async (email: string, password: string): Promise<AuthUser> => {
  try {
    await authService.login(email, password);

    // For demo purposes, create user from email
    // In production, user info would come from JWT token payload
    const username = email.split("@")[0];
    const user = {
      id: email,
      email: email,
      name: username ? username.charAt(0).toUpperCase() + username.slice(1) : "User",
    };

    // Store user info in localStorage for persistence
    // This is safe because we only store it after successful JWT authentication
    const authData = {
      user,
      isAuthenticated: true,
      isLoading: false,
      error: null,
    };
    localStorage.setItem("auth", JSON.stringify(authData));

    return user;
  } catch (_error) {
    throw new Error("Invalid email or password");
  }
};

// Logout function
export const logout = (): boolean => {
  authService.logout(); // This now clears both JWT token and auth data
  return true;
};
