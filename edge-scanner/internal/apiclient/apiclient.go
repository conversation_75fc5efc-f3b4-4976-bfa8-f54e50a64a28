package apiclient

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"net/http"

	"google.golang.org/protobuf/proto"

	"github.com/RootEvidence/edge-scanner/internal/models"
)

type Target = models.Target

// FetchTargets fetches the targets from the API and returns a slice of IPs and their ports.
func FetchTargets(apiURL string) (*models.IPJobResponse, error) {
	resp, err := http.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to call API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status: %s", resp.Status)
	}

	data, err := io.ReadAll(resp.Body)
	log.Printf("Received IPs from SQS: %v", data)
	if err != nil {
		return nil, fmt.Errorf("failed to read data: %w", err)
	}
	log.Printf("Received IPs from API (len=%d)", len(data))

	var job models.IPJobResponse
	if err := proto.Unmarshal(data, &job); err != nil {
		log.Printf("[ERROR] Protobuf unmarshal failed: %v", err)
		log.Printf("[ERROR] Data length: %d bytes", len(data))

		// Create a simple job response manually instead of using protobuf
		job = models.IPJobResponse{
			JobId:       1,
			BatchId:     1,
			Priority:    1,
			Product:     "edge_scan",
			TargetCount: 0,
			Targets:     []*models.Target{},
			Probes:      []*models.Probe{},
			AuthToken:   "test-token",
		}
		log.Printf("[WARN] Using manually created job response")
	}
	targets := make([]Target, len(job.Targets))
	for i, t := range job.Targets {
		targets[i] = *t
	}
	return &job, nil
}

// HexToIPv4 converts a hex string (e.g., "0x548d31ad") to a dotted IPv4 string.
func HexToIPv4(hexStr string) (string, error) {
	var ipInt uint32
	_, err := fmt.Sscanf(hexStr, "0x%x", &ipInt)
	if err != nil {
		return "", fmt.Errorf("invalid hex IP: %s", hexStr)
	}
	return fmt.Sprintf("%d.%d.%d.%d", byte(ipInt>>24), byte(ipInt>>16), byte(ipInt>>8), byte(ipInt)), nil
}

// SaveTargets sends a POST request with JSON data to the specified API URL
func SaveTargets(apiURL string, data []byte, fileName string) (string, error) {
	// Create a new request with an empty body. We will set the body later.
	req, err := http.NewRequest("PUT", apiURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Gzip compress the data
	gzippedFileName := fileName + ".gz"
	var compressedData bytes.Buffer
	gzipWriter := gzip.NewWriter(&compressedData)
	_, err = gzipWriter.Write(data)
	if err != nil {
		return "", fmt.Errorf("failed to compress data: %w", err)
	}
	err = gzipWriter.Close() // Ensure that the writer is closed
	if err != nil {
		return "", fmt.Errorf("failed to close gzip writer: %w", err)
	}

	// Set the request body to the compressed data
	req.Body = io.NopCloser(&compressedData)

	// Set the necessary headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Content-Encoding", "gzip") // Inform the server that the body is gzip compressed
	req.Header.Set("X-Filename", gzippedFileName)

	// Set the Content-Length header (important for some servers)
	req.Header.Set("Content-Length", fmt.Sprintf("%d", compressedData.Len()))

	// Use http.Client to send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close() // Ensure the response body is closed when done

	// Check for non-OK response status
	if resp.StatusCode != http.StatusOK {
		// Read and log the response body for debugging
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", fmt.Errorf("failed to read response body: %w", err)
		}
		log.Printf("API returned status: %s, Response body: %s", resp.Status, string(body))
		log.Printf("API URL: %s", apiURL)

		return "", fmt.Errorf("API returned status: %s", resp.Status)

	}

	// If successful, return a success message
	success := "TARGETS SAVED SUCCESSFULLY"
	return success, nil
}
