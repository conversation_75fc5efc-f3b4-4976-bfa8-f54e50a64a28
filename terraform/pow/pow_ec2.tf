# EC2 instances configuration for PoW targets

# Security Group for the PoW EC2 instances
resource "aws_security_group" "pow_ec2_sg" {
  count = local.create_pow_resources
  
  name        = "${var.project_name}-pow-ec2-sg"
  description = "Allow HTTP inbound traffic from PoW ALB to EC2 instances"
  vpc_id      = local.vpc_id

  # Allow HTTP access from anywhere (required for NLB and Lambda scanning)
  ingress {
    description = "HTTP from anywhere (NLB + Lambda scanner)"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"] # Allow all outbound for updates, etc.
  }
  
  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-ec2-sg"
  })
}

# Latest Amazon Linux 2 AMI
data "aws_ami" "amazon_linux_2" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# User Data script to set up a simple HTTP server
locals {
  user_data = <<-EOF
    #!/bin/bash
    
    # Enable logging for debugging (fixed syntax)
    exec > /var/log/user-data.log 2>&1
    echo "Starting user data script execution at $(date)"
    
    # Update system packages
    echo "Updating system packages..."
    yum update -y
    
    # Install nginx using amazon-linux-extras (correct method for Amazon Linux 2)
    echo "Installing nginx..."
    amazon-linux-extras install nginx1 -y
    
    # Get instance metadata
    echo "Retrieving instance metadata..."
    INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
    AZ=$(curl -s http://***************/latest/meta-data/placement/availability-zone)
    
    # Create simple HTML content
    echo "Creating HTML content..."
    cat > /usr/share/nginx/html/index.html << 'HTML'
<!DOCTYPE html>
<html>
<head>
  <title>FastScan PoW Target</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; color: #333; }
    h1 { color: #0066cc; }
    .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
  </style>
</head>
<body>
  <h1>FastScan PoW Target</h1>
  <div class="status">
    <strong>Status:</strong> Online and ready for scanning
  </div>
  <p>This is a test target for Nuclei vulnerability scanning.</p>
  <p>Instance ID: <code>INSTANCE_ID_PLACEHOLDER</code></p>
  <p>Availability Zone: <code>AZ_PLACEHOLDER</code></p>
</body>
</html>
HTML
    
    # Replace placeholders
    sed -i "s/INSTANCE_ID_PLACEHOLDER/$INSTANCE_ID/g" /usr/share/nginx/html/index.html
    sed -i "s/AZ_PLACEHOLDER/$AZ/g" /usr/share/nginx/html/index.html
    
    # Create health check endpoint
    echo "healthy" > /usr/share/nginx/html/health
    
    # Start and enable nginx
    echo "Starting nginx service..."
    systemctl start nginx
    systemctl enable nginx
    
    # Verify nginx is running
    echo "Verifying nginx status..."
    systemctl status nginx
    
    # Test local connectivity
    echo "Testing local connectivity..."
    sleep 3
    curl -f http://localhost/ && echo "Root endpoint OK" || echo "Root endpoint FAILED"
    curl -f http://localhost/health && echo "Health endpoint OK" || echo "Health endpoint FAILED"
    
    echo "User data script completed at $(date)"
    EOF
}


# Single normal target EC2 instance
resource "aws_instance" "pow_target_normal" {
  count = local.create_pow_resources
  
  ami                         = data.aws_ami.amazon_linux_2.id
  instance_type               = var.pow_instance_type
  subnet_id                   = local.public_subnet_ids[0]
  vpc_security_group_ids      = [aws_security_group.pow_ec2_sg[0].id]
  associate_public_ip_address = true
  user_data                   = local.user_data
  key_name                    = var.pow_key_name

  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-normal-target"
    Type = "SAFE-TARGET"
  })
}

# Attach normal target to load balancer
resource "aws_lb_target_group_attachment" "pow_attach_normal" {
  count = local.create_pow_resources
  
  target_group_arn = aws_lb_target_group.pow_ec2_tg[0].arn
  target_id        = aws_instance.pow_target_normal[0].id
  port             = 80
}

# Vulnerable target user data script
locals {
  vulnerable_user_data = <<-EOF
    #!/bin/bash
    
    # Enable logging for debugging
    exec > /var/log/vulnerable-user-data.log 2>&1
    echo "Starting vulnerable target user data script execution at $(date)"
    
    # Update system packages
    echo "Updating system packages..."
    yum update -y
    
    # Install Python 3
    echo "Installing Python 3..."
    yum install -y python3 python3-pip

    # Allow non-root users to bind to port 80
    echo "Configuring port permissions..."
    echo "net.ipv4.ip_unprivileged_port_start=80" >> /etc/sysctl.conf
    sysctl -p

    # Create vulnerable server script
    echo "Creating vulnerable Apache server script..."
    cat > /opt/vulnerable-apache.py << 'PYTHON_SCRIPT'
${file("${path.module}/vulnerable-apache-server.py")}
PYTHON_SCRIPT

    # Verify script was created successfully
    if [ ! -f /opt/vulnerable-apache.py ]; then
        echo "ERROR: Failed to create vulnerable server script"
        exit 1
    fi

    # Make the script executable
    chmod +x /opt/vulnerable-apache.py
    
    # Create systemd service
    echo "Creating systemd service..."
    cat > /etc/systemd/system/vulnerable-apache.service << 'SERVICE'
[Unit]
Description=Vulnerable Apache 2.4.49 Simulator
After=network.target

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt
ExecStart=/usr/bin/python3 /opt/vulnerable-apache.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
SERVICE
    
    # Start service
    echo "Starting vulnerable Apache service..."
    systemctl daemon-reload
    systemctl enable vulnerable-apache
    systemctl start vulnerable-apache
    echo "Vulnerable target user data script completed at $(date)"
    EOF
}

# Single vulnerable EC2 instance
resource "aws_instance" "pow_vulnerable_target" {
  count = local.create_pow_resources
  
  ami                         = data.aws_ami.amazon_linux_2.id
  instance_type               = var.pow_instance_type
  subnet_id                   = local.public_subnet_ids[0]
  vpc_security_group_ids      = [aws_security_group.pow_ec2_sg[0].id]
  associate_public_ip_address = true
  user_data                   = local.vulnerable_user_data
  key_name                    = var.pow_key_name

  tags = merge(var.tags, {
    Name = "${var.project_name}-pow-vulnerable-target"
    Type = "VULNERABLE-TEST-TARGET"
    CVE  = "CVE-2021-41773"
    Warning = "INTENTIONALLY-VULNERABLE-FOR-TESTING"
  })
}

# Attach vulnerable instance to target group
resource "aws_lb_target_group_attachment" "pow_attach_vulnerable" {
  count = local.create_pow_resources
  
  target_group_arn = aws_lb_target_group.pow_ec2_tg[0].arn
  target_id        = aws_instance.pow_vulnerable_target[0].id
  port             = 80
}

# Output EC2 instance information (optimized 2-instance setup)
output "pow_ec2_instance_ids" {
  description = "IDs of all PoW EC2 instances"
  value = local.create_pow_resources > 0 ? {
    normal_target = aws_instance.pow_target_normal[0].id
    vulnerable_target = aws_instance.pow_vulnerable_target[0].id
  } : null
}

output "pow_instance_summary" {
  description = "Summary of PoW instances (optimized 2-instance setup)"
  value = local.create_pow_resources > 0 ? {
    total_instances = 2
    normal_instances = 1
    vulnerable_instances = 1
    vulnerable_percentage = "50%"
    normal_instance_ip = aws_instance.pow_target_normal[0].public_ip
    vulnerable_instance_ip = aws_instance.pow_vulnerable_target[0].public_ip
    cost_optimization = "Reduced from 3 to 2 instances for cost savings"
  } : null
}
