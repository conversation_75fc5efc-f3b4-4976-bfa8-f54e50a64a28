package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"nuclear_pond/pkg/auth"
)

func TestAuthHandlerGeneratesValidJWT(t *testing.T) {
	// Set test environment
	os.Setenv("JWT_SECRET", "test-secret-for-auth-handler")
	os.Setenv("DEMO_PASSWORD", "testpass")
	defer os.Unsetenv("JWT_SECRET")
	defer os.Unsetenv("DEMO_PASSWORD")

	// Create auth request
	authReq := AuthRequest{Password: "testpass"}
	jsonBody, _ := json.Marshal(authReq)
	req, err := http.NewRequest("POST", "/auth", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call auth handler
	AuthHandler(rr, req) // Check response status
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, status)
	}

	// Parse response
	var authResp AuthResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &authResp); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify token is not empty
	if authResp.Token == "" {
		t.Fatal("Expected non-empty token")
	}

	// Verify token is a valid JWT (should have 3 parts separated by dots)
	tokenParts := strings.Split(authResp.Token, ".")
	if len(tokenParts) != 3 {
		t.Errorf("Expected JWT token with 3 parts, got %d parts", len(tokenParts))
	}

	// Verify token can be validated
	claims, err := auth.ValidateJWT(authResp.Token)
	if err != nil {
		t.Fatalf("Generated token failed validation: %v", err)
	}

	// Verify claims
	if claims.UserID != "demo-user" {
		t.Errorf("Expected UserID 'demo-user', got %s", claims.UserID)
	}

	if claims.Issuer != "fastscan-demo" {
		t.Errorf("Expected issuer 'fastscan-demo', got %s", claims.Issuer)
	}
}

func TestAuthHandlerInvalidPassword(t *testing.T) {
	// Set test environment
	os.Setenv("DEMO_PASSWORD", "correctpass")
	defer os.Unsetenv("DEMO_PASSWORD")

	// Create auth request with wrong password
	authReq := AuthRequest{Password: "wrongpass"}
	jsonBody, _ := json.Marshal(authReq)

	req, err := http.NewRequest("POST", "/auth", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call auth handler
	AuthHandler(rr, req)

	// Check response status
	if status := rr.Code; status != http.StatusUnauthorized {
		t.Errorf("Expected status code %d, got %d", http.StatusUnauthorized, status)
	}
}
