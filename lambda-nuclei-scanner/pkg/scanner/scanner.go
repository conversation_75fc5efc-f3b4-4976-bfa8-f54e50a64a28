package scanner

import (
	"fmt"
	"log"
	"main/internal/config"
	"main/internal/nuclei"
	"main/internal/types"
	"os"
	"strings"
)

// Scanner provides the main interface for Nuclei scanning operations
type Scanner struct {
	env *types.Environment
}

// NewScanner creates a new scanner instance with the appropriate environment
func NewScanner() (*Scanner, error) {
	var env *types.Environment
	var err error

	env, err = config.SetupLambdaEnvironment()
	if err != nil {
		return nil, fmt.Errorf("failed to setup Lambda environment: %w", err)
	}
	log.Printf("Lambda environment configured successfully. Version is %s", types.LambdaVersion)

	return &Scanner{env: env}, nil
}

// GetEnvironment returns the current environment configuration
func (s *Scanner) GetEnvironment() *types.Environment {
	return s.env
}

// ValidateEnvironment validates the scanner environment and nuclei binary
func (s *Scanner) ValidateEnvironment() error {
	return nuclei.ValidateBinary(s.env)
}

// NormalizeRequest logs request fields and ensures defaults.
func (s *Scanner) NormalizeRequest(request *types.ScanRequest) {
	log.Printf("Raw request received - Targets: %v, Output: '%s', Templates: %v, StreamOutput: %v",
		len(request.Targets), request.Output, len(request.Templates), request.StreamOutput)

	if request.Output == "" {
		request.Output = types.OutputModeJSON
	}
}

// Scan executes a nuclei scan and returns a structured response only.
// Any errors encountered are embedded into the response's Error field.
func (s *Scanner) Scan(request *types.ScanRequest) (*types.StructuredBatchResponse, error) {
	s.NormalizeRequest(request)

	// Log received request for debugging
	log.Printf("Scanner received request: Targets=%d, Output=%s, Templates=%v, StreamOutput=%v",
		len(request.Targets), request.Output, len(request.Templates), request.StreamOutput)

	// Prepare base structured response metadata
	scanID := strings.ReplaceAll(request.ScanID, "-", "")
	if scanID == "" {
		scanID = ""
	}
	batchID := request.BatchID
	if batchID == "" {
		batchID = "batch-1"
	}
	structured := &types.StructuredBatchResponse{
		ScanID:  scanID,
		BatchID: batchID,
	}

	// Validate environment
	if err := s.ValidateEnvironment(); err != nil {
		structured.Error = fmt.Sprintf("Environment validation failed: %v", err)

		return structured, nil
	}

	// Prepare nuclei command
	cmdArgs, err := nuclei.PrepareCommand(s.env, request)
	if err != nil {
		structured.Error = fmt.Sprintf("Command preparation failed: %v", err)

		return structured, nil
	}

	// Execute nuclei with validation
	// Determine output mode (default to JSON for structured parsing)
	outputMode := request.Output
	if outputMode == "" {
		outputMode = types.OutputModeJSON
	}
	result, execErr := nuclei.ExecuteWithValidation(s.env, cmdArgs, outputMode, request.StreamOutput)

	// Parse findings when JSON mode
	var findings []any
	if outputMode == types.OutputModeJSON {
		if parsed, perr := nuclei.ParseJSONFindings(s.env.ScanOutput); perr == nil {
			findings = parsed
		} else {
			log.Printf("Failed to parse JSON findings file: %v", perr)
		}
	}
	structured.Findings = findings

	// Cleanup
	s.Cleanup(request, result)

	if execErr != nil {
		msg := execErr.Error()
		if len(msg) > 2048 {
			msg = msg[:2048]
		}
		structured.Error = msg
	}

	return structured, nil
}

// Cleanup performs post-scan cleanup
func (s *Scanner) Cleanup(request *types.ScanRequest, result *types.NucleiResult) {
	// Remove targets file if multiple targets were used
	if len(request.Targets) > 1 {
		os.Remove(s.env.TargetsFile)
	}
	// Remove scan output file
	os.Remove(s.env.ScanOutput)

	log.Printf("Cleanup completed ")
}
