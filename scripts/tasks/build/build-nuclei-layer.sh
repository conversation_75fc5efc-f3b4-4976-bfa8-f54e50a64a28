#!/bin/bash

# Build Nuclei Binary Layer
# Downloads Nuclei binary from GitHub releases and packages it as a Lambda layer

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"

# Configuration
NUCLEI_VERSION="${NUCLEI_VERSION:-3.1.7}"
NUCLEI_ARCH="${NUCLEI_ARCH:-linux_amd64}"
NUCLEI_REPO="projectdiscovery/nuclei"

# Validate input parameters
if [[ $# -ne 1 ]]; then
    log_error "Usage: $0 <output_directory>"
    log_error "Example: $0 /path/to/artifacts"
    exit 1
fi

OUTPUT_DIR="$1"
TEMP_DIR=$(mktemp -d)
LAYER_DIR="${TEMP_DIR}/nuclei-layer"

# Cleanup function
cleanup() {
    if [[ -d "${TEMP_DIR}" ]]; then
        rm -rf "${TEMP_DIR}"
    fi
}

trap cleanup EXIT

# Create layer directory structure
create_layer_structure() {
    log_info "Creating layer directory structure..."
    # AWS Lambda layers are extracted to /opt, so we don't need an opt/ directory in the zip
    # The binary should be at the root of the zip so it becomes /opt/nuclei when extracted
    mkdir -p "${LAYER_DIR}"
    log_success "Layer directory created: ${LAYER_DIR}"
}

# Download Nuclei binary
download_nuclei_binary() {
    log_info "Downloading Nuclei v${NUCLEI_VERSION} for ${NUCLEI_ARCH}..."
    
    local download_url="https://github.com/${NUCLEI_REPO}/releases/download/v${NUCLEI_VERSION}/nuclei_${NUCLEI_VERSION}_${NUCLEI_ARCH}.zip"
    local download_file="${TEMP_DIR}/nuclei.zip"
    
    log_info "Download URL: ${download_url}"
    
    # Download with curl (with retries and proper error handling)
    if ! curl -L -f -o "${download_file}" "${download_url}" --retry 3 --retry-delay 2; then
        log_error "Failed to download Nuclei binary from: ${download_url}"
        log_error "Please check if version ${NUCLEI_VERSION} exists for architecture ${NUCLEI_ARCH}"
        return 1
    fi
    
    # Validate download
    if [[ ! -f "${download_file}" ]]; then
        log_error "Downloaded file not found: ${download_file}"
        return 1
    fi
    
    local file_size=$(stat -f%z "${download_file}" 2>/dev/null || stat -c%s "${download_file}" 2>/dev/null || echo "0")
    if [[ "${file_size}" -eq 0 ]]; then
        log_error "Downloaded file is empty: ${download_file}"
        return 1
    fi
    
    log_success "Downloaded Nuclei binary (${file_size} bytes)"
    
    # Extract the binary
    log_info "Extracting Nuclei binary..."
    if ! unzip -q "${download_file}" -d "${TEMP_DIR}"; then
        log_error "Failed to extract Nuclei binary"
        return 1
    fi
    
    # Find the nuclei binary (it should be in the extracted files)
    local nuclei_binary="${TEMP_DIR}/nuclei"
    if [[ ! -f "${nuclei_binary}" ]]; then
        log_error "Nuclei binary not found after extraction: ${nuclei_binary}"
        log_error "Available files:"
        ls -la "${TEMP_DIR}"
        return 1
    fi
    
    # Validate binary is executable
    if [[ ! -x "${nuclei_binary}" ]]; then
        log_info "Making Nuclei binary executable..."
        chmod +x "${nuclei_binary}"
    fi
    
    # Move binary to layer root directory (not inside opt/) and ensure it's executable
    # AWS Lambda will extract this to /opt, so nuclei at root becomes /opt/nuclei
    mv "${nuclei_binary}" "${LAYER_DIR}/nuclei"
    chmod +x "${LAYER_DIR}/nuclei"
    log_success "Nuclei binary placed in layer root directory with executable permissions"
}

# Validate binary integrity
validate_binary() {
    log_info "Validating Nuclei binary..."
    
    local nuclei_binary="${LAYER_DIR}/nuclei"
    
    # Check if binary exists and is executable
    if [[ ! -f "${nuclei_binary}" ]]; then
        log_error "Nuclei binary not found: ${nuclei_binary}"
        return 1
    fi
    
    if [[ ! -x "${nuclei_binary}" ]]; then
        log_error "Nuclei binary is not executable: ${nuclei_binary}"
        return 1
    fi
    
    # Check binary size (should be reasonable, > 1MB)
    local binary_size=$(stat -f%z "${nuclei_binary}" 2>/dev/null || stat -c%s "${nuclei_binary}" 2>/dev/null || echo "0")
    local min_size=$((1024 * 1024)) # 1MB
    
    if [[ "${binary_size}" -lt "${min_size}" ]]; then
        log_error "Nuclei binary seems too small (${binary_size} bytes). Expected > ${min_size} bytes"
        return 1
    fi
    
    # Try to get version (this validates the binary works)
    log_info "Testing Nuclei binary..."
    if ! "${nuclei_binary}" -version >/dev/null 2>&1; then
        log_warning "Could not get version from Nuclei binary (this might be normal in some environments)"
    else
        local version_output=$("${nuclei_binary}" -version 2>&1 | head -1 || echo "unknown")
        log_success "Nuclei binary validated: ${version_output}"
    fi
    
    log_success "Binary validation completed (${binary_size} bytes)"
}

# Create layer zip file
create_layer_zip() {
    log_info "Creating Nuclei layer zip file..."
    
    # Use absolute path for output file
    local output_file="$(cd "${OUTPUT_DIR}" && pwd)/nuclei-layer.zip"
    
    # Ensure output directory exists
    mkdir -p "${OUTPUT_DIR}"
    
    # Debug: Check layer directory contents
    log_info "Layer directory contents before zipping:"
    ls -la "${LAYER_DIR}"
    
    # Create zip file from layer directory
    cd "${LAYER_DIR}"
    log_info "Current directory: $(pwd)"
    log_info "Creating zip file: ${output_file}"
    
    if ! zip -r "${output_file}" . 2>&1; then
        log_error "Failed to create layer zip file: ${output_file}"
        log_error "Zip command output above"
        return 1
    fi
    
    # Validate created zip file
    if [[ ! -f "${output_file}" ]]; then
        log_error "Layer zip file not created: ${output_file}"
        return 1
    fi
    
    if ! unzip -t "${output_file}" >/dev/null 2>&1; then
        log_error "Created zip file is invalid: ${output_file}"
        return 1
    fi
    
    local zip_size=$(stat -f%z "${output_file}" 2>/dev/null || stat -c%s "${output_file}" 2>/dev/null || echo "0")
    log_success "Nuclei layer created: ${output_file} (${zip_size} bytes)"
    
    # List contents for verification
    log_info "Layer contents (will be extracted to /opt in Lambda):"
    unzip -l "${output_file}" | grep -E "^\s*[0-9]+" | while read -r line; do
        echo "  ${line}"
    done
    
    # Verify the structure is correct (nuclei should be at root, not in opt/)
    if unzip -l "${output_file}" | grep -q "opt/nuclei"; then
        log_error "INCORRECT STRUCTURE: Binary is inside opt/ directory in zip file"
        log_error "This will result in /opt/opt/nuclei path in Lambda, causing 'file not found' errors"
        return 1
    elif unzip -l "${output_file}" | grep -q "^.*nuclei$"; then
        log_success "CORRECT STRUCTURE: Binary is at root of zip file (will become /opt/nuclei in Lambda)"
    else
        log_warning "Could not verify nuclei binary location in zip file"
    fi
}

# Main execution
main() {
    log_info "Building Nuclei binary layer..."
    log_info "Version: ${NUCLEI_VERSION}"
    log_info "Architecture: ${NUCLEI_ARCH}"
    log_info "Output directory: ${OUTPUT_DIR}"
    
    # Create layer structure
    if ! create_layer_structure; then
        log_error "Failed to create layer structure"
        exit 1
    fi
    
    # Download and extract binary
    if ! download_nuclei_binary; then
        log_error "Failed to download Nuclei binary"
        exit 1
    fi
    
    # Validate binary
    if ! validate_binary; then
        log_error "Binary validation failed"
        exit 1
    fi
    
    # Create layer zip
    if ! create_layer_zip; then
        log_error "Failed to create layer zip"
        exit 1
    fi
    
    log_success "Nuclei layer build completed successfully!"
}

# Execute main function
main "$@"