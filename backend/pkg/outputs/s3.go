package outputs

import (
	"bufio"
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"nuclear_pond/pkg/aws"
	"strings"
	"time"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
)

// encodeNDJSON converts findings into NDJSON bytes.
func encodeNDJSON(findings []map[string]any) ([]byte, error) {
	var buf bytes.Buffer
	w := bufio.NewWriter(&buf)
	enc := json.NewEncoder(w)
	for _, f := range findings {
		if err := enc.Encode(f); err != nil {
			return nil, fmt.Errorf("encode NDJSON: %w", err)
		}
	}
	if err := w.Flush(); err != nil {
		return nil, fmt.Errorf("flush NDJSON: %w", err)
	}

	return buf.Bytes(), nil
}

// UploadFindingsToS3 uploads per-batch findings as NDJSON and sets useful tags/metadata.
func UploadFindingsToS3(scanID, batchID string, findings []map[string]any, meta map[string]string) (string, error) {
	awsConfig := aws.NewAWSConfig()
	if awsConfig.S3Bucket == "" {
		return "", fmt.Errorf("S3 bucket not configured")
	}
	sess, err := awsConfig.CreateSession()
	if err != nil {
		return "", fmt.Errorf("aws session: %w", err)
	}
	svc := s3.New(sess)

	body, err := encodeNDJSON(findings)
	if err != nil {
		return "", err
	}

	sum := sha256.Sum256(body)
	sumHex := hex.EncodeToString(sum[:])
	key := fmt.Sprintf("findings/%s/nuclei-findings-%s.jsonl", scanID, batchID)

	var tags []string
	appendTag := func(k, v string) {
		if v != "" {
			tags = append(tags, fmt.Sprintf("%s=%s", k, v))
		}
	}
	appendTag("scan-id", scanID)
	appendTag("batch-id", batchID)
	for k, v := range meta {
		appendTag(k, v)
	}

	_, err = svc.PutObject(&s3.PutObjectInput{
		Bucket:      awssdk.String(awsConfig.S3Bucket),
		Key:         awssdk.String(key),
		Body:        bytes.NewReader(body),
		ContentType: awssdk.String("application/x-ndjson"),
		Metadata:    map[string]*string{"scan-id": awssdk.String(scanID), "batch-id": awssdk.String(batchID), "sha256": awssdk.String(sumHex)},
		Tagging:     awssdk.String(strings.Join(tags, "&")),
		Expires:     awssdk.Time(time.Now().Add(30 * 24 * time.Hour)),
	})
	if err != nil {
		return "", fmt.Errorf("upload batch findings: %w", err)
	}

	return key, nil
}

// UploadAggregatedFindingsToS3 uploads aggregated findings for a scan.
func UploadAggregatedFindingsToS3(scanID string, findings []map[string]any, meta map[string]string) (string, error) {
	awsConfig := aws.NewAWSConfig()
	if awsConfig.S3Bucket == "" {
		return "", fmt.Errorf("S3 bucket not configured")
	}
	sess, err := awsConfig.CreateSession()
	if err != nil {
		return "", fmt.Errorf("aws session: %w", err)
	}
	svc := s3.New(sess)

	body, err := encodeNDJSON(findings)
	if err != nil {
		return "", err
	}

	sum := sha256.Sum256(body)
	sumHex := hex.EncodeToString(sum[:])
	key := fmt.Sprintf("findings/%s/nuclei-findings-aggregated.jsonl", scanID)

	var tags []string
	appendTag := func(k, v string) {
		if v != "" {
			tags = append(tags, fmt.Sprintf("%s=%s", k, v))
		}
	}
	appendTag("scan-id", scanID)
	for k, v := range meta {
		appendTag(k, v)
	}

	_, err = svc.PutObject(&s3.PutObjectInput{
		Bucket:      awssdk.String(awsConfig.S3Bucket),
		Key:         awssdk.String(key),
		Body:        bytes.NewReader(body),
		ContentType: awssdk.String("application/x-ndjson"),
		Metadata:    map[string]*string{"scan-id": awssdk.String(scanID), "sha256": awssdk.String(sumHex)},
		Tagging:     awssdk.String(strings.Join(tags, "&")),
		Expires:     awssdk.Time(time.Now().Add(30 * 24 * time.Hour)),
	})
	if err != nil {
		return "", fmt.Errorf("upload aggregated findings: %w", err)
	}

	return key, nil
}
