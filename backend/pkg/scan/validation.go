package scan

import (
	"fmt"
	"log"
	"strings"
)

// ValidateTemplateSelections ensures provided template IDs exist in metadata.
func ValidateTemplateSelections(templateIDs []string) error {
	if len(templateIDs) == 0 {
		return nil
	}

	tmplIndex, err := getTemplateMetadata()
	if err != nil {
		// Return clear messages by common error types
		msg := err.Error()
		switch {
		case strings.Contains(msg, "not found") || strings.Contains(msg, "NoSuchKey"):
			return fmt.Errorf("template validation failed: template metadata not available; try again later")
		case strings.Contains(msg, "AccessDenied") || strings.Contains(msg, "Forbidden"):
			return fmt.Errorf("template validation failed: access denied to template metadata")
		case strings.Contains(msg, "NoSuchBucket"):
			return fmt.Errorf("template validation failed: templates bucket not configured")
		default:
			return fmt.Errorf("template validation failed: %w", err)
		}
	}
	if tmplIndex.Templates == nil || len(tmplIndex.Templates) == 0 {
		return fmt.Erro<PERSON>("template validation failed: no templates are available")
	}

	var missing []string
	for _, id := range templateIDs {
		if _, ok := tmplIndex.Templates[id]; !ok {
			missing = append(missing, id)
		}
	}
	if len(missing) > 0 {
		// Provide a short hint of availability without dumping huge lists
		available := make([]string, 0, len(tmplIndex.Templates))
		for id := range tmplIndex.Templates {
			available = append(available, id)
		}
		log.Printf("Template validation: missing=%d of %d requested", len(missing), len(templateIDs))
		hint := available
		if len(hint) > 20 {
			hint = hint[:20]
		}

		return fmt.Errorf("templates not found: %v. Example available IDs (%d total): %v", missing, len(available), hint)
	}

	return nil
}
