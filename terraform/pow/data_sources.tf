# Network configuration locals
# Using input variables instead of data sources for better dependency management
locals {
  vpc_id             = var.vpc_id
  public_subnet_ids  = var.public_subnet_ids
  private_subnet_ids = var.private_subnet_ids
}

# Output the network values for use in other resources
output "vpc_id" {
  description = "VPC ID used by PoW targets"
  value       = local.vpc_id
}

output "public_subnet_ids" {
  description = "Public subnet IDs used by PoW targets"
  value       = local.public_subnet_ids
}

output "private_subnet_ids" {
  description = "Private subnet IDs used by PoW targets"
  value       = local.private_subnet_ids
}