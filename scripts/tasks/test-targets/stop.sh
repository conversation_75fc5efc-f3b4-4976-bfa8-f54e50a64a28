#!/bin/bash

# Test Targets Stop Script
# Stops and removes only the test target containers without affecting other services

set -euo pipefail


# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if docker-compose.yml exists
check_compose_file() {
    if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        error "docker-compose.yml not found in project root: $PROJECT_ROOT"
        exit 1
    fi
}

# Check if test targets are running
check_test_targets_running() {
    cd "$PROJECT_ROOT"
    
    local running_containers
    running_containers=$(docker-compose ps -q test-app-1 test-app-2 test-proxy 2>/dev/null | wc -l)
    
    if [[ $running_containers -eq 0 ]]; then
        warn "No test target containers are currently running"
        return 1
    fi
    
    return 0
}

# Stop test target containers
stop_test_targets() {
    info "Stopping test target containers..."
    
    cd "$PROJECT_ROOT"
    
    # Stop and remove test target services and proxy
    docker-compose stop test-app-1 test-app-2 test-proxy
    docker-compose rm -f test-app-1 test-app-2 test-proxy
    
    if [[ $? -eq 0 ]]; then
        success "Test target containers stopped and removed successfully"
    else
        error "Failed to stop test target containers"
        exit 1
    fi
}

# Clean up test target resources
cleanup_resources() {
    info "Cleaning up test target resources..."
    
    # Remove any dangling containers with test target names
    local containers_to_remove
    containers_to_remove=$(docker ps -aq --filter "name=fastscan-test-app" 2>/dev/null || true)
    
    if [[ -n "$containers_to_remove" ]]; then
        info "Removing dangling test target containers..."
        docker rm -f $containers_to_remove >/dev/null 2>&1 || true
    fi
    
    # Clean up any unused networks (but preserve the main fastscan_network)
    docker network prune -f >/dev/null 2>&1 || true
    
    success "Resource cleanup completed"
}

# Verify containers are stopped
verify_stopped() {
    info "Verifying test targets are stopped..."
    
    local running_containers
    running_containers=$(docker ps --filter "name=fastscan-test-app" --format "{{.Names}}" | wc -l)
    
    if [[ $running_containers -eq 0 ]]; then
        success "All test target containers are stopped"
        return 0
    else
        warn "Some test target containers may still be running"
        docker ps --filter "name=fastscan-test-app" --format "table {{.Names}}\t{{.Status}}"
        return 1
    fi
}

# Show status after stopping
show_status() {
    info "Test target status after stopping:"
    echo ""
    
    # Check for any remaining test target containers
    local remaining_containers
    remaining_containers=$(docker ps -a --filter "name=fastscan-test-app" --format "table {{.Names}}\t{{.Status}}" | grep -v NAMES || true)
    
    if [[ -n "$remaining_containers" ]]; then
        warn "Remaining test target containers:"
        echo "$remaining_containers"
    else
        success "No test target containers found"
    fi
    
    echo ""
    info "Test targets are now stopped"
    info "Use 'devbox run test-targets:start' to start them again"
}

# Force stop function for emergency situations
force_stop() {
    info "Force stopping all test target containers..."
    
    # Kill any containers with test-app names
    local containers_to_kill
    containers_to_kill=$(docker ps -q --filter "name=fastscan-test-app" 2>/dev/null || true)
    
    if [[ -n "$containers_to_kill" ]]; then
        docker kill $containers_to_kill >/dev/null 2>&1 || true
        docker rm -f $containers_to_kill >/dev/null 2>&1 || true
        success "Force stopped all test target containers"
    else
        info "No test target containers to force stop"
    fi
}

# Main execution
main() {
    local force_mode=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force|-f)
                force_mode=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    info "FastScan Test Targets - Stop Script"
    echo ""
    
    # Pre-flight checks
    check_docker
    check_compose_file
    
    if [[ "$force_mode" == true ]]; then
        force_stop
        cleanup_resources
    else
        # Check if test targets are running
        if check_test_targets_running; then
            # Stop containers gracefully
            stop_test_targets
            
            # Clean up resources
            cleanup_resources
            
            # Verify they're stopped
            verify_stopped
        else
            info "No test targets to stop"
        fi
    fi
    
    # Show final status
    echo ""
    show_status
    
    echo ""
    success "Test targets stopped successfully!"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Stop FastScan test target containers"
        echo ""
        echo "Options:"
        echo "  --help, -h    Show this help message"
        echo "  --force, -f   Force stop all test target containers"
        echo "  --status      Show current status of test targets"
        echo ""
        exit 0
        ;;
    --status)
        show_status
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac