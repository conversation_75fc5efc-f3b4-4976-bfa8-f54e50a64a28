import type { BackendScanStatusResponse } from "@/features/scans/services";
import { AlertTriangleIcon, CheckCircleIcon, ExternalLinkIcon, InfoIcon, ShieldCheckIcon } from "lucide-react";
import type React from "react";
import { VulnerableTargetsList } from "./VulnerableTargetsList";

interface ExecutiveMetrics {
  totalDuration: string;
  targetCompletion: {
    scanned: number;
    total: number;
    percentage: number;
  };
  findings: number;
  vulnerabilityChecks: number;
  targetAnalysis: {
    vulnerableTargets: number;
    cleanTargets: number;
    vulnerablePercentage: string;
    cleanPercentage: string;
  };
}

interface ExecutiveSummaryProps {
  scanStatus: BackendScanStatusResponse;
  showPerformanceMetrics?: boolean;
  showVulnerableTargets?: boolean;
}

const calculateExecutiveMetrics = (scanStatus: BackendScanStatusResponse): ExecutiveMetrics => {
  // Use results_summary from either scanResults or scanStatus (for completed scans)
  const config = scanStatus?.config;

  // Calculate total vulnerability checks (targets × templates)
  const templateCount = config?.templates?.length || 10;
  const vulnerabilityChecks = (config?.target_count || 0) * templateCount;

  // Format duration
  const durationSeconds = scanStatus.completed_at ? new Date(scanStatus.completed_at).getTime() - new Date(scanStatus.created_at).getTime() : 0;
  const totalDuration = formatDuration(durationSeconds);

  // Calculate target analysis
  const findings = scanStatus.total_vulnerable_targets || 0;
  const targetsScanned = config?.target_count || 0;
  const vulnerableTargets = findings || 0;
  const cleanTargets = targetsScanned - vulnerableTargets;

  let vulnerablePercentageNumber = targetsScanned > 0 ? Math.round((vulnerableTargets / targetsScanned) * 100) : 0;
  if (vulnerablePercentageNumber === 0 && findings > 0) {
    vulnerablePercentageNumber = 1;
  }
  const vulnerablePercentage = targetsScanned > 0 ? `${vulnerablePercentageNumber}%` : "0%";

  let cleanPercentageNumber = targetsScanned > 0 ? Math.round((cleanTargets / targetsScanned) * 100) : 0;
  if (cleanPercentageNumber === 100 && findings > 0) {
    cleanPercentageNumber = 99;
  }
  const cleanPercentage = targetsScanned > 0 ? `${cleanPercentageNumber}%` : "0%";

  return {
    totalDuration,
    targetCompletion: {
      scanned: targetsScanned,
      total: config?.target_count || 0,
      percentage: (config?.target_count || 0) > 0 ? Math.round((targetsScanned / (config?.target_count || 1)) * 100) : 0,
    },
    vulnerabilityChecks,
    findings,
    targetAnalysis: {
      vulnerableTargets,
      cleanTargets,
      vulnerablePercentage,
      cleanPercentage,
    },
  };
};

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}s`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h ${minutes}m`;
};

const CVE_DETAILS_SAMPLE = {
  "curl-command":
    "curl -X 'GET' -d '' -H 'Host: vulnerable-apache:8002' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2762.73 Safari/537.36' -H 'X-Bugbounty-Hacker: fastscan' 'http://vulnerable-apache:8002/icons/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd'",
  host: "vulnerable-apache:8002",
  info: {
    author: ["daffainfo", "666asd"],
    classification: {
      cpe: "cpe:2.3:a:apache:http_server:2.4.49:*:*:*:*:*:*:*",
      "cve-id": ["cve-2021-41773"],
      "cvss-metrics": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N",
      "cvss-score": 7.5,
      "cwe-id": ["cwe-22"],
      "epss-percentile": 0.99968,
      "epss-score": 0.94398,
    },
    description:
      'A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root. If files outside of the document root are not protected by "require all denied" these requests can succeed. Additionally, this flaw could leak the source of interpreted files like CGI scripts. This issue is known to be exploited in the wild. This issue only affects Apache 2.4.49 and not earlier versions.\n',
    impact: "Successful exploitation of this vulnerability can lead to unauthorized access, data leakage, and remote code execution.\n",
    metadata: {
      "max-request": 3,
      product: "http_server",
      "shodan-query": ["Apache 2.4.49", 'cpe:"cpe:2.3:a:apache:http_server"', "apache 2.4.49"],
      vendor: "apache",
      verified: true,
    },
    name: "Apache 2.4.49 - Path Traversal and Remote Code Execution",
    reference: [
      "https://github.com/apache/httpd/commit/e150697086e70c552b2588f369f2d17815cb1782",
      "https://nvd.nist.gov/vuln/detail/cve-2021-41773",
      "https://cve.mitre.org/cgi-bin/cvename.cgi?name=cve-2021-41773",
      "https://twitter.com/ptswarm/status/1445376079548624899",
      "https://twitter.com/h4x0r_dz/status/1445401960371429381",
      "https://github.com/blasty/cve-2021-41773",
    ],
    remediation: "Upgrade Apache to version 2.4.50 or apply the relevant patch provided by the vendor.\n",
    severity: "high",
    tags: ["cve2021", "cve", "lfi", "rce", "apache", "misconfig", "traversal", "kev"],
  },
  ip: "**********",
  "matched-at": "http://vulnerable-apache:8002/icons/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd",
  "matcher-name": "LFI",
  "matcher-status": true,
  port: "8002",
  scheme: "http",
  severity: "high",
  "template-id": "CVE-2021-41773",
  "template-path": "/var/task/opt/templates/CVE-2021-41773.yaml",
  timestamp: "2025-08-05T14:18:12.456520781Z",
  type: "http",
  url: "http://vulnerable-apache:8002",
} as const;

const CVEDetailsCard: React.FC = () => {
  const info = CVE_DETAILS_SAMPLE.info;
  const refs = info.reference || [];
  const cvss = info.classification["cvss-score"];
  const cve = (info.classification["cve-id"] || [])[0];
  const severity = info.severity || "unknown";
  const title = info.name;
  const epss = info.classification["epss-score"];
  const remediation = info.remediation?.trim();

  const sevColor =
    severity.toLowerCase() === "critical"
      ? "badge-error"
      : severity.toLowerCase() === "high"
        ? "badge-error"
        : severity.toLowerCase() === "medium"
          ? "badge-warning"
          : severity.toLowerCase() === "low"
            ? "badge-info"
            : "badge-ghost";

  return (
    <div className="card bg-base-100 shadow-sm border border-base-300">
      <div className="card-body gap-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-error/10 rounded-lg">
              <AlertTriangleIcon className="h-5 w-5 text-error" />
            </div>
            <div>
              <div className="flex items-center gap-2 flex-wrap">
                <span className={`badge ${sevColor} badge-sm uppercase`}>{severity}</span>
                {cve && <span className="badge badge-outline badge-sm">{cve.toUpperCase()}</span>}
                {cvss !== undefined && <span className="badge badge-outline badge-sm">CVSS {cvss}</span>}
                {epss !== undefined && <span className="badge badge-outline badge-sm">EPSS {epss}</span>}
              </div>
              <h4 className="text-base font-semibold mt-1">{title}</h4>
              <p className="text-sm text-base-content/70 mt-1 line-clamp-3">{info.description}</p>
            </div>
          </div>
        </div>

        {remediation && (
          <div className="rounded-lg bg-base-200 p-3">
            <div className="flex items-center gap-2 text-sm font-medium">
              <InfoIcon className="h-4 w-4" />
              Remediation
            </div>
            <p className="text-sm text-base-content/80 mt-1">{remediation}</p>
          </div>
        )}

        {refs.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {refs.slice(0, 4).map((r: string) => (
              <a key={r} className="btn btn-xs btn-outline" href={r} target="_blank" rel="noreferrer">
                <ExternalLinkIcon className="h-3 w-3 mr-1" />
                Reference
              </a>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({ scanStatus, showVulnerableTargets = true }) => {
  const metrics = calculateExecutiveMetrics(scanStatus);

  // Only show executive summary for completed scans with results
  if (scanStatus.status !== "completed") {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Executive Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Target Completion */}
        <div className="card bg-base-100 shadow-sm border border-base-300">
          <div className="card-body p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-success/10 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-success" />
              </div>
              <div>
                <div className="text-2xl font-bold">{metrics.targetCompletion.total.toLocaleString()}</div>
                <div className="text-base text-base-content/70">Targets</div>
              </div>
            </div>
          </div>
        </div>

        {/* Vulnerability Checks */}
        <div className="card bg-base-100 shadow-sm border border-base-300">
          <div className="card-body p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-info/10 rounded-lg">
                <ShieldCheckIcon className="h-5 w-5 text-info" />
              </div>
              <div>
                <div className="text-2xl font-bold">{metrics.vulnerabilityChecks.toLocaleString()}</div>
                <div className="text-base text-base-content/70">Vulnerability Checks</div>
              </div>
            </div>
          </div>
        </div>

        {/* Total Vulnerabilities */}
        <div className="card bg-base-100 shadow-sm border border-base-300">
          <div className="card-body p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-warning/10 rounded-lg">
                <AlertTriangleIcon className="h-5 w-5 text-error" />
              </div>
              <div>
                <div className="text-2xl font-bold text-error">{metrics.findings.toLocaleString()}</div>
                <div className="text-base text-base-content/70">Vulnerabilities Found</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Target Analysis */}
      <div className="card bg-base-100 shadow-sm border border-base-300">
        <div className="card-body">
          <h3 className="text-lg font-semibold mb-4">Target Security Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-error mb-2">{metrics.targetAnalysis.vulnerableTargets.toLocaleString()}</div>
              <div className="text-sm text-base-content/70 mb-1">Vulnerable Targets</div>
              <div className="text-lg font-semibold text-error">{metrics.targetAnalysis.vulnerablePercentage}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-success mb-2">{metrics.targetAnalysis.cleanTargets.toLocaleString()}</div>
              <div className="text-sm text-base-content/70 mb-1">Clean Targets</div>
              <div className="text-lg font-semibold text-success">{metrics.targetAnalysis.cleanPercentage}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Vulnerable Targets List */}
      {showVulnerableTargets && scanStatus.vulnerable_targets && Object.keys(scanStatus.vulnerable_targets).length > 0 && (
        <VulnerableTargetsList vulnerableTargets={scanStatus.vulnerable_targets} totalVulnerableTargets={scanStatus.total_vulnerable_targets} loading={false} error={null} />
      )}

      {/* CVE Details (Demo) */}
      {scanStatus.vulnerable_targets && Object.keys(scanStatus.vulnerable_targets).length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">CVEs Discovered</h3>
          <CVEDetailsCard />
        </div>
      )}
    </div>
  );
};
