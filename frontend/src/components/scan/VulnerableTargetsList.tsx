import type { VulnerableTargetsListProps } from "@/features/scans/types";
import { calculateTableDisplayConfig, isValidVulnerableTargetsData, transformVulnerableTargets } from "@/features/scans/utils";
import { AlertTriangleIcon, RefreshCwIcon } from "lucide-react";
import type React from "react";
import { useMemo } from "react";

export const VulnerableTargetsList: React.FC<VulnerableTargetsListProps> = ({ vulnerableTargets, totalVulnerableTargets, loading = false, error = null }) => {
  // Transform and sort vulnerable targets data
  const transformedTargets = useMemo(() => {
    if (!isValidVulnerableTargetsData(vulnerableTargets) || Object.keys(vulnerableTargets).length === 0) {
      return [];
    }
    return transformVulnerableTargets(vulnerableTargets);
  }, [vulnerableTargets]);

  // Calculate table display configuration
  const displayConfig = useMemo(() => {
    return calculateTableDisplayConfig(transformedTargets, totalVulnerableTargets);
  }, [transformedTargets, totalVulnerableTargets]);

  // Loading state
  if (loading) {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Vulnerable Targets</h3>
        <div className="card bg-base-100 shadow-sm border border-base-300">
          <div className="card-body">
            <div className="flex items-center justify-center py-8">
              <span className="loading loading-spinner loading-md mr-3"></span>
              <span className="text-base-content/70">Loading vulnerable targets...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state with retry option
  if (error) {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Vulnerable Targets</h3>
        <div className="card bg-base-100 shadow-sm border border-base-300">
          <div className="card-body">
            <div className="text-center py-8">
              <div className="p-3 bg-error/10 rounded-lg inline-block mb-4">
                <AlertTriangleIcon className="h-6 w-6 text-error" />
              </div>
              <div className="text-error font-medium mb-2">Failed to load vulnerable targets</div>
              <div className="text-sm text-base-content/70 mb-4">{error}</div>
              <button type="button" className="btn btn-sm btn-outline" onClick={() => window.location.reload()}>
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Empty state
  if (transformedTargets.length === 0) {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Vulnerable Targets</h3>
        <div className="card bg-base-100 shadow-sm border border-base-300">
          <div className="card-body">
            <div className="text-center py-8 text-base-content/70">No vulnerable targets found</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <h3 className="text-lg font-semibold">Vulnerable Targets</h3>
      <div className="card bg-base-100 shadow-sm border border-base-300">
        <div className="card-body p-0">
          <div className="overflow-x-auto">
            <table className="table table-zebra">
              <thead>
                <tr>
                  <th>Target</th>
                  <th>Vulnerabilities</th>
                </tr>
              </thead>
              <tbody>
                {displayConfig.targetsToShow.map((targetData, index) => (
                  <tr key={`${targetData.target}-${index}`}>
                    <td>
                      <div className="font-mono text-sm break-all">{targetData.target}</div>
                    </td>
                    <td>
                      <div className="font-semibold">
                        {targetData.templateIds.length > 0 ? (
                          targetData.templateIds.map((templateId, index) => (
                            <span
                              key={`${templateId}-${
                                // biome-ignore lint/suspicious/noArrayIndexKey: <fine for now>
                                index
                              }`}
                              className="badge badge-sm badge-outline uppercase"
                            >
                              {templateId}
                            </span>
                          ))
                        ) : (
                          <span className="text-xs text-base-content/60 ml-1">(details pending)</span>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* "And X more" indicator when total exceeds display limit */}
            {displayConfig.hasMoreTargets && <div className="p-4 text-center text-sm text-base-content/70">{displayConfig.moreTargetsText}</div>}

            {/* "Top X of Y" message for lists with more than 10 items but under the limit */}
            {displayConfig.showTopMessage && <div className="p-4 text-center text-sm text-base-content/70">{displayConfig.topMessageText}</div>}
          </div>
        </div>
      </div>
    </div>
  );
};
