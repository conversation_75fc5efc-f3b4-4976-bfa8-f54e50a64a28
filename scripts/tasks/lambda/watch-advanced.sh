#!/usr/bin/env bash

# ==============================================================================
# Advanced Lambda Function Watcher Script (inotify-based)
# ==============================================================================
# This script uses inotify to watch for changes in the Lambda function code
# and automatically redeploys the function to LocalStack when changes are detected.
# This provides near-instant change detection compared to polling.

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly PROJECT_ROOT

source "${PROJECT_ROOT}/scripts/lib/logging.sh"
source "${PROJECT_ROOT}/scripts/lib/utils.sh"

readonly LAMBDA_DIR="$PROJECT_ROOT/lambda-nuclei-scanner"
DEBOUNCE_DELAY=1  # seconds to wait after last change before deploying

usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --debounce N     Set debounce delay in seconds (default: 1)"
    echo "  --test           Test Lambda function after each deployment"
    echo "  --fallback       Use polling fallback if inotify is not available"
    echo "  --help           Show this help message"
}

check_prerequisites() {
    log_header "Checking Prerequisites"
    require_tool "curl"
    
    if [ ! -d "$LAMBDA_DIR" ]; then
        fail "Lambda directory not found: $LAMBDA_DIR"
    fi
    
    if [ ! -f "${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh" ]; then
        fail "Deploy script not found: ${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh"
    fi
    
    if ! curl -s "http://localhost:4566/_localstack/health" | grep -q "available"; then
        fail "LocalStack is not running. Please start it with: docker-compose up -d localstack"
    fi
    
    if ! command -v inotifywait &> /dev/null; then
        log_warn "inotifywait not found. Install inotify-tools for better performance."
        log_warn "Falling back to polling mode or use --fallback flag."
        export USE_FALLBACK=true
    fi
    
    log_success "All prerequisites met"
}

deploy_lambda() {
    log_header "Building and Deploying Lambda Function"
    local deploy_script="${PROJECT_ROOT}/scripts/tasks/lambda/deploy.sh"
    local deploy_flags="--create-layers"

    if [ "${TEST_LAMBDA:-false}" = "true" ]; then
        deploy_flags="$deploy_flags --test"
    fi

    if bash "$deploy_script" $deploy_flags; then
        log_success "Lambda function deployed successfully"
    else
        log_error "Lambda function deployment failed"
    fi
}

watch_with_inotify() {
    log_header "Starting inotify-based Lambda Function Watcher"
    log_info "Watching for changes in: $LAMBDA_DIR"
    log_info "Debounce delay: ${DEBOUNCE_DELAY} seconds"
    log_info "Press Ctrl+C to stop watching"
    
    deploy_lambda # Initial deployment
    
    inotifywait -m -r -e modify,create,delete,move \
        --include '\.(go|yaml|yml|json)$' \
        "$LAMBDA_DIR" 2>/dev/null | while read -r directory events filename; do
        
        log_info "Change detected: $events $directory$filename"
        sleep "$DEBOUNCE_DELAY"
        deploy_lambda
        log_info "Watching for changes... (Ctrl+C to stop)"
    done
}

watch_with_polling() {
    log_header "Starting polling-based Lambda Function Watcher"
    log_info "Using fallback polling mode"
    exec bash "${PROJECT_ROOT}/scripts/tasks/lambda/watch.sh" --interval 2 ${TEST_LAMBDA:+--test}
}

main() {
    local USE_FALLBACK=${USE_FALLBACK:-false}
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --debounce) DEBOUNCE_DELAY=$2; shift 2 ;;
            --test) TEST_LAMBDA=true; shift ;;
            --fallback) USE_FALLBACK=true; shift ;;
            --help) usage; exit 0 ;;
            *) fail "Unknown option: $1" ;;
        esac
    done
    
    export TEST_LAMBDA=${TEST_LAMBDA:-false}
    export DEBOUNCE_DELAY
    
    trap 'echo -e "\n${YELLOW}[INFO]${NC} Advanced watcher stopped"; exit 0' INT
    
    check_prerequisites
    
    if [ "$USE_FALLBACK" = "true" ] || ! command -v inotifywait &> /dev/null; then
        watch_with_polling
    else
        watch_with_inotify
    fi
}

main "$@"