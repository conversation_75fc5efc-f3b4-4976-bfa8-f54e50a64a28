package config

import (
	"log"
	"nuclear_pond/pkg/aws"
	"os"
)

// Server<PERSON>he<PERSON> validates that the server is configured correctly.
func ServerCheck() {
	// Import AWS configuration to validate setup
	awsConfig := aws.NewAWSConfig()

	// Check required environment variables
	requiredEnvVars := map[string]string{
		"NUCLEI_TEMPLATES_BUCKET": "S3 bucket containing Nuclei templates metadata",
		"DEMO_PASSWORD":           "Demo password for authentication",
		"JWT_SECRET":              "Secret key for JWT token signing and validation",
	}

	for envVar, description := range requiredEnvVars {
		if value := os.Getenv(envVar); value == "" {
			if envVar == "GITHUB_TOKEN" || envVar == "GITHUB_REPOSITORY" {
				log.Printf("⚠️  %s not configured (%s) - PoW control will be disabled", envVar, description)
			} else {
				log.Fatalf("CRITICAL: %s environment variable must be set (%s)", envVar, description)
			}
		} else {
			log.Printf("✓ %s configured", envVar)
		}
	}

	// AWS configuration is now handled by the AWS config abstraction layer
	log.Printf("✓ AWS configuration initialized (Local: %t)", awsConfig.IsLocal)
}
